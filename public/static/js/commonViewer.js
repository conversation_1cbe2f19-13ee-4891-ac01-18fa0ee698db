/*
 * @Author: your name
 * @Date: 2020-02-16 02:43:00
 * @LastEditTime : 2020-02-19 19:17:21
 * @LastEditors  : Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \acmisui\static\js\commonViewer.js
 */
if (!window.context) {
  if (!window.location.origin) {
    location.origin = location.protocol + '//' + location.hostname + (location.port ? ':' + location.port : '')
  }
  window.context = location.origin + '/v6.0'
}

// 获取查询参数 ?fileUrl=...
function getQueryString(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return decodeURIComponent(r[2])
  return null
}

function IEVersion() {
  var userAgent = navigator.userAgent // 取得浏览器的userAgent字符串
  var isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 // 判断是否IE<11浏览器
  var isEdge = userAgent.indexOf('Edge') > -1 && !isIE // 判断是否IE的Edge浏览器
  var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
  if (isIE) {
    var reIE = new RegExp('MSIE (\\d+\\.\\d+);')
    reIE.test(userAgent)
    var fIEVersion = parseFloat(RegExp['$1'])
    if (fIEVersion === 7) {
      return 7
    } else if (fIEVersion === 8) {
      return 8
    } else if (fIEVersion === 9) {
      return 9
    } else if (fIEVersion === 10) {
      return 10
    } else {
      return 6 // IE版本<=7
    }
  } else if (isEdge) {
    return 'edge' // edge
  } else if (isIE11) {
    return 11 // IE11
  } else {
    return -1 // 不是ie浏览器
  }
}

var WEBSITE = '*'

var body = $('body')
// 为窗口绑定节点移除事件，节点移除时，向父窗口传递body的高度
var isChange = false
body.on('DOMNodeRemoved', function () {
  isChange = true
})

// 监听传递的消息,并根据传递过来的消息打开对应的窗口
addEvent(window, 'message', function (e) {
  var json = JSON.parse(e.data)
  switch (json.type) {
    case 'click':
      body.trigger('click.one')
      break
    case 'height':
      if (isChange) {
        isChange = false
        postHeight(body.height())
      } else {
        postHeight($(document).height())
      }
      break
    default:
      break
  }
})

// 绑定事件
function addEvent(obj, name, fn, async) {
  if (obj.addEventListener) {
    obj.addEventListener(name, fn, async)
  } else if (obj.attachEvent) {
    obj.attachEvent('on' + name, fn)
  } else {
    obj['on' + name] = fn
  }
}

// 向父窗口传递本页面内容高度
function postHeight(height) {
  parent.postMessage('{"type":"height", "goal":"' + window.name + '", "height": "' + height + '"}', WEBSITE)
}
