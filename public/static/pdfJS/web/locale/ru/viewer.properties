# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

previous.title = Предыдущая страница
previous_label = Предыдущая
next.title = Следующая страница
next_label = Следующая
page.title = Страница
of_pages = из {{pagesCount}}
page_of_pages = ({{pageNumber}} из {{pagesCount}})
zoom_out.title = Уменьшить
zoom_out_label = Уменьшить
zoom_in.title = Увеличить
zoom_in_label = Увеличить
zoom.title = Масштаб
presentation_mode.title = Перейти в режим презентации
presentation_mode_label = Режим презентации
open_file.title = Открыть файл
open_file_label = Открыть
print.title = Печать
print_label = Печать
download.title = Загрузить
download_label = Загрузить
bookmark.title = Ссылка на текущий вид (скопировать или открыть в новом окне)
bookmark_label = Текущий вид
tools.title = Инструменты
tools_label = Инструменты
first_page.title = Перейти на первую страницу
first_page.label = Перейти на первую страницу
first_page_label = Перейти на первую страницу
last_page.title = Перейти на последнюю страницу
last_page.label = Перейти на последнюю страницу
last_page_label = Перейти на последнюю страницу
page_rotate_cw.title = Повернуть по часовой стрелке
page_rotate_cw.label = Повернуть по часовой стрелке
page_rotate_cw_label = Повернуть по часовой стрелке
page_rotate_ccw.title = Повернуть против часовой стрелки
page_rotate_ccw.label = Повернуть против часовой стрелки
page_rotate_ccw_label = Повернуть против часовой стрелки
hand_tool_enable.title = Включить Инструмент «Рука»
hand_tool_enable_label = Включить Инструмент «Рука»
hand_tool_disable.title = Отключить Инструмент «Рука»
hand_tool_disable_label = Отключить Инструмент «Рука»
document_properties.title = Свойства документа…
document_properties_label = Свойства документа…
document_properties_file_name = Имя файла:
document_properties_file_size = Размер файла:
document_properties_kb = {{size_kb}} КБ ({{size_b}} байт)
document_properties_mb = {{size_mb}} МБ ({{size_b}} байт)
document_properties_title = Заголовок:
document_properties_author = Автор:
document_properties_subject = Тема:
document_properties_keywords = Ключевые слова:
document_properties_creation_date = Дата создания:
document_properties_modification_date = Дата изменения:
document_properties_date_string = {{date}}, {{time}}
document_properties_creator = Приложение:
document_properties_producer = Производитель PDF:
document_properties_version = Версия PDF:
document_properties_page_count = Число страниц:
document_properties_close = Закрыть
print_progress_message = Подготовка документа к печати…
print_progress_percent = {{progress}}%
print_progress_close = Отмена
toggle_sidebar.title = Показать/скрыть боковую панель
toggle_sidebar_notification.title = Показать/скрыть боковую панель (документ имеет содержание/вложения)
toggle_sidebar_label = Показать/скрыть боковую панель
document_outline.title = Показать содержание документа (двойной щелчок, чтобы развернуть/свернуть все элементы)
document_outline_label = Содержание документа
attachments.title = Показать вложения
attachments_label = Вложения
thumbs.title = Показать миниатюры
thumbs_label = Миниатюры
findbar.title = Найти в документе
findbar_label = Найти
thumb_page_title = Страница {{page}}
thumb_page_canvas = Миниатюра страницы {{page}}
find_label = Найти:
find_previous.title = Найти предыдущее вхождение фразы в текст
find_previous_label = Назад
find_next.title = Найти следующее вхождение фразы в текст
find_next_label = Далее
find_highlight = Подсветить все
find_match_case_label = С учётом регистра
find_reached_top = Достигнут верх документа, продолжено снизу
find_reached_bottom = Достигнут конец документа, продолжено сверху
find_not_found = Фраза не найдена
error_more_info = Детали
error_less_info = Скрыть детали
error_close = Закрыть
error_version_info = PDF.js v{{version}} (сборка: {{build}})
error_message = Сообщение: {{message}}
error_stack = Стeк: {{stack}}
error_file = Файл: {{file}}
error_line = Строка: {{line}}
rendering_error = При создании страницы произошла ошибка.
page_scale_width = По ширине страницы
page_scale_fit = По размеру страницы
page_scale_auto = Автоматически
page_scale_actual = Реальный размер
page_scale_percent = {{scale}}%
loading_error_indicator = Ошибка
loading_error = При загрузке PDF произошла ошибка.
invalid_file_error = Некорректный или повреждённый PDF-файл.
missing_file_error = PDF-файл отсутствует.
unexpected_response_error = Неожиданный ответ сервера.
text_annotation_type.alt = [Аннотация {{type}}]
password_label = Введите пароль, чтобы открыть этот PDF-файл.
password_invalid = Неверный пароль. Пожалуйста, попробуйте снова.
password_ok = OK
password_cancel = Отмена
printing_not_supported = Предупреждение: В этом браузере не полностью поддерживается печать.
printing_not_ready = Предупреждение: PDF не полностью загружен для печати.
web_fonts_disabled = Веб-шрифты отключены: невозможно использовать встроенные PDF-шрифты.
document_colors_not_allowed = PDF-документам не разрешено использовать свои цвета: в браузере отключён параметр «Разрешить веб-сайтам использовать свои цвета».
