<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <script type="text/javascript" src="../js/jquery.js"></script>
    <script type="text/javascript" src="../js/commonViewer.js"></script>
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }
      .container {
        width: 100%;
        height: 99%;
      }
    </style>
  </head>
  <body style="margin: 0">
    <div class="container">
      <div id="editor" style="position: relative; height: 100%; width: 100%; border: none; margin: auto; padding: 0">
        <script type="text/javascript" src="iWebOffice2009.js"></script>
      </div>

      <div id="statusBar"></div>
    </div>

    <script type="text/javascript">
      var WebOffice = document.getElementById('WebOffice')
      var fileUrl = getQueryString('file')
      var fileType = fileUrl.slice(fileUrl.lastIndexOf('.'))
      var fileName = fileUrl.slice(fileUrl.lastIndexOf('/') + 1, fileUrl.lastIndexOf('.'))

      $(document).ready(function () {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
          WebOffice.WebUrl = getQueryString('office')
          Load()
        } else {
          alert('文档查看仅支持IE，请使用IE浏览器查看。')
        }
      })

      function StatusMsg(msg) {
        $('statusBar').html(msg)
      }

      function Load() {
        try {
          //以下属性必须设置，实始化iWebOffice
          WebOffice.RecordID = fileUrl //RecordID:本文档记录编号
          WebOffice.FileName = fileName //FileName:文档名称
          WebOffice.FileType = fileType //FileType:文档类型  .doc  .xls  .wps
          // WebOffice.FileType = ".docx";                      //FileType:文档类型  .doc  .xls  .wps
          WebOffice.UserName = 'mingwang' //UserName:操作用户名，痕迹保留需要
          WebOffice.EditType = '0,0' //EditType:编辑类型  方式一、方式二  <参考技术文档>
          //第一位可以为0,1,2,3 其中:0不可编辑;1可以编辑,无痕迹;2可以编辑,有痕迹,不能修订;3可以编辑,有痕迹,能修订；
          //第二位可以为0,1 其中:0不可批注,1可以批注。可以参考iWebOffice2009的EditType属性，详细参考技术白皮书
          WebOffice.MaxFileSize = 4 * 1024 //最大的文档大小控制，默认是8M，现在设置成4M。
          WebOffice.Language = 'CH' //Language:多语言支持显示选择   CH简体 TW繁体 EN英文
          //WebOffice.ShowWindow = true;                  //控制显示打开或保存文档的进度窗口，默认不显示

          WebOffice.Print = '1' //Print:默认是否可以打印:1可以打印批注,0不可以打印批注
          WebOffice.ShowToolBar = '1' //ShowToolBar:是否显示工具栏:1显示,0不显示

          //以下为自定义工具栏按钮↓ 参数一:Index按钮编号,参数二:Caption按钮显示内容,参数三:Icon图标名称
          WebOffice.VisibleTools('新建文件', false)
          WebOffice.VisibleTools('打开文件', false)
          WebOffice.VisibleTools('保存文件', false)
          WebOffice.VisibleTools('文字批注', false)
          WebOffice.VisibleTools('文档清稿', false)
          WebOffice.VisibleTools('重新批注', false)
          WebOffice.VisibleTools('手写批注', false)

          WebOffice.VisibleTools('全屏', true)
          WebOffice.AppendTools('13', '打印文档', 13)

          WebOffice.ShowMenu = '0' //控制整体菜单显示

          //以上为自定义菜单↑
          WebOffice.DisableMenu('宏(&M);选项(&O)...') //禁止某个（些）菜单项

          WebOffice.WebOpen() //打开该文档    交互OfficeServer  调出文档OPTION="LOADFILE"    调出模板OPTION="LOADTEMPLATE"     <参考技术文档>

          StatusMsg(WebOffice.Status) //状态信息
        } catch (e) {
          alert(e.description) //显示出错误信息
        }
      }

      //作用：打印文档
      function WebOpenPrint() {
        try {
          WebOffice.WebOpenPrint()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
    </script>
    <script language="javascript" for="WebOffice" event="OnToolsClick(vIndex,vCaption)">
      if (vIndex == 13) {
        WebOpenPrint() //打印文档
      }
    </script>
  </body>
</html>
