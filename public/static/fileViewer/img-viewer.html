<!--
 * @Author: your name
 * @Date: 2020-02-14 14:20:49
 * @LastEditTime: 2020-02-16 14:31:41
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \acmisui\static\fileViewer\img-viewer.html
 -->
<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="../css/viewer.css" />
    <script type="text/javascript" src="../js/jquery.js"></script>
    <script type="text/javascript" src="../js/commonViewer.js"></script>
    <script type="text/javascript" src="../js/viewer.js"></script>
    <style>
      .image-container img:hover {
        cursor: pointer;
      }
    </style>
  </head>
  <body style="margin: 0px; border: none; padding: 0px; width: 100%">
    <script type="text/javascript">
      var fileUrl = getQueryString('file'),
        viewer = null

      var image = new Image()
      image.onload = function () {
        var divWidth = 900
        var divHeight = window.innerHeight - 60

        var imageWidth = image.width > 850 ? 850 : image.width
        divWidth = imageWidth

        var imageHeight = imageWidth * (image.height / image.width)
        if (divHeight > imageHeight) divHeight = imageHeight

        var content =
          "<div class='image-container' style='margin:auto;width:" +
          divWidth +
          'px;height:' +
          divHeight +
          "px;'> " +
          "<img style='display:block;margin:auto;width:" +
          imageWidth +
          'px;height:' +
          imageHeight +
          "px;' src='" +
          fileUrl +
          "'  />" +
          '</div>'

        $(document.body).append(content)
        viewer = new Viewer($('.image-container')[0])
        viewer.show(true)
      }

      image.src = fileUrl
    </script>
  </body>
</html>
