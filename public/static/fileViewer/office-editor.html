<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <script type="text/javascript" src="../js/jquery.js"></script>
    <script type="text/javascript" src="../js/commonViewer.js"></script>
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }
      .container {
        width: 100%;
        height: 99%;
      }
    </style>
  </head>
  <body style="margin: 0">
    <div class="container">
      <div id="editor" style="position: relative; height: 100%; width: 100%; border: none; margin: auto; padding: 0">
        <script type="text/javascript" src="iWebOffice2009.js"></script>
      </div>

      <div id="statusBar"></div>
    </div>

    <div id="buttonArea" style="display: none">
      <button>保存pdf</button>
    </div>
    <script type="text/javascript">
      var height = $(window).innerHeight()
      $('#editor').height(height - 8)
      var WebOffice = document.getElementById('WebOffice')
      var reloadGoal = getQueryString('reloadGoal')
      var fileUrl = getQueryString('file')
      var nativeFileType = fileUrl.slice(fileUrl.lastIndexOf('.'))
      var fileType = nativeFileType
      var fileName = fileUrl.slice(fileUrl.lastIndexOf('/') + 1, fileUrl.lastIndexOf('.'))
      if (nativeFileType === '.pdf') {
        fileType = '.docx'
        fileUrl = fileUrl.slice(0, fileUrl.lastIndexOf('.')) + fileType
      }

      $(document).ready(function () {
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
          WebOffice.WebUrl = getQueryString('office')
          Load()
        } else {
          alert('文档查看仅支持IE，请使用IE浏览器查看。')
        }
      })

      function StatusMsg(msg) {
        $('statusBar').html(msg)
      }

      function Load() {
        try {
          //以下属性必须设置，实始化iWebOffice
          WebOffice.RecordID = fileUrl //RecordID:本文档记录编号
          WebOffice.FileName = fileName //FileName:文档名称
          WebOffice.Compatible = false
          WebOffice.FileType = fileType //FileType:文档类型  .docx .doc  .xls  .wps
          //WebOffice.UserName="coolman";                   //UserName:操作用户名，痕迹保留需要
          if (nativeFileType === '.pdf') {
            WebOffice.EXTPARAM = true //保存时是否需要直接转换为pdf(金格额外参数)
          }
          WebOffice.EditType = '1,0' //EditType:编辑类型  方式一、方式二  <参考技术文档>
          //第一位可以为0,1,2,3 其中:0不可编辑;1可以编辑,无痕迹;2可以编辑,有痕迹,不能修订;3可以编辑,有痕迹,能修订；
          //第二位可以为0,1 其中:0不可批注,1可以批注。可以参考iWebOffice2009的EditType属性，详细参考技术白皮书
          WebOffice.MaxFileSize = 4 * 1024 //最大的文档大小控制，默认是8M，现在设置成4M。
          WebOffice.Language = 'CH' //Language:多语言支持显示选择   CH简体 TW繁体 EN英文

          WebOffice.Print = '1' //Print:默认是否可以打印:1可以打印批注,0不可以打印批注
          WebOffice.ShowToolBar = '1' //ShowToolBar:是否显示工具栏:1显示,0不显示

          //以下为自定义工具栏按钮↓ 参数一:Index按钮编号,参数二:Caption按钮显示内容,参数三:Icon图标名称
          WebOffice.VisibleTools('新建文件', false)
          WebOffice.VisibleTools('打开文件', false)
          WebOffice.VisibleTools('保存文件', false)
          WebOffice.VisibleTools('文字批注', false)
          WebOffice.VisibleTools('文档清稿', false)
          WebOffice.VisibleTools('重新批注', false)
          WebOffice.VisibleTools('手写批注', false)

          WebOffice.VisibleTools('全屏', true)
          WebOffice.AppendTools('5', '保存为PDF', 5)
          WebOffice.AppendTools('13', '打印文档', 13)
          WebOffice.AppendTools('2', '本地保存', 2)
          WebOffice.AppendTools('1', '本地打开', 1)
          WebOffice.AppendTools('3', '服务器保存', 9)

          //以上为自定义工具栏按钮↑

          WebOffice.ShowMenu = '0' //控制整体菜单显示
          //WebSetRibbonUIXML();                          //控制OFFICE2007的选项卡显示
          WebOffice.WebOpen() //打开该文档    交互OfficeServer  调出文档OPTION="LOADFILE"    调出模板OPTION="LOADTEMPLATE"     <参考技术文档>
          StatusMsg(WebOffice.Status) //状态信息
        } catch (e) {
          alert(e.description) //显示出错误信息
        }
      }
      //作用：打开本地文件
      function WebOpenLocal() {
        try {
          WebOffice.WebOpenLocal()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //作用：存为本地文件
      function WebSaveLocal() {
        try {
          WebOffice.WebSaveLocal()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //作用：保存文档
      function SaveDocument() {
        //WebOffice.WebSetMsgByName("MyDefine1","自定义变量值1");  //设置变量MyDefine1="自定义变量值1"，变量可以设置多个  在WebSave()时，一起提交到OfficeServer中
        if (!WebOffice.WebSave(true)) {
          //交互OfficeServer的OPTION="SAVEFILE"  注：WebSave()是保存复合格式文件，包括OFFICE内容和手写批注文档；如只保存成OFFICE文档格式，那么就设WebSave(true)
          StatusMsg(WebOffice.Status)
          return false
        } else {
          StatusMsg(WebOffice.Status)
          return true
        }
      }
      //作用：打印文档
      function WebOpenPrint() {
        try {
          WebOffice.WebOpenPrint()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //保存文档为PDF
      function SaveAsPdf() {
        try {
          WebOffice.WebSavePDF()
        } catch (e) {
          alert(e.description)
        }
      }
    </script>
    <script language="javascript" for="WebOffice" event="OnToolsClick(vIndex,vCaption)">
      if (vIndex == 1) {
        WebOpenLocal() //打开本地文件
      }
      if (vIndex == 2) {
        WebSaveLocal() //保存本地文件
      }
      if (vIndex == 3) {
        SaveDocument() //保存正文到服务器上（不退出）
      }
      if (vIndex == 13) {
        SaveDocument()
        WebOpenPrint() //打印文档
      }
      if (vIndex == 5) {
        if (SaveDocument()) {
          SaveAsPdf()
          var jsonObj = { type: 'reload', reloadGoal: reloadGoal }
          parent.postMessage(JSON.stringify(jsonObj), parent.WEBSITE)
        }
      }
    </script>
  </body>
</html>
