<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <script type="text/javascript" src="../js/jquery.js"></script>
    <style type="text/css">
      .webOfficeBtn {
        background: #d0eeff;
        background: #d0eeff;
        border: 1px solid #99d3f5;
        border-radius: 4px;
        padding: 4px 12px;
        overflow: hidden;
        color: #1e88c7;
        text-decoration: none;
        text-indent: 0;
        line-height: 20px;
        margin: 2px 6px;
        height: 28px;
      }
    </style>
  </head>
  <body style="margin: 0px; border: none; padding: 0px">
    <div id="editor" style="position: relative; width: 100%; border: none; margin: auto; padding: 0px">
      <script type="text/javascript" src="iWebOffice2009.js"></script>
    </div>
    <div id="buttonArea" style="height: 32px; padding-top: 5px">
      <button class="prev webOfficeBtn">上一份</button>
      <button class="next webOfficeBtn">下一份</button>
      <button class="serverSave webOfficeBtn">保存</button>
      <button class="saveAsPdf webOfficeBtn">保存为PDF</button>
      <button class="print webOfficeBtn">打印文档</button>
      <button class="localSave webOfficeBtn">保存到本地</button>
      <button class="localOpen webOfficeBtn">打开文件</button>
    </div>
    <script type="text/javascript">
      $('.localOpen').on('click', function () {
        WebOpenLocal()
      })
      $('.localSave').on('click', function () {
        WebSaveLocal()
      })
      $('.serverSave').on('click', function () {
        SaveDocument()
      })
      $('.print').on('click', function () {
        SaveDocument()
        WebOpenPrint()
      })
      $('.saveAsPdf').on('click', function () {
        if (SaveDocument()) {
          SaveAsPdf()
          var jsonObj = { type: 'reload', reloadGoal: reloadGoal }
          console.log(jsonObj)
          parent.postMessage(JSON.stringify(jsonObj), parent.WEBSITE)
        }
      })
      $('.prev').on('click', function () {
        SaveDocument()
        if (parseInt(CURR_INDEX) > 0 && parseInt(CURR_INDEX) <= FILE_LIST.length) {
          CURR_INDEX--
          FILE_OBJECT = FILE_LIST[CURR_INDEX]
          Load()
        }
      })
      $('.next').on('click', function () {
        SaveDocument()
        if (parseInt(CURR_INDEX) >= 0 && parseInt(CURR_INDEX) < FILE_LIST.length - 1) {
          CURR_INDEX++
          FILE_OBJECT = FILE_LIST[CURR_INDEX]
          Load()
        }
      })
    </script>
    <script type="text/javascript">
      var height = window.outerHeight
      document.getElementById('editor').style.height = height - 240 + 'px'
      var WebOffice = document.getElementById('WebOffice')

      // 获取查询参数 ?fileUrl=...
      function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
      }
      var FILE_LIST = []
      var CURR_INDEX = 0
      var FILE_OBJECT = {}

      var fileUrls = getQueryString('fileUrls')
      var operateType = getQueryString('operateType')
      var savedFun = getQueryString('savedFun')
      var reloadGoal = getQueryString('reloadGoal')

      operateType = decodeURI(decodeURI(operateType))
      fileUrls = fileUrls.substring(0, fileUrls.lastIndexOf(','))
      fileUrls = decodeURI(decodeURI(fileUrls))
      var arrFileUrl = fileUrls.split('\,')
      if (arrFileUrl && arrFileUrl.length > 0) {
        for (var i = 0; i < arrFileUrl.length; i++) {
          var reg = new RegExp('/', 'g')
          var fileName = arrFileUrl[i].replace(reg, '-')
          FILE_LIST.push({ fileUrl: arrFileUrl[i], fileName: fileName })
        }
        FILE_OBJECT = FILE_LIST[CURR_INDEX]
      }

      $(document).ready(function () {
        var pathname = window.location.pathname
        var appName = '/arb-mis'
        var index = pathname.indexOf('/', 1)
        appName = pathname.substring(0, index)
        WebOffice.WebUrl = location.protocol + '//' + location.host + appName + '/weboffice2009'
        if (operateType == 'saveAll') {
          //parent.window.showMask();
          try {
            $('#editor').css('height', '1px')
            $('#editor').css('height', '1px')

            for (var k = 0; k < FILE_LIST.length; k++) {
              FILE_OBJECT = FILE_LIST[k]
              Load()
              SaveAsPdf()
            }
          } catch (e) {
          } finally {
            if (!savedFun) {
              window.parent.window.location.reload()
            } else {
              window.parent.window[savedFun]()
            }
          }
        } else {
          Load()
        }
      })

      function StatusMsg(msg) {
        $('statusBar').html(msg)
      }

      function Load() {
        try {
          //以下属性必须设置，实始化iWebOffice
          WebOffice.RecordID = FILE_OBJECT.fileUrl //RecordID:本文档记录编号
          WebOffice.FileName = FILE_OBJECT.fileName //FileName:文档名称
          WebOffice.Compatible = false
          WebOffice.FileType = '.docx' //FileType:文档类型  .doc  .xls  .wps
          //WebOffice.UserName="coolman";                   //UserName:操作用户名，痕迹保留需要
          WebOffice.EditType = '1,0' //EditType:编辑类型  方式一、方式二  <参考技术文档>
          //第一位可以为0,1,2,3 其中:0不可编辑;1可以编辑,无痕迹;2可以编辑,有痕迹,不能修订;3可以编辑,有痕迹,能修订；
          //第二位可以为0,1 其中:0不可批注,1可以批注。可以参考iWebOffice2009的EditType属性，详细参考技术白皮书
          WebOffice.MaxFileSize = 4 * 1024 //最大的文档大小控制，默认是8M，现在设置成4M。
          WebOffice.Language = 'CH' //Language:多语言支持显示选择   CH简体 TW繁体 EN英文

          WebOffice.Print = '1' //Print:默认是否可以打印:1可以打印批注,0不可以打印批注
          WebOffice.ShowToolBar = '0' //ShowToolBar:是否显示工具栏:1显示,0不显示

          //以上为自定义工具栏按钮↑

          WebOffice.ShowMenu = '0' //控制整体菜单显示
          //WebSetRibbonUIXML();                          //控制OFFICE2007的选项卡显示
          WebOffice.WebOpen() //打开该文档    交互OfficeServer  调出文档OPTION="LOADFILE"    调出模板OPTION="LOADTEMPLATE"     <参考技术文档>
          StatusMsg(WebOffice.Status) //状态信息
        } catch (e) {
          alert(e.description) //显示出错误信息
        }
      }
      //作用：打开本地文件
      function WebOpenLocal() {
        try {
          WebOffice.WebOpenLocal()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //作用：存为本地文件
      function WebSaveLocal() {
        try {
          WebOffice.WebSaveLocal()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //作用：保存文档
      function SaveDocument() {
        //WebOffice.WebSetMsgByName("MyDefine1","自定义变量值1");  //设置变量MyDefine1="自定义变量值1"，变量可以设置多个  在WebSave()时，一起提交到OfficeServer中
        if (!WebOffice.WebSave(true)) {
          //交互OfficeServer的OPTION="SAVEFILE"  注：WebSave()是保存复合格式文件，包括OFFICE内容和手写批注文档；如只保存成OFFICE文档格式，那么就设WebSave(true)
          StatusMsg(WebOffice.Status)
          return false
        } else {
          StatusMsg(WebOffice.Status)
          return true
        }
      }
      //作用：打印文档
      function WebOpenPrint() {
        try {
          WebOffice.WebOpenPrint()
          StatusMsg(WebOffice.Status)
        } catch (e) {
          alert(e.description)
        }
      }
      //保存文档为PDF
      function SaveAsPdf() {
        try {
          WebOffice.WebSavePDF()
        } catch (e) {
          alert(e.description)
        }
      }
    </script>
  </body>
</html>
