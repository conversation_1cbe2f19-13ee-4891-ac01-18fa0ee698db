import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>load, <PERSON><PERSON><PERSON><PERSON>, El<PERSON>ableColumn } from 'element-plus/lib/components/index.js'
import type { FormItemRule } from 'element-plus'

declare global {
  type ELEMENTPLUS_ELTREE_TYPE = InstanceType<typeof ElTree>
  type ELEMENTPLUS_ELFORM_TYPE = InstanceType<typeof ElForm>
  type ELEMENTPLUS_ELTABLE_TYPE = InstanceType<typeof ElTable>
  type ELEMENTPLUS_ELUPLOAD_TYPE = InstanceType<typeof ElUpload>
  type ELEMENTPLUS_ELDIALOG_TYPE = InstanceType<typeof ElDialog>
  type ELEMENTPLUS_ELTABLECOLUMN_TYPE = InstanceType<typeof ElTableColumn>

  type ELFORM_PROPS_TYPE = ELEMENTPLUS_ELFORM_TYPE['props']
  type ELEMENTPLUS_FORMITEMRULE_TYPE = Partial<FormItemRule>
  type ELEMENTPLUS_ELTABLECOLUMN_PROP_TYPE = ELEMENTPLUS_ELTABLECOLUMN_TYPE['$props']
  type ELFORM_VALIDATEFIELD_TYPE = Parameters<ELEMENTPLUS_ELFORM_TYPE['validateField']>
}
