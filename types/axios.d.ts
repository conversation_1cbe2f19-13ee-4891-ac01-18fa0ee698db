import axios from 'axios'

declare module 'axios' {
  export interface AxiosRequestConfig {
    hideLoading?: boolean
  }
  export interface AxiosResponse {
    token?: string
  }
  // export interface AxiosInstance {
  //   request<T>(config: AxiosRequestConfig): Promise<T>
  //   get<T>(url: string, config?: AxiosRequestConfig): Promise<T>
  //   delete<T>(url: string, config?: AxiosRequestConfig): Promise<T>
  //   head<T>(url: string, config?: AxiosRequestConfig): Promise<T>
  //   post<T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<T>
  //   put<T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<T>
  //   patch<T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<T>
  // }
}
