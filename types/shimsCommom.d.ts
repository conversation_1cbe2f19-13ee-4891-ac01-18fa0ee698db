declare type ARR_T_TYPE<T> = T[]

declare type ARR_NUMBER_TYPE = number[]

declare type ARR_STRING_TYPE = string[]

declare type OBJ_KEY_STR_T_TYPE<T> = { [x: string]: T }

declare type OBJ_KEY_STR_ANY_TYPE = { [x: string]: any }

declare type OBJ_KEY_STR_NUM_TYPE = { [x: string]: number }

declare type OBJ_KEY_STR_STR_TYPE = { [x: string]: string }

declare type OBJ_KEY_NUM_STR_TYPE = { [x: number]: string }

declare type OBJ_KEY_NUM_NUM_TYPE = { [x: number]: number }

declare type OBJ_KEY_T_ANY_TYPE<T> = { [x in keyof T]?: any }

declare type OBJ_KEY_T_T_TYPE<T> = { -readonly [x in keyof T]?: T[x] }

declare type OPERATION_STATE_TYPE = 'add' | 'edit' | 'view'

declare type METHOD_ENUM_TYPE = 'get' | 'post' | 'put' | 'deletes'

declare type OPERATION_STATE_T_TYPE<T> = { add: T; edit: T; view: T }

declare type OPTIONS_TYPE = { value: string | number | boolean; label: string }[]

declare type HTTP_RESPONSE<T> = { code: number; message: string; result: T }

declare type DROPDOWN_LIST_TYPE = { value: string | number; label: string | number; icon?: string }[]
