interface Window {
  ActiveXObject: OBJ_KEY_STR_ANY_TYPE
  existLoading: boolean
  tccc: any

  // 微信注入 https://developers.weixin.qq.com/miniprogram/dev/component/web-view.html
  __wxjs_environment: string
  wx?: {
    miniProgram: {
      navigateBack: Function
      navigateTo: Function
      redirectTo: Function
      switchTab: Function
      reLaunch: Function
      postMessage: Function
      getEnv: Function
    }
  }
}

interface Navigator {
  msSaveBlob: (blob: Blob, fileName: string) => {}
}

interface Document {
  createEventObject: () => any
}

interface ImportMeta {
  url: string

  readonly hot?: import('./hot').ViteHotContext

  readonly env: ImportMetaEnv

  glob<Module = { [key: string]: any }>(pattern: string, options?: GlobOptions): Record<string, () => Promise<Module>>

  globEager<Module = { [key: string]: any }>(pattern: string, options?: GlobOptions): Record<string, Module>
}

interface ImportMetaEnv {
  [key: string]: any
  BASE_URL: string
  MODE: string
  DEV: boolean
  PROD: boolean
  SSR: boolean
}
