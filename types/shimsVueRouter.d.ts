declare module 'vue-router' {
  interface RouteMeta {
    title?: string // 页面标题 | String
    noLogin?: boolean // 进入该页面无需登录 | Boolean
    multipage?: string // 指示一个路由参数，以该路由参数的值为差异生成不同的页面tab | String
    isTab?: boolean // 页面加入顶栏标签列表 | Boolean

    // arco
    roles?: string[] // 角色列表
    requiresAuth?: boolean // 是否需要权限
    icon?: string // 图标
    locale?: string // 国际化
    hideInMenu?: boolean // 是否隐藏
    hideChildrenInMenu?: boolean // 是否隐藏菜单栏按钮
    activeMenu?: string // 选中按钮
    order?: number // 排序
    noAffix?: boolean // 是否显示标签 如果设置为true，标签将不会粘贴在标签栏中
    keepAlive?: boolean // 缓存
  }
}

export {}
