<template>
  <div class="app-footer">
    <mdFooter :color="color"></mdFooter>
  </div>
</template>

<script lang="ts">
import mdFooter from '@arco/components/footer/index.vue'
import variables from '@scss/var.module.scss'

export default {
  name: 'Copyright',
  components: { mdFooter },
  computed: {
    color() {
      return variables['colorWeak']
    }
  }
}
</script>

<style lang="scss" scoped>
.app-footer {
  @include h-v-c;
  margin: 0 auto;
  width: 100%;
  height: $--normal-footer-height;
  flex-shrink: 0;
}
</style>
