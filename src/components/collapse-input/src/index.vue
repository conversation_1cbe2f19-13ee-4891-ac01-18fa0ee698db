<template>
  <a-popover trigger="click" content-class="collapse-popover-box" @popup-visible-change="handleChange">
    <div class="collapse-popover-textarea">
      <a-input v-model="model" :readonly="visible" placeholder="请输入内容" allow-clear />
    </div>
    <template #content>
      <a-textarea ref="textareaRef" v-model="model" :auto-size="{ minRows: 3 }" placeholder="请输入内容" allow-clear />
    </template>
  </a-popover>
</template>
<script lang="ts" setup>
import { type PopoverInstance, type TextareaInstance } from '@arco-design/web-vue'
import { ref, nextTick } from 'vue'

const model = defineModel({ default: '', required: true, type: String })

const textareaRef = ref<TextareaInstance>()
const visible = ref(false)

const handleChange: PopoverInstance['onPopupVisibleChange'] = (state) => {
  visible.value = state
  nextTick(() => {
    console.log(textareaRef.value)
    textareaRef.value?.focus()
  })
}
</script>
<style lang="scss" scoped>
.collapse-popover-box {
  padding: 10px;
}
</style>
