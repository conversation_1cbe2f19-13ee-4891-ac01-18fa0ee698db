<template>
  <a-range-picker
    v-bind="$attrs"
    v-model="dateRange"
  />
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 使用 defineModel 定义双向绑定
const startValue = defineModel<string | undefined>('startValue')
const endValue = defineModel<string | undefined>('endValue')

// 计算日期范围数组 - 支持回显
const dateRange = computed({
  get() {
    if (startValue.value && endValue.value) {
      return [startValue.value, endValue.value]
    }
    return []
  },
  set(dates: string[]) {
    if (dates && dates.length === 2) {
      startValue.value = dates[0]
      endValue.value = dates[1]
    } else {
      startValue.value = undefined
      endValue.value = undefined
    }
  }
})


</script>
