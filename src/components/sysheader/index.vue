<template>
  <div class="sys">
    <a-image :src="sysLogo" class="sys-logo" :preview="false">
      <template #error> <img :src="noLogoImg" class="sys-logo" /></template>
    </a-image>
    <div class="sys-name" :style="sysnameStyle">{{ logoTitle }}</div>
  </div>
</template>

<script lang="ts">
import { getSysInfo, readLogoPath } from '@/api/commonApi/vis'
import { useAppStore } from '@/layouts/appArco/store'
import noLogoImg from '@/assets/images/zd_logo.png'
import { getSysCompany } from '@/api/eleApi/home'
import { getImgFilePath } from '@/utils/proto'
import variables from '@scss/var.module.scss'

const appStore = useAppStore()

export default {
  name: 'SysHeader',
  props: {
    sysnameColor: { type: String, default: variables.colorDark },
    page: { type: String, default: 'login' },
    logoLink: {
      type: Object,
      default: () => {
        return { name: 'home' }
      }
    }
  },
  data() {
    return {
      platformTitle: '',
      noLogoImg
    }
  },
  computed: {
    sysnameStyle() {
      return { color: this.sysnameColor }
    },
    sysLogo() {
      let brandPath = appStore.loginPageConfig?.brandPath
      if (brandPath) {
        return getImgFilePath(brandPath)
      }
      return readLogoPath()
    },
    logoTitle() {
      let brandTitle = appStore.loginPageConfig?.brandTitle
      if (brandTitle) {
        // 重新设置网站标题
        document.title = appStore.loginPageConfig?.tabName || this.platformTitle
        // 重新设置favicon图标
        if(appStore.loginPageConfig?.tabPath) {
          const link = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
          link.href = getImgFilePath(appStore.loginPageConfig.tabPath)
        }
        return brandTitle
      } else {
        document.title = this.platformTitle
        return this.platformTitle
      }
    }
  },
  created() {
    this.page === 'login' ? this.getSysInfo() : this.getSysCompany()
  },
  methods: {
    getSysInfo() {
      getSysInfo().then(({ sctName, twoFactorAuthentication }) => {
        appStore.systemParam.twoFactorAuthentication = twoFactorAuthentication ? Number(twoFactorAuthentication) : 0
        this.platformTitle = sctName
      })
    },
    getSysCompany() {
      return getSysCompany().then(({ companyName }) => {
        this.platformTitle = companyName
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$textColor: #303030;
$logoSize: 48px;
.sys {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-logo {
    display: inline-block;
    width: $logoSize;
    height: $logoSize;
    line-height: $logoSize;
    :deep(.arco-image-img) {
      width: $logoSize;
      height: $logoSize;
      line-height: $logoSize;
    }
  }
  &-name {
    margin-left: 14px;
    color: $textColor;
    font-size: 20px;
    font-weight: bold;
    font-family: '华文中宋';
    letter-spacing: 2px;
  }
}
</style>
