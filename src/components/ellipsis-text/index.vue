<template>
  <span>
    <a-tooltip v-if="needEllipsis" :content="text">
      <span>
        {{ displayedText }}
      </span>
    </a-tooltip>
    <template v-else>
      {{ displayedText }}
    </template>
  </span>
</template>
<script setup lang="ts">

import { computed } from 'vue'

const {text,maxLength}= defineProps({
	text:{
		type:String,
		required:true
	},
	maxLength:{
		type:Number
	}
})



const displayedText = computed(()=>{
	if(!maxLength){
		//没有限制
		return text
	}
	if(text?.length>maxLength){
		return text.slice(0,maxLength)+'...';
	}

	return text;
})

const needEllipsis = computed(()=>{
	return displayedText.value?.length != text?.length
})

</script>
<style scoped lang="scss">

</style>
