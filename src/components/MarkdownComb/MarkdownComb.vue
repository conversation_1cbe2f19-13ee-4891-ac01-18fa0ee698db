<template>
  <a-layout>
    <!-- TOC 侧边栏 -->
    <a-layout-sider v-if="tocItems && tocItems.length>0" :id="anchorId" :collapsed="false">
      <a-anchor :change-hash="false" :scroll-container="'#'+contentId">
        <AnchorLinkeTree :items="tocItems" :slugify="slugify" />
      </a-anchor>
    </a-layout-sider>

    <!-- 内容区域 -->
    <a-layout-content :id="contentId" :style="{ padding: '20px' }" @scroll="handleContentScroll">
      <div v-html="parsedMarkdownIt" />
    </a-layout-content>
  </a-layout>
</template>

<script setup>
import { ref, computed, useTemplateRef } from 'vue'
import MarkdownIt from 'markdown-it';
import MarkdownItAnchor from 'markdown-it-anchor';
import MarkdownItToc from 'markdown-it-toc-done-right';
import { nanoid } from 'nanoid';
import AnchorLinkeTree from '@/components/MarkdownComb/AnchorLinkeTree.vue'


// 定义 props 并设定默认值
const props = defineProps({
	markdownContent: {
		type: String,
		default: '# 默认标题\n\n这是默认的 Markdown 内容。'
	}
});

// 生成唯一实例ID
const instanceId = ref(nanoid(4));
const contentId = computed(() => `md-content-${instanceId.value}`);


// 内容滚动时，滚动到对应锚点
const anchorId = computed(() => `md-anchor-${instanceId.value}`);
// 处理内容区域滚动事件
const handleContentScroll = (event) => {
	let target = event.target
	const anchorElement = document.querySelector(`#${anchorId.value} .arco-layout-sider-children`);
	//被arco 控制的绿色指示条
	const lineSliderEl = document.querySelector(`#${anchorId.value} .arco-anchor-line-slider`);
	if (anchorElement) {
		if (target.scrollTop==0){
			anchorElement.scrollTop = 0
		}else{
			anchorElement.scrollTop = lineSliderEl.offsetTop
		}
	}
};

// 配置带唯一ID的slugify函数
const slugify = (s) => {
	const r = encodeURIComponent(
		String(s).trim().toLowerCase().replace(/\s+/g, '-')
	);
	return `${instanceId.value}-${r}`;
};

const itemClick = (name) => {
	let elementId = slugify(name)
	const element = document.getElementById(elementId);
	if (element) {
		element.scrollIntoView({ behavior: 'smooth' });
	}
};



// TOC树形结构
const tocItems = ref([])


// 初始化Markdown处理器
const md = new MarkdownIt().use(MarkdownItAnchor, {
	slugify}).use( MarkdownItToc,
	{
		callback: (html , ast ) => {
			tocItems.value = ast.c
		}
	});


// 计算属性，将 Markdown 内容解析为 HTML
const parsedMarkdownIt = computed(() => {
	return md.render(props.markdownContent);
});





</script>

<style scoped>

</style>
