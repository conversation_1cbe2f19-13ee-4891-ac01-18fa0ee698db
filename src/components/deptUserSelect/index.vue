<template>
  <a-tree-select
    v-model="valueModel"
    :data="deptEmployeeTree"
    :allow-search="true"
    :multiple="multiple"
    allow-clear
    selectable="leaf"
    :filter-tree-node="filterTreeNode"
  />
</template>

<script setup lang="ts">
import { useStructStore } from '@arco/store'
import { computed } from 'vue'

const structStore = useStructStore()

//请求api，已请求过就不请求了
structStore.refreshDeptEmployeeoOlyNull()

const valueModel = defineModel<number | number[] | null | string>()

const { allowNull = false, multiple = false } = defineProps<{
  allowNull?: boolean
  multiple?: boolean
}>()

const deptEmployeeTree = computed(()=>{
	let newTree = [...structStore.orgDeptTree];
	if (allowNull){
		newTree.unshift({
			title:"空",
			key:-1,
			value:-1
		})
	}

	return newTree;
})

//查询时的规则
const filterTreeNode = (searchValue, nodeData) => {
	return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1
}
</script>

<style scoped lang="scss"></style>
