<template>
	<a-cascader
    v-model="valueModel"
    :options="dictDataTree"
    :multiple="props.multiple"
    :allow-search="true"
		:check-strictly="checkStrictly"
    allow-clear
    placeholder="请选择"
    @change="(value) => emits('selected',value)"
  />
</template>

<script setup lang="ts">
import { useDictStore } from '@arco/store'
import { ref, watch } from 'vue'
import { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'
import { DictTypeEnum } from '@/dict/systemManage.ts'

const dictStore = useDictStore()


const valueModel = defineModel()

const props = withDefaults( defineProps<{
	dictType: DictTypeEnum,
	//严格模式
	checkStrictly?:boolean,
	//自动增加一个空值
	appendNullValue?: boolean,
	multiple?: boolean,
	//用哪个值作为modleValue,tag,id,key
	modelValueWitch?: 'key' | 'tag' | 'id',
	//筛选对应的业务类型
	groupByBusinessType?:boolean,
	businessType?:string|number,
	filterBusinessTypes?:string[],
	//允许忽略一些key
	ignoreKeys?:[]
}>(),{
	appendNullValue:false,
	checkStrictly:false,
	multiple:false,
	useTagForKey:false,
	modelValueWitch:'key',
	groupByBusinessType:false,
	filterBusinessTypes: ()=>[]
})

const emits = defineEmits(['selected']);

// 树形数据
const dictDataTree = ref<CascaderOption[]>([])

const buildTreeData = function(list:REQUEST_GET_DICT_VALUE_LIST_TYPE[], businessType:number|string): CascaderOption[] {

	let newTree: CascaderOption[] = []

	for (let dictData of list) {
		// 如果指定了业务类型，则只显示对应业务类型的数据
		if (props.businessType && dictData.businessType && dictData.businessType !== businessType) {
			continue
		}
		//如果在ignore列表中就跳过
		if (props.ignoreKeys && dictData.dictKey in props.ignoreKeys) {
			continue;
		}

		let children: CascaderOption[] = []

		//子孙存在就构建
		if (dictData.children && dictData.children.length > 0) {
			children = buildTreeData(dictData.children,businessType)
		}

		let treeKey:string|number = dictData.dictKey;

		if (props.modelValueWitch === 'tag') {
			treeKey = dictData.dictTag
		}else if (props.modelValueWitch === 'id') {
			treeKey = dictData.dictDataId
		}

		const tag = dictData['dictTag']
		newTree.push({
			label: tag,
			value: treeKey,
			children: children.length==0?undefined:children,
			disabled: dictData['enableFlag']==0
		})
	}

	return newTree
}


//根据业务类型分组的tree
const buildGroupTreeData = function(list:REQUEST_GET_DICT_VALUE_LIST_TYPE[]): CascaderOption[] {


	let map = new Map<string,REQUEST_GET_DICT_VALUE_LIST_TYPE[]>()

	let businessTypeMap = new Map<string,string|undefined>()

	for (let dictData of list) {
		let businessType = dictData.businessType+"";

		if (props.filterBusinessTypes?.length && (!props.filterBusinessTypes.map(String).includes(String(businessType)) )){
			continue;
		}


		if (!businessType){
			businessType = "无";
		}
		if (!map.has(businessType)){
			map.set(businessType, [])
		}
		map.get(businessType)!.push(dictData)
		businessTypeMap.set(businessType,dictData.businessTypeName)
	}


	let map1 = Array.from(map.entries()).map(([businessType,list]) => {

		return {
			children: buildTreeData(list,businessType),
			value: businessType,
			label: businessTypeMap.get(businessType) || "无"
		}
	})
	return map1
}



//监听dictId改变，以及数据源更新
watch([dictStore,()=>props.dictType,()=>props.businessType,()=>props.filterBusinessTypes],async ()=>{
	if(!props.dictType){
		return
	}
	let dictDataList = await dictStore.listTreeDictDataByType(props.dictType)

	if(!dictDataList){
		return
	}

	let newTree: CascaderOption[] = props.groupByBusinessType? buildGroupTreeData(dictDataList) :buildTreeData(dictDataList,props.businessType!)

	// 如果指定了业务类型，直接过滤
	if (props.businessType) {

	}

	if (props.appendNullValue) {
		newTree.unshift({
			label: '空',
			value: -1
			// value: -1
		})
	}

	dictDataTree.value = newTree
},{
	immediate:true
})

//暴漏当前的树数据
defineExpose({
	dictDataTree:dictDataTree
})
</script>

<style scoped lang="scss"></style>
