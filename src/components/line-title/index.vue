<template>
  <div class="line-title">
    <div class="line-title__line"></div>
    <div class="line-title__txt"><slot></slot></div>
  </div>
</template>

<script lang="ts">
export default { name: 'LineTitle' }
</script>

<style lang="scss">
.line-title {
  position: relative;
  height: 24px;
  margin-bottom: 36px;
  &__line {
    position: absolute;
    top: 50%;
    width: 100%;
    // width: 92px;
    height: 1px;
    background-color: #d9d9d9;
  }
  &__txt {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 0 12px;
    font-size: 24px;
    line-height: 1;
    font-weight: bold;
    color: var(--gray-1);
    letter-spacing: 1px;
    background-color: var(--color-bg-2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
