<template>
  <a-cascader
    v-model="cascaderVal"
    :options="cascaderOptions"
    :style="{ width: '100%' }"
    :load-more="loadMore"
    placeholder="请选择"
    allow-search
    allow-clear
  />
</template>

<script lang="ts" setup>
import { getOrg, getDept, getMediator } from '@/api/arcoApi/caseManage/caseManage'
import type { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'
import { ref } from 'vue'

const [cascaderVal] = defineModel('value', { default: '', required: true, type: String })

const cascaderOptions = ref<CascaderOption[]>([])
const loadMore = (option, done) => {
  window.setTimeout(async () => {
    let nodes: CascaderOption[] = []
    if (option.value && option.type === 'org') {
      nodes = await getMediationTeamList(option.value)
    } else if (option.value && option.type === 'dept') {
      nodes = await getMediationStaffList(option.value)
    }
    done(nodes)
  }, 1000)
}

const getMediationStaffList = async (deptId: string | number) => {
  let mediatorRes = await getMediator([deptId])
  if (mediatorRes?.length) {
    return mediatorRes.map((item) => ({ label: item.employeeName, value: item.accountId, type: 'staff', isLeaf: true }))
  }
  return []
}

const getMediationTeamList = async (orgId: string | number) => {
  let deptRes = await getDept([orgId])
  if (deptRes?.length) {
    return deptRes.map((item) => ({ label: item.deptName, value: item.deptId, type: 'dept', isLeaf: false }))
  }
  return []
}

const getMediationOrganizationList = async () => {
  let orgRes = await getOrg()
  if (orgRes?.length) {
    cascaderOptions.value = orgRes.map((item) => ({
      label: item.orgName,
      value: item.orgId,
      type: 'org',
      isLeaf: false
    }))
  }
  return []
}

getMediationOrganizationList()
</script>
