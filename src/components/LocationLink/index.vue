<template>
  <div class="location-link-container">
    <a 
      v-if="linkProps.href" 
      :href="linkProps.href" 
      target="_blank"
      class="location-link"
      :title="`点击在百度地图中查看位置: ${linkProps.text}`"
    >
      <icon-location />
      {{ linkProps.text }}
    </a>
    <span v-else class="no-location" :title="linkProps.text">
      <icon-location />
      {{ linkProps.text }}
    </span>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { getLocationLinkProps } from '@/utils/locationLink'

interface Props {
  longitude?: string | number
  latitude?: string | number
  title?: string
  content?: string
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  longitude: '',
  latitude: '',
  title: '登录位置',
  content: '上次登录',
  showIcon: true
})

const linkProps = computed(() => {
  return getLocationLinkProps(props.longitude, props.latitude, props.title, props.content)
})
</script>

<style scoped lang="scss">
.location-link-container {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.location-link {
  color: #1890ff;
  text-decoration: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
  
  &:visited {
    color: #722ed1;
  }
}

.no-location {
  color: #999;
  font-style: italic;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
</style>
