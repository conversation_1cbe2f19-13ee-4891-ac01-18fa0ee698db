<template>
  <a-cascader :options="options"
              v-model.number="selectedValue"
              :default-value="modelValue"
              :placeholder="props.placeholder"
              :check-strictly="props.checkStrictly"
              :expand-trigger="props.expandTrigger"
              :multiple="props.multiple"
              :disabled="disabled"
              :allow-clear="props.allowClear"
              @change="changeValue" />
</template>
<script setup lang="ts" name="BusinessTypeSelect">
  import { ref,watch } from 'vue'
  import { defineProps,defineEmits } from 'vue';
  import { businessTypeTree } from '@/api/arcoApi/systemManage/businessTypeManage.ts'
  const props = defineProps({
    modelValue: { type: [Array, String, Number,null], default: () => [] }, // 支持 Array/String/Number
    placeholder: { type: String, default: '请选择' },
    checkStrictly: { type: Boolean, default: true }, // 是否严格模式（可单选任意级）
    expandTrigger: { type: String, default: 'hover' }, // 展开方式：hover/click
    multiple: { type: Boolean, default: false }, // 是否多选
    disabled: {type:Boolean,default: false },
    allowClear: {type:Boolean,default: true }
  });
  let emit = defineEmits(['update:modelValue','change'])
  const selectedValue = ref(props.modelValue);


  const changeValue = (value: any) => {
    // 从路径缓存中获取包含该value的所有父级路径数组
    const ancestorPath = pathCache.value.get(value) || [];
    emit('update:modelValue', value);
    emit('change', value, ancestorPath);
  }

  const options = ref<any>([]);
  // 节点缓存，用于快速查找 value -> node 映射
  const nodeCache = ref<Map<string | number, any>>(new Map());
  // 路径缓存，用于快速查找 value -> 父级路径数组 映射
  const pathCache = ref<Map<string | number, (string | number)[]>>(new Map());


  watch(
    () => props.modelValue,
    (newVal) => {
      selectedValue.value = newVal;
    }
  );

  function transformData(input: any[], parentPath: (string | number)[] = []): any[] {
    return input.map(item => {
      const currentPath = [...parentPath, item.businessTypeId];

      const transformedNode = {
        value: item.businessTypeId,
        label: item.businessTypeName,
        children: item.children && item.children.length > 0
          ? transformData(item.children, currentPath)
          : undefined
      };

      // 在转换数据的同时构建缓存
      nodeCache.value.set(item.businessTypeId, transformedNode);
      // 构建路径缓存，存储完整的祖先路径（包括自己）
      pathCache.value.set(item.businessTypeId, currentPath);

      return transformedNode;
    });
  }

  businessTypeTree().then((res:any)=>{
    options.value = transformData(res);
  })

</script>

<style scoped lang="scss">

</style>
