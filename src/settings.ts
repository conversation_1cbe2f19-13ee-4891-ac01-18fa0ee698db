export const sysName = 'mediate' // 网站标识符

export const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZrizglLM2tQ7R+zHQTy6YhmIvCrNztn7y8ahLd8/YBpiy9bSHo/iAvwOtcGN3BAD+YqFhUyKlY0q4rAubzhX7ShBC70t/s9q07L267rWr/9ZW8cDFvBFeoyySkmaZT4KDIwvpKHBzy9LQ1UJ+AlL98OxtvoDwIuVawYtwE23eYwIDAQAB'
export const sysBaseOfMobile = `${sysName}-mobile`
export const TokenKeyOfHeader = 'auth-token' // 请求头的 key
export const tokenLocalStorageExpires = 3 // 记住密码状态下的token在LocalStorage中存储的天数
export const passLocalStorageExpires = 3 // 记住密码状态下的密码在LocalStorage中存储的天数
export const ThemeKey = `${sysName}-theme` // 主题色 key
export const TokenKey = `${sysName}Token` // localStorage 存储的key
export const tcccDomID = 'tccc-anonymous' // 腾讯云外呼sdk引入标识
export const DictKey = `${sysName}Dict` // localStorage 存储的key
export const TabKey = `${sysName}Tabs` // 记录一打开 tabList的localStorage 存储的key
export const multipleToken = true // 是否保存多个token
export const title = `多元调解系统`
export const timeout = 600000 // 请求超时时间，毫秒（默认10分钟）
export const sysBase = sysName // url base

export default {
  tokenLocalStorageExpires,
  passLocalStorageExpires,
  TokenKeyOfHeader, // 请求头的 key
  sysBaseOfMobile,
  multipleToken, // 是否保存多个token
  publicKey,
  tcccDomID,
  ThemeKey, // 主题色 key
  sysName,
  TokenKey, // localStorage 存储的key
  sysBase, // url base
  DictKey, // localStorage 存放 dict的key
  timeout,
  TabKey, //
  title
}
