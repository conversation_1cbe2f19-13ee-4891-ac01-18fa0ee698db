export const usageModeOptions = [
  { label: '呼入', value: '呼入' },
  { label: '呼出', value: '呼出' }
]

export const receiveTypeOptions = [
  { label: '本地导入', value: '1' },
  { label: '线上系统接收', value: '2' }
]

export const createResultOptions = [
  { label: '创建成功', value: '1' },
  { label: '创建失败', value: '2' }
]

export const phoneCheckStatusOptions = [
  { label: '检测成功', value: '0' },
  { label: '未查询到结果', value: '1' },
  { label: '手机号码格式不正确', value: '2' },
  { label: '验证中心服务繁忙', value: '3' }
]

export const repairNetOptions = [
  { label: '全网', value: '1' },
  { label: '异网', value: '2' },
  { label: '电信', value: '3' },
  { label: '云宝宝', value: '4' }
]

export const phoneStatusOptions = [
  { label: '正常', value: '0' },
  { label: '停机', value: '1' },
  { label: '销号', value: '2' },
  { label: '空号', value: '3' },
  { label: '不在网', value: '4' },
  { label: '未知状态', value: '5' },
  { label: '号码缺失', value: '6' },
  { label: '号码未检测', value: '7' },
  { label: '关机', value: '8' }
]

export const meetingStatusOptions = [
  { label: '未开始', value: '1' },
  { label: '进行中', value: '2' },
  { label: '已结束', value: '3' },
  { label: '已过期', value: '4' }
]

export const sendMessageOptions = [
  { label: '发送成功', value: 'DELIVERED' },
  { label: '发送失败', value: 'FAIL' }
]

export const sendTypeOptions = [
  { label: '单条发送', value: '0' },
  { label: '批量发送', value: '1' }
]

// 案件详情-调解记录
export const repairStatusOptions = [
  { label: '待修复', value: '1' },
  { label: '修复中', value: '2' },
  { label: '修复完成', value: '3' }
]

// 作业日志-调解记录
export const repairStateOptions = [
  { label: '待修复', value: '1' },
  { label: '修复中', value: '2' },
  { label: '修复成功', value: '3' },
  { label: '修复失败', value: '4' }
]

export const repairResultOptions = [
  { label: '修复成功', value: '修复成功' },
  { label: '修复失败', value: '修复失败' }
]


export default {
  phoneCheckStatusOptions,
  meetingStatusOptions,
  repairResultOptions,
  createResultOptions,
  repairStatusOptions,
  repairStateOptions,
  phoneStatusOptions,
  receiveTypeOptions,
  sendMessageOptions,
  repairNetOptions,
  usageModeOptions,
  sendTypeOptions
}
