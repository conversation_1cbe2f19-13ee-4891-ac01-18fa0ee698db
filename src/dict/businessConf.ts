// export const entrustsTypeOptions = [
//   { label: '法院', value: '1' },
//   { label: '司法局', value: '2' }
// ]

export const entrustsTypeOptions = [
  { label: '法院', value: '1' },
  { label: '司法局', value: '2' }
]

export const multipleRecordOptions = [
  { label: '单条记录', value: 0 },
  { label: '多条记录', value: 1 }
]

export const tmplTypeOptions = [
  { label: '案件基础模板', value: '1' },
  { label: '案件导入模板', value: '2' },
  { label: '案件导出模板', value: '4' },
  { label: '案件更新模板', value: '3' }
]

export const statusOptions = [
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
]

export const fieldDataTypeOptions = [
  { label: '数字', value: 'number' },
  { label: '文本', value: 'varchar' },
  { label: '日期', value: 'datetime' },
  { label: '小数', value: 'decimal' },
  { label: '字典', value: 'dict' },
  { label: '系统字典', value: 'system_dict' }
]

export const isValidatedOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

export const isRequireOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

export const isApprovalValidatedOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

export const buildOrgTypeOptions = [
  { label: '国家机关', value: '1' },
  { label: '社会团体', value: '2' },
  { label: '企业机构', value: '3' },
  { label: '事业机构', value: '4' }
]

export const mapMethodsOptions = [
  { label: '单记录映射', value: 0 },
  { label: '多记录映射', value: 1 }
]

export const docTmplTypeOptions = [
  { label: '独立生成', value: '1' },
  { label: '合并生成', value: '2' }
]

export const createMethodsOptions = [
  { label: '模板生成', value: '1' },
  { label: '本地上传', value: '2' },
  { label: '调解笔录生成', value: '3' }
]

export const componentOptions = [
  { label: '文本输入框', value: 'AInput' },
  { label: '单选下拉框', value: 'ASelect' },
  { label: '多选下拉框', value: 'ASelectMultiple' },
  { label: '日期控件', value: 'ADatePicker' },
  { label: '时间控件', value: 'ADateTimePicker' },
  { label: '数字输入框', value: 'AInputNumber' },
  { label: '多行文本框', value: 'ATextarea' }
]

export const componentMapObject = {
  ADateTimePicker: 'ADatePicker',
  AInputNumber: 'AInputNumber',
  ASelectMultiple: 'ASelect',
  ADatePicker: 'ADatePicker',
  ATextarea: 'ATextarea',
  ASelect: 'ASelect',
  AInput: 'AInput'
}

export const componentMapModals = {
  ASelectMultiple: [],
  ADateTimePicker: [],
  AInputNumber: 0,
  ADatePicker: '',
  ATextarea: '',
  ASelect: '',
  AInput: ''
}

export default {
  isApprovalValidatedOptions,
  multipleRecordOptions,
  createMethodsOptions,
  fieldDataTypeOptions,
  buildOrgTypeOptions,
  entrustsTypeOptions,
  docTmplTypeOptions,
  isValidatedOptions,
  mapMethodsOptions,
  componentOptions,
  isRequireOptions,
  tmplTypeOptions,
  statusOptions
}
