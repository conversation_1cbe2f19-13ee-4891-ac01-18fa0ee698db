export const mdtCaseStatusOptions = [
	{ label: '待分派', value: '30' },
	{ label: '处理中', value: '35' },
	{ label: '已办结', value: '40' }
]

export const mediateResultOptions = [
	{ label: '调解成功', value: '1' },
	{ label: '调解终止', value: '2' },
	{ label: '调解中止', value: '3' }
]

export const litigantTypeOptions = [
	{ label: '自然人', value: '1' },
	{ label: '法人', value: '2' }
]

export const idCardTypeOptions = [
	{ label: '身份证', value: '1' },
	{ label: '社会统一代码', value: '2' }
]

export const mediateTypeOptions = [
	{ label: '电话调解', value: '1' },
	{ label: '视频调解', value: '2' },
	{ label: '企业微信调解', value: '3' },
	{ label: '线下调解', value: '4' }
]



export const litigantPhoneStatusOptions = [
	{ label: '正常', value: '0' },
	{ label: '停机', value: '1' },
	{ label: '销号', value: '2' },
	{ label: '空号', value: '3' },
	{ label: '不在网', value: '4' },
	{ label: '未知状态', value: '5' },
	{ label: '号码缺失', value: '6' },
	{ label: '号码未检测', value: '7' },
	{ label: '关机', value: '8' }
]

export const isAnswerOptions = [
	{ label: '是', value: '1' },
	{ label: '否', value: '0' }
]

export const sexOptions = [
	{ label: '男', value: '1' },
	{ label: '女', value: '0' }
]

export const isPrivateEnterpriseOptions = [
	{ label: '是', value: '1' },
	{ label: '否', value: '0' }
]

export const auditStatusOptions = [
	{ label: '待审核', value: '1' },
	{ label: '审核中', value: '5' },
	{ label: '审核完成', value: '10' }
]

const approvalResultOptions = [
	{ label: '通过', value: '1' },
	{ label: '失败', value: '5' }
]

export const approvalTypeOptions = [
	{ label: '调解办结', value: '1' },
	{ label: '延期审批', value: '2' },
	{ label: '案管办结', value: '3' }
]

export const signStatusOptions = [
	{ label: '发起签名', value: '1' },
	{ label: '签名中', value: '2' },
	{ label: '签名成功', value: '3' },
	{ label: '签名失败', value: '4' },
	{ label: '签名终止', value: '5' }
]

export const dateConfigTypeOptions = [
	{ label: '模板导入', value: '1' },
	{ label: '页面设置', value: '2' }
]



export const taskTypeList = [
	{ type: '送达', status: 'deliveredTaskStatus', user: 'deliveredTaskAccountId', userName: 'deliveredTaskAccountName' },
	{ type: '触达', status: 'reachTaskStatus', user: 'reachTaskAccountId', userName: 'reachTaskAccountName' },
	{ type: '失联修复', status: 'repairTaskStatus', user: 'repairTaskAccountId', userName: 'repairTaskAccountName' },
	{
		type: '排期',
		status: 'schedulingTaskStatus',
		user: 'schedulingTaskAccountId',
		userName: 'schedulingTaskAccountName'
	},
	{
		type: '审评鉴',
		status: 'evaluationTaskStatus',
		user: 'evaluationTaskAccountId',
		userName: 'evaluationTaskAccountName'
	},
	{ type: '结案', status: 'closedTaskStatus', user: 'closedTaskAccountId', userName: 'closedTaskAccountName' }
]

export const businessType = [
	{ label: '政务', value: '1' },
	{ label: '金融', value: '2' }
]


export const caseMediateTypeOptions = [
	{ label: '先行调解', value: '1' },
	{ label: '诉前调解', value: '2' },
	{ label: '非诉调解', value: '3' },
	{ label: '诉保调解', value: '4' },
	{ label: '诉中委托', value: '5' }
]

export default {
	litigantPhoneStatusOptions,
	isPrivateEnterpriseOptions,
	caseMediateTypeOptions,
	dateConfigTypeOptions,
	approvalResultOptions,
	mdtCaseStatusOptions,
	mediateResultOptions,
	approvalTypeOptions,
	litigantTypeOptions,
	mediateTypeOptions,
	auditStatusOptions,
	idCardTypeOptions,
	signStatusOptions,
	isAnswerOptions,
	taskTypeList,
	businessType,
	sexOptions
}
