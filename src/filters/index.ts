import type { App } from 'vue'

function toDecimal(val: number, digit = 2) {
  if (isNaN(val)) {
    return 0
  }
  var num = Math.round(val * Math.pow(10, digit)) / Math.pow(10, digit)
  if (num % 1 === 0) {
    return num.toString()
  } else {
    return num.toFixed(digit)
  }
}

export function genderCn(val: string) {
  return val === '1' ? '男' : '女'
}

export function datetimeFilter(val: null | string, fmt = 'yyyy-MM-dd hh:mm', emptyDesc = '') {
  if (val !== null && val !== '') {
    let date = new Date(val)
    let o: { [x: string]: number } = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds()
    }
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (let k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k].toString() : ('00' + o[k]).substr(('' + o[k]).length)
        )
      }
    }
    return fmt
  } else {
    return emptyDesc
  }
}

export function decimal(val: number, digit = 2) {
  if (isNaN(val)) {
    return 0
  }
  var num = Math.round(val * Math.pow(10, digit)) / Math.pow(10, digit)
  if (num % 1 === 0) {
    return num.toString()
  } else {
    return num.toFixed(digit)
  }
}

export function _toCurrencyString(val: number, digit = 2) {
  if (isNaN(val)) {
    return ''
  }
  let num = toDecimal(val, digit)
  return num.toString().replace(/\d(?=(?:\d{3})+\b)/g, '$&,')
}

const filters = { toDecimal, genderCn, datetimeFilter, decimal, _toCurrencyString }

export default {
  install(app: App) {
    for (const [key, func] of Object.entries(filters)) {
      app.config.globalProperties[key] = func
    }
  }
}
