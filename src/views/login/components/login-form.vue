<template>
  <div class="login-form">
    <LineTitle>{{ loginBoxTitle }}</LineTitle>
    <a-form
      v-if="!showWechat && !showAlipay"
      ref="loginFormRef"
      :model="loginForm"
      size="large"
      :rules="loginRules"
      :label-col-props="{ span: 0 }"
      :wrapper-col-props="{ span: 24 }"
    >
      <a-form-item field="loginUname" class="is-no-asterisk">
        <a-input v-model="loginForm.loginUname" type="text" auto-complete="off" placeholder="账号">
          <template #prefix> <icon-user /> </template>
        </a-input>
      </a-form-item>
      <a-form-item field="loginPwd" class="is-no-asterisk">
        <a-input-password v-model="loginForm.loginPwd" placeholder="密码" @keyup="handlePswKeyup">
          <template #prefix><icon-lock /></template>
        </a-input-password>
      </a-form-item>
      <a-form-item v-if="twoFactorAuthentication" field="smsCode" class="is-no-asterisk">
        <div class="validate-code-box">
          <a-input v-model="loginForm.smsCode" type="text" class="validate-input" placeholder="验证码"> </a-input>
          <div class="validate-code-img" @click="getMobileValidateCode">
            {{ handleBtnText }}
          </div>
        </div>
      </a-form-item>

      <!-- 定位状态显示 -->
<!--      <div v-if="isGettingLocation || locationInfo" class="location-status">-->
<!--        <div v-if="isGettingLocation" class="location-loading">-->
<!--          <icon-loading /> 正在获取位置信息...-->
<!--        </div>-->
<!--        <div v-else-if="locationInfo && locationInfo.success" class="location-success">-->
<!--          <icon-check-circle /> 位置获取成功-->
<!--        </div>-->
<!--        <div v-else-if="locationInfo && !locationInfo.success" class="location-error">-->
<!--          <icon-exclamation-circle /> 位置获取失败-->
<!--        </div>-->
<!--      </div>-->

      <a-form-item class="login-btn-contain">
        <a-button :loading="loading" class="login-btn" type="primary" @click.prevent="handleLogin">
          <span v-if="!loading"> <span style="margin-right: 18px">登</span> 录 </span>
          <span v-else>登&nbsp;录&nbsp;中...</span>
        </a-button>
      </a-form-item>
    </a-form>
    <a-modal
      v-model:visible="verifyVisible"
      title="图片验证码"
      width="350px"
      @close="handleCancel"
      @cancel="handleCancel"
      @ok="handleConfirm"
    >
      <div class="verify-content">
        <a-input v-model="verifySms.verifyValue" placeholder="请输入图片验证码">
          <template #append>
            <img :src="verifySms.imageBase64Str" class="verify-img" @click="changeVerify" />
          </template>
        </a-input>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { setLocalStorage, getLocalStorage, deleteLocalStorage, setToken } from '@/utils/auth'
import { login, getCaptchaInfo, sendSmsCodeByUserName } from '@/api/commonApi/login'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import LineTitle from '@/components/line-title/index.vue'
import { useAppStore } from '@/layouts/appArco/store'
import { getCurrentLocation, type LocationResult } from '@/utils/geolocation'
import Config from '@/settings'

import bgImg from '@/assets/images/bg.png'

const appStore = useAppStore()

export default {
  name: 'LoginForm',
  components: { LineTitle },
  emits: ['loginSuccess'],
  data() {
    let initData: LOGIN_FORM_INITDATA_TYPE = {
      loginForm: {
        rememberMe: false,
        loginUname: '',
        loginPwd: '',
        smsCode: '',
        code: '',
        uuid: ''
      },
      loginRules: {
        loginUname: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        smsCode: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
        loginPwd: [{ required: true, trigger: 'blur', message: '密码不能为空' }]
      },
      verifySms: {
        imageBase64Str: '',
        verifyValue: '',
        verifyKey: ''
      },
      verifyVisible: false,
      // 滑块验证码
      silderText: '向右滑动',
      dialogVisible: false,
      validatePass: false,
      isFirstClick: true,
      showAlipay: false,
      showWechat: false,
      loading: false,
      localStoragePass: '',
      validateTimer: null,
      redirectUri: '',
      validateTime: 0,
      bgimgs: [bgImg],
      redirect: '',
      codeUrl: '',
      appid: '',
      // 定位相关
      locationInfo: null as LocationResult | null,
      isGettingLocation: false
    }
    return initData
  },
  computed: {
    handleBtnText() {
      if (this.validateTime === 0 && this.isFirstClick) {
        return '获取验证码'
      } else if (this.validateTime === 0 && !this.isFirstClick) {
        return '重新获取'
      } else {
        return `${this.validateTime < 10 ? '0' : ''}${this.validateTime}S`
      }
    },
    loginBoxTitle() {
      let boxtitle = appStore.loginPageConfig?.loginBoxTitle
      return boxtitle ? boxtitle : '登录'
    },
    twoFactorAuthentication() {
      return appStore.systemParam.twoFactorAuthentication === 1
    },
    loginFormRef() {
      let loginformRef = this.$refs['loginFormRef'] as FormInstance
      return loginformRef
    }
  },
  created() {
    this.getLocalStorage()
    this.initLocationService()
  },
  unmounted() {
    if (this.validateTimer) clearTimeout(this.validateTimer)
  },
  methods: {
    // 初始化定位服务
    async initLocationService() {
      try {
        // 获取当前位置
        await this.getCurrentLocationInfo()
      } catch (error) {
        console.error('定位服务初始化失败:', error)
      }
    },

    // 获取当前位置信息
    async getCurrentLocationInfo() {
      if (this.isGettingLocation) return

      this.isGettingLocation = true
      try {
        const location = await getCurrentLocation()
        this.locationInfo = location

        if (location.success) {
          console.log('定位成功:', location)
        } else {
          console.warn('定位失败:', location.message)
        }
      } catch (error) {
        console.error('获取位置信息失败:', error)
        this.locationInfo = {
          latitude: 0,
          longitude: 0,
          source: 'browser',
          success: false,
          message: '获取位置信息失败'
        }
      } finally {
        this.isGettingLocation = false
      }
    },

    getLocalStorage() {
      const rememberMe = getLocalStorage(`${Config.sysName}-rememberMe`) || false
      const loginUname = getLocalStorage(`${Config.sysName}-loginUname`) || ''
      let loginPwd = getLocalStorage(`${Config.sysName}-loginPwd`) || ''
      // 保存localStorage里面的加密后的密码
      this.localStoragePass = loginPwd === undefined ? '' : loginPwd
      loginPwd = loginPwd === undefined ? this.loginForm.loginPwd : loginPwd
      this.loginForm = {
        loginUname: loginUname === undefined ? this.loginForm.loginUname : loginUname,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        loginPwd: loginPwd,
        code: ''
      }
    },
    handlePswKeyup(e: KeyboardEvent) {
      if (e.keyCode == 13) this.handleLogin()
    },
    async handleLogin() {
      if (this.loginFormRef) {
        this.loginFormRef.validate(async (valid) => {
          const user = {
            loginUname: this.loginForm.loginUname,
            rememberMe: this.loginForm.rememberMe,
            smsCode: this.loginForm.smsCode || '',
            loginPwd: this.loginForm.loginPwd,
            loginSys: Config.sysName,
            // 添加定位信息
            longitude: this.locationInfo?.success ? this.locationInfo.longitude : undefined,
            latitude: this.locationInfo?.success ? this.locationInfo.latitude : undefined,
          }
          // if (this.loginForm.loginPwd !== this.localStoragePass) {
          //   user.loginPwd = encrypt(user.loginPwd, Config.publicKey);
          // }
          if (!valid) {
            this.loading = true
            if (user.rememberMe) {
              setLocalStorage(`${Config.sysName}-loginUname`, user.loginUname, Config.passLocalStorageExpires + 'd')
              setLocalStorage(`${Config.sysName}-loginPwd`, user.loginPwd, Config.passLocalStorageExpires + 'd')
              setLocalStorage(`${Config.sysName}-rememberMe`, user.rememberMe, Config.passLocalStorageExpires + 'd')
            } else {
              deleteLocalStorage(`${Config.sysName}-loginUname`)
              deleteLocalStorage(`${Config.sysName}-loginPwd`)
              deleteLocalStorage(`${Config.sysName}-rememberMe`)
            }
            this.login(user)
          }
        })
      }
    },
    // 登录http
    login(userInfo: LOGIN_USER_FORM_DATA) {
      login(userInfo)
        .then(({ token }) => {
          setLocalStorage('loginName', userInfo.loginUname)
          this.$emit('loginSuccess')
          setToken(token)
        })
        .finally(() => {
          this.loading = false
        })
    },
    changeVerify() {
      getCaptchaInfo({ loginName: this.loginForm.loginUname }).then((res) => {
        this.verifySms.imageBase64Str = `data:image/png;base64,${res.imageBase64Str}` || ''
        this.verifySms.verifyKey = res.verifyKey || ''
        this.verifyVisible = true
      })
    },
    handleCancel() {
      this.verifySms = { imageBase64Str: '', verifyKey: '', verifyValue: '' }
      this.verifyVisible = false
    },
    handleConfirm() {
      if (!this.verifySms.verifyValue) {
        this.acWarningMess('请输入图片验证码')
        return false
      }
      this.sendSmsCodeByUserNameEvent()
    },
    sendSmsCodeByUserNameEvent() {
      let { loginForm, verifySms } = this
      sendSmsCodeByUserName({
        loginName: loginForm.loginUname,
        captcha: verifySms.verifyValue,
        verifyKey: verifySms.verifyKey
      }).then(() => {
        this.acSuccessMess('手机验证码已发送')
        this.sendPhoneValidate()
        this.handleCancel()
      })
    },
    sendPhoneValidate() {
      this.isFirstClick = false
      if (this.validateTimer) clearInterval(this.validateTimer)
      this.validateTime = 60
      let _this = this
      _this.validateTimer = setInterval(() => {
        _this.validateTime--
        if (_this.validateTime === 0) {
          if (_this.validateTimer) clearInterval(_this.validateTimer)
        }
      }, 1000)
    },
    async getMobileValidateCode() {
      if (this.validateTime === 0) {
        const errField = await this.loginFormRef?.validateField('loginUname')
        if (!errField) {
          if (this.validatePass) {
            this.dialogVisible = true
          } else {
            this.changeVerify()
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$color-login-anchor: rgb(144, 144, 144);
$font-size-login: 16px;
$font-size-label: $--font-size-base;
.login-form {
  margin: 0 auto;
  width: 332px;
  &__foot {
    text-align: center;
  }
  .login-btn {
    width: 100%;
    background-color: #3a9189;
  }
  .register-com-btn {
    width: 160px;
  }
  .canca-btn {
    margin-right: 22px 0 0 !important;
  }
}
</style>
<style lang="scss" scoped>
$form-radius: 4px;
.login {
  .login-btn {
    height: 40px;
    font-size: 16px;
    &-contain {
      height: 40px;
    }
  }
  .a-checkbox__label {
    font-size: $--font-size-base;
  }
}
.validate-code-box {
  display: flex;
  width: 100%;
  .validate-input {
    flex: 1;
  }
  .validate-code-img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    text-align: center;
    font-size: 15px;
    font-weight: 600;
    color: #8787b9;
    cursor: pointer;
  }
}

.verify-img {
  width: 120px;
  padding: 2px;
}

.slide-verify-dialog {
  .a-dialog__header {
    display: none;
    padding: 0;
  }
}

.location-status {
  margin-bottom: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;

  .location-loading {
    color: #1890ff;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .location-success {
    color: #52c41a;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .location-error {
    color: #ff4d4f;
    display: flex;
    align-items: center;
    gap: 6px;
  }
}
</style>
