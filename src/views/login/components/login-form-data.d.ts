type LOGIN_FORM_DATA_TYPE = {
  rememberMe: boolean
  loginUname: string
  loginPwd: string
  smsCode?: string
  uuid?: string
  code: string
}

type LOGIN_USER_FORM_DATA = {
  loginUname: string
  loginPwd: string
  smsCode: string
  rememberMe: boolean
  loginSys: string
  longitude?: number
  latitude?: number
}

type LOGIN_FORM_INITDATA_TYPE = {
  loginRules: { [x: string]: { required: boolean; trigger: string; message: string }[] }
  validateTimer: NodeJS.Timeout | null
  loginForm: LOGIN_FORM_DATA_TYPE
  localStoragePass: string
  bgimgs: ARR_STRING_TYPE
  dialogVisible: boolean
  verifyVisible: boolean
  validatePass: boolean
  isFirstClick: boolean
  validateTime: number
  redirectUri: string
  showAlipay: boolean
  showWechat: boolean
  silderText: string
  loading: boolean
  verifySms: {
    imageBase64Str: string
    verifyValue: string
    verifyKey: string
  }
  redirect: string
  codeUrl: string
  appid: string
}
