<template>
  <div ref="loginPageRef" class="login">
    <header class="login-head">
      <SysHeader :sysname-color="themeColor"></SysHeader>
    </header>
    <main class="login-main">
      <div class="login-main__contain">
        <LoginForm @login-success="loginSuccessHanlder"></LoginForm>
      </div>
    </main>
    <Copyright class="login-foot" />
  </div>
</template>

<script lang="ts">
import LoginForm from '@/views/login/components/login-form.vue'
import Copyright from '@/components/copyright/index.vue'
import SysHeader from '@/components/sysheader/index.vue'
import configJson from '@arco/config/settings.json'
import useVisibility from '@arco/hooks/visibility'

import { useAppStore } from '@/layouts/appArco/store'
import { getImgFilePath } from '@/utils/proto'

export default {
  name: 'Login',
  components: { <PERSON><PERSON>F<PERSON>, <PERSON>ys<PERSON>eader, Copyright },
  data() {
    useVisibility()
    return {
      court: this.$route.params.court as string,
      themeColor: configJson.themeColor
    }
  },
  async mounted() {
    const appStore = useAppStore()

    localStorage.removeItem('court')
    // 判断是否存在登录页标识
    if (this.court) {
      localStorage.setItem('court', this.court)
      await appStore.getCustomLoginPage()
      let loginPageDom = this.$refs.loginPageRef as HTMLDivElement
      if (loginPageDom && appStore.loginPageConfig && appStore.loginPageConfig.backgroundPath) {
        const imgPath = getImgFilePath(appStore.loginPageConfig.backgroundPath)
        loginPageDom.style.backgroundImage = `url('${imgPath}')`
      }
    }
  },
  methods: {
    loginSuccessHanlder() {
      this.$router.push({ name: 'desktop' })
    }
  }
}
</script>

<style lang="scss" scoped>
$color-login-anchor: rgb(144, 144, 144);
$font-size-login: 16px;
$font-size-label: $--font-size-base;

.login {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 1000px;
  @include login-bg;
  &-head {
    position: relative;
    z-index: 1;
    @include v-c;
    justify-content: space-between;
    padding: $--page-header-padding;
    height: $--header-height;
    line-height: $--header-height;
    flex-shrink: 0;
  }
  &-main {
    font-size: $font-size-label;
    position: relative;
    flex-grow: 1;
    &__contain {
      position: absolute;
      top: 180px;
      left: 50%;
      padding: 36px 0;
      // padding: 36px 0 36px 80px;
      transform: translateX(-50%);
      // width: 640px;
      width: 600px;
      border-radius: 16px;
      box-shadow: 0 10px 49px 1px rgba(0, 0, 0, 0.2);
      // background: url('@/assets/images/login-dialog-bg.png') no-repeat top left;
      background-color: var(--color-bg-1);
      background-size: 13% 100%;
      opacity: 0.95;
    }
  }
  &-foot {
    flex-shrink: 0;
  }
}
</style>
