<template>
  <a-modal
    v-model:visible="visible"
    top="20%"
    title="手机验证"
    :width="vertical ? '90vw' : '60vw'"
    :close-on-click-modal="false"
    :modal-class="vertical ? 'dialog-sign' : 'dialog-reset'"
    :show-close="false"
    append-to-body
    @close="close"
  >
    <a-form ref="smsFormRef" :model="smsForm" layout="vertical" :rules="smsRules">
      <a-form-item field="smsCode" label="验证码">
        <!-- <a-space :size="10"> -->
        <a-input v-model="smsForm.smsCode" placeholder="短信验证码" autocomplete="off" maxlength="6"> </a-input>
        <div class="ml10">
          <a-button v-if="timer" style="width: 80px"> {{ timer }}s </a-button>
          <a-button v-else type="primary" :loading="smsLoading" @click="sendCode"> 获取验证码 </a-button>
        </div>
        <!-- </a-space> -->
      </a-form-item>
      <a-typography-text v-if="timer" type="success">
        验证码已发送至手机号： <a-typography-text type="danger"> {{ mobile }} </a-typography-text>
      </a-typography-text>
    </a-form>
    <template #footer>
      <a-button @click="close"> 关闭 </a-button>
      <a-button type="primary" :loading="confirming" @click="signConfirm"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { uploadSignImageAndSignFile } from '@/api/arcoApi/sign/customSign'

import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import { defineComponent, ref } from 'vue'

interface Props {
  smsLoading: boolean
  caseId: string
  vertical: boolean
  imgBase64: string
  filePath: string
  authKey: string
  timer: number
  mobile: string
}

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const props = withDefaults(defineProps<Props>(), {})

const emits = defineEmits<{ (event: 'close'): void; (event: 'sendCode'): void; (event: 'confirm'): void }>()

const smsRules = ref({ smsCode: [{ required: true, message: '请输入短信验证码' }] })
const smsFormRef = ref<FormInstance>()
const smsForm = ref({ smsCode: '' })
const confirming = ref(false)

function dataURLtoBlob(dataUrl: string) {
  const arr = dataUrl.split(',')
  const mimes = arr[0]?.match(/:(.*?);/)
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr.buffer], { type: mimes ? mimes[1] : '' })
}

function BlobToFile(b: Blob, filename: string) {
  b['lastModifiedDate'] = new Date()
  b['name'] = filename
  return b
}

const signConfirm = function () {
  if (confirming.value) {
    return
  }
  confirming.value = true
  smsFormRef.value &&
    smsFormRef.value.validate((valid) => {
      if (!valid) {
        const blob = dataURLtoBlob(props.imgBase64)
        const file = BlobToFile(blob, 'sign.png')
        const param = new FormData()
        param.append('file', file, 'sign.png')
        uploadSignImageAndSignFile(
          {
            smsCode: smsForm.value.smsCode,
            signFilePath: props.filePath,
            authKey: props.authKey
          },
          param
        )
          .then(() => {
            Message.success('提交签名成功')
            emits('confirm')
          })
          .catch((e) => {
            Message.error(e?.data?.message || e.message || e || '签名失败')
          })
          .finally(() => {
            confirming.value = false
          })
      } else {
        confirming.value = false
        return false
      }
    })
}

const close = function () {
  smsFormRef.value?.clearValidate()
  emits('close')
}

const sendCode = function () {
  emits('sendCode')
}
</script>

<script lang="ts">
export default defineComponent({ name: 'SettingDialog' })
</script>

<style lang="scss">
.dialog-sign {
  transform: translate(0%, 0%) rotate(90deg) !important;
}
.dialog-reset {
  transform: translate(0%, 0%) !important;
}
</style>
