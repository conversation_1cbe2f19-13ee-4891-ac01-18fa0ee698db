<template>
  <div class="auth">
    <div v-if="isAuthPart" class="auth-part">
      <a-form ref="authFormRef" :model="authForm" layout="vertical" :rules="authFormRule">
        <!-- <a-form-item label="姓名：" field="name">
          <a-input v-model="authForm.name" placeholder="请输入姓名"></a-input>
        </a-form-item> -->
        <a-form-item v-if="needRealName" label="身份证号码：" field="cardNo">
          <a-input v-model="authForm.cardNo" icon="" placeholder="请输入身份证号码">
            <template #prefix>
              <icon-idcard />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="手机号码：" field="mobile">
          <a-input v-model="authForm.mobile" placeholder="请输入手机号码" @change="mobileChange">
            <template #prefix>
              <icon-phone />
            </template>
          </a-input>
        </a-form-item>
      </a-form>
      <div class="auth-part-btn">
        <a-button type="primary" long size="small" @click="handleAuthCert"> 确定 </a-button>
      </div>
    </div>
    <div v-else class="auth-file">
      <!-- h5查看文件 -->
      <iframe ref="fileFrameRef" :src="fileUrl" frameborder="0" style="width: 100%; height: calc(100% - 60px)" />
      <div class="auth-file-btn">
        <a-space :size="20">
          <a-button @click="handleReject"> 拒绝签名 </a-button>
          <a-button type="primary" @click="handleAgree"> 同意签名 </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { newCheck, rejectSign, readFiles } from '@/api/arcoApi/sign/customSign'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@/layouts/appArco/hooks/loading'
import { defineComponent, ref, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { pdfJsViewer } from '@/assets/ts/const'
import checkIdCard from '@/utils/identityTest'
import { encrypt } from '@/utils/rsaEncrypt'
import { useRoute } from 'vue-router'

const { setLoading } = useLoading(true)

const route = useRoute()
const props = defineProps<{ filePath: string; needRealName: boolean }>()
const emits = defineEmits<{
  (event: 'caseId', val: string): void
  (event: 'instrumentsId', val: string): void
  (event: 'cardNo', val: string): void
  (event: 'authKey', val: string): void
  (event: 'reject'): void
  (event: 'agree'): void
  (event: 'mobile', mobile: string): void
}>()

function authData() {
  return { cardNo: '', mobile: '' }
}
const isAuthPart = ref(true)
const authForm = ref(authData())
const authFormRef = ref<FormInstance>()
const authFormRule = ref({
  cardNo: [
    { required: true, message: '身份证号码不能为空', trigger: 'blur' },
    {
      validator: (value: string, callBack: Function) => {
        const errorText = checkIdCard.test(value)
        if (errorText) {
          return callBack(new Error(errorText))
        } else {
          callBack()
        }
      },
      trigger: 'blur'
    }
  ],
  mobile: [
    { required: true, message: '手机号码不能为空', trigger: 'blur' },
    {
      validator: (value: string, callBack: Function) => {
        if (!value) return callBack(new Error('请输入手机号码'))
        if (!/^1\d{10}$/.test(value)) return callBack(new Error('请输入正确的手机号码'))
        return callBack()
      },
      trigger: 'blur'
    }
  ]
})
const fileFrameRef = ref<HTMLIFrameElement>()

const instrumentsId = ref('')
const authKey = ref('')
const fileUrl = ref('')
const caseId = ref('')

onMounted(() => {
  const query = route.query
  if (query.instrumentsId) {
    instrumentsId.value = query.instrumentsId as string
    emits('instrumentsId', instrumentsId.value)
  }
  if (query.caseId) {
    caseId.value = query.caseId as string
    emits('caseId', caseId.value)
  }
  if (query.mobile) {
    authForm.value.mobile = query.mobile as string
    emits('mobile', authForm.value.mobile)
  }
  if (query.source && query.source === 'customSign') {
    if (query.cardNo) {
      authForm.value.cardNo = query.cardNo as string
      emits('cardNo', authForm.value.cardNo)
      handleAuthCert()
    }
  }
})

const handleAuthCert = function () {
  authFormRef.value &&
    authFormRef.value.validate((valid) => {
      if (!valid) {
        confirmAuthCert()
      }
    })
}

const confirmAuthCert = function () {
  // refId2@idCardNo@mobile（案件id@身份证@手机号）
  // eg: 123414324@13113131313@3607XXXXXX1234
  const query = route.query
  const encryptContent = encrypt(`${query.caseId}@${authForm.value.cardNo}@${authForm.value.mobile || ''}`)
  setLoading(true)
  newCheck({
    encryptContent: encryptContent as string,
    filePath: props.filePath
  })
    .then((res) => {
      authKey.value = res
      emits('authKey', authKey.value)
      isAuthPart.value = false
      setTimeout(async () => {
        try {
          const url = await readFiles(authKey.value, props.filePath)
          fileUrl.value = `${pdfJsViewer}?file=${encodeURIComponent(url)}`
          // 微信扫码进入
          const timer = setInterval(() => {
            const frame = fileFrameRef.value?.contentWindow
            if (frame && frame.document.readyState === 'complete') {
              const handTool = frame.document.getElementById('toolbarToggleHandTool')
              if (handTool) {
                handTool.style.display = 'none'
                const print = frame.document.getElementById('print')
                if (print) print.style.display = 'none'
                const specialPrint = frame.document.getElementById('specialPrint')
                if (specialPrint) specialPrint.style.display = 'none'
                const secondaryToolbarToggle = frame.document.getElementById('secondaryToolbarToggle')
                if (secondaryToolbarToggle) secondaryToolbarToggle.style.display = 'none'
              }
              clearInterval(timer)
            }
          }, 300)
        } catch (e) {
          Message.error(e as string)
        }
      })
    })
    .finally(() => {
      setLoading(false)
    })
}

const handleReject = function () {
  Modal.info({
    title: '温馨提示',
    hideCancel: false,
    width: '90vw',
    content: '拒绝签名的相应法律后果由您自行承担，但不影响案件文书的法律效力， 是否确定拒绝签名?',
    onOk: () => {
      rejectSign({ authKey: authKey.value, signFilePath: props.filePath, rejectReason: '' })
        .then(() => {
          emits('reject')
        })
        .catch((e) => {
          Message.error(e)
        })
    },
    onCancel: () => {}
  })
}

const handleAgree = function () {
  emits('agree')
}
const mobileChange = function (val: string) {
  emits('mobile', val)
}
</script>

<script lang="ts">
export default defineComponent({ name: 'Auth' })
</script>

<style scoped lang="scss">
.auth {
  width: 100%;
  height: 100%;
  &-part {
    padding: 60px 40px;
    &-btn {
      margin-top: 30px;
    }
  }
  &-file {
    width: 100%;
    height: 100%;
    &-btn {
      position: fixed;
      bottom: 0;
      width: 100vw;
      left: 0;
      border-top: 1px solid #ddd;
      height: 60px;
      line-height: 60px;
      text-align: center;
    }
  }
}
.miniprogram-wrap {
  width: 100%;
  height: calc(100% - 60px);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
