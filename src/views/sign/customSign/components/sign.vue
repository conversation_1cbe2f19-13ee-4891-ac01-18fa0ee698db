<template>
  <MdtSignaturePad ref="signaturePadRef" width="100%" height="100%" />
  <div ref="signPageRef" class="sign-wra">
    <div v-show="imgUrl" ref="signImgRef" class="canvas-box">
      <img class="canvas-box-img" :src="imgUrl" />
      <div class="canvas-box-btn">
        <div class="canvas-box-btn-left">
          <a-button plain size="small" @click="handleReback"> 返回 </a-button>
        </div>
        <div class="canvas-box-btn-right">
          <a-button type="primary" size="small" @click="handleConfirm(imgUrl)"> 提交签名 </a-button>
        </div>
        <div class="canvas-box-btn-right">
          <a-button size="small" @click="handleReset"> 重新签名 </a-button>
        </div>
      </div>
    </div>
    <div v-show="!imgUrl" ref="canvasRef" class="canvas-box">
      <!-- <img :src="imgBase64" style="height: 300px; width: 100%;" alt=""> -->
      <div class="canvas-box-btn">
        <div class="canvas-box-btn-left">
          <a-button plain size="small" @click="handleReback"> 返回 </a-button>
        </div>
        <div ref="saveCanvasRef" class="canvas-box-btn-right">
          <a-button type="primary" size="small" @click="submitSign"> 提交签名 </a-button>
        </div>
        <div ref="clearCanvasRef" class="canvas-box-btn-right">
          <a-button size="small" @click="initSign"> 重新签名 </a-button>
        </div>
      </div>
      <p class="tips">请在空白处书写您的姓名</p>
    </div>

    <sms-dialog
      v-model:visible="dialogVisible"
      :timer="timer"
      :mobile="mobile"
      :case-id="caseId"
      :file-path="filePath"
      :auth-key="authKey"
      :vertical="vertical"
      :sms-loading="smsLoading"
      :img-base64="imgBase64"
      @close="dialogVisible = false"
      @send-code="sendCode"
      @confirm="emits('confirm')"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { getExistSignImage, sendSignSmsCode } from '@/api/arcoApi/sign/customSign'
import MdtSignaturePad from '@arco/components/MdtSignaturePad/index.vue'
import { horizontalScreen } from '../utils/horizontalScreen'
import { Message } from '@arco-design/web-vue'
import smsDialog from './smsDialog.vue'

const props = defineProps<{
  authKey: string
  mobile: string
  filePath: string
  caseId: string
  instrumentsId: string
}>()

const emits = defineEmits<{ (event: 'emits'): void; (event: 'confirm'): void; (event: 'reback'): void }>()

const signaturePadRef = ref<InstanceType<typeof MdtSignaturePad>>()
const signPageRef = ref<HTMLDivElement>()
const canvasRef = ref<HTMLDivElement>()
const dialogVisible = ref(false)
const vertical = ref(true)
const imgBase64 = ref('')
const imgUrl = ref('')
const timer = ref(0)

// 是否正在调用发送短信的api。该接口有时候挺慢的，容易多次发送
const smsLoading = ref(false)

const getVertical = function () {
  const verticalStr = signPageRef.value && signPageRef.value?.dataset?.vertical
  vertical.value = verticalStr === 'true'
}

const getImgUrl = function () {
  if (!props.authKey) {
    handleReset()
    return false
  }
  getExistSignImage(props.authKey).then((res) => {
    imgUrl.value = res ? `data:image/png;base64,${res}` : ''
    if (!imgUrl.value) {
      handleReset()
    }
  })
}

const handleReset = function () {
  imgUrl.value = ''
}

const handleReback = function () {
  emits('reback')
}

const handleConfirm = function (imgBase64Value: string) {
  if (!imgBase64Value) {
    Message.warning('请在空白处书写您的姓名')
    return false
  }
  imgBase64.value = imgBase64Value
  dialogVisible.value = true
}

const submitSign = function () {
  const res = signaturePadRef.value?.saveSignature()
  handleConfirm(res?.data)
}

const initSign = function () {
  signaturePadRef.value?.clearSignature()
}

const sendCode = function () {
  smsLoading.value = true
  sendSignSmsCode({ mobile: props.mobile, filePath: props.filePath, authKey: decodeURIComponent(props.authKey) })
    .then(() => {
      timer.value = 60
      const intTimer = setInterval(() => {
        if (timer.value === 0) {
          clearInterval(intTimer)
        } else {
          timer.value--
        }
      }, 1000)
    })
    .finally(() => {
      smsLoading.value = false
    })
}

watch(
  () => props.authKey,
  (val) => val && getImgUrl(),
  { immediate: true }
)

onMounted(() => {
  nextTick(() => {
    window.addEventListener('resize', getVertical, false)
    window.addEventListener('orientationchange', getVertical, false)

    if (signPageRef.value) {
      horizontalScreen.mounted(signPageRef.value)
    }
  })
})
onBeforeUnmount(() => {
  if (signPageRef.value) {
    horizontalScreen.unmounted(signPageRef.value)
  }
  window.removeEventListener('resize', getVertical, false)
  window.removeEventListener('orientationchange', getVertical, false)
})
</script>

<script lang="ts">
export default defineComponent({ name: 'Sign' })
</script>

<style scoped lang="scss">
.sign-wra {
  width: 100%;
  height: 100%;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none
}
.canvas-box {
  width: 100%;
  height: 100%;
  position: relative;
  &-img {
    width: 100%;
    height: calc(100% - 60px);
  }
  &-btn {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    line-height: 60px;
    pointer-events: all;
    &-left {
      float: left;
      margin-left: 20px;
    }
    &-right {
      float: right;
      margin-right: 20px;
    }
  }
  .tips {
    position: absolute;
    z-index: -1;
    color: #999;
    padding: 10px;
    top: 0;
  }
}
</style>
