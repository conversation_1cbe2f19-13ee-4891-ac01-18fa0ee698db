<template>
  <div class="custom-sign">
    <auth
      v-if="isAuth"
      :file-path="filePath"
      :need-real-name="needRealName"
      @reject="handleAuthReject"
      @agree="handleAuthAgree"
      @auth-key="getAuthKey"
      @instruments-id="getInstrumentsId"
      @case-id="getPlatformId"
      @mobile="getMobile"
    />
    <sign
      v-if="!isAuth && agreeSign && !completeSign"
      :auth-key="authKey"
      :mobile="mobile"
      :file-path="filePath"
      :case-id="caseId"
      :instruments-id="instrumentsId"
      @reback="handleSignReback"
      @confirm="handleSignConfirm"
    />

    <div v-if="(!isAuth && !agreeSign && !completeSign) || (!isAuth && completeSign)" class="custom-sign-tips">
      <!-- <div v-if="!completeSign">您已拒绝签名</div>
      <div v-if="completeSign">您的签名已完成</div> -->

      <a-result :status="completeSign ? '500': '403'" :title="completeSign? '签名成功': '签名失败'">
        <template #subtitle> {{ completeSign ? '您的签名已完成': '您已拒绝签名' }} </template>
      </a-result>

      <!-- 小程序环境下，显示返回按钮 -->
      <div v-if="isMiniProgram" class="back-wrap">
        <a-button type="primary" @click="backMiniProgram"> 返回 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import auth from './components/auth.vue'
import sign from './components/sign.vue'
import { useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'

const isMiniProgram = ref(window.__wxjs_environment === 'miniprogram') // 是否小程序中的webview环境
const completeSign = ref(false)
const needRealName = ref(false) // 是否显示身份证输入框
const agreeSign = ref(true)
const isAuth = ref(true)

const instrumentsId = ref('')
const filePath = ref('')
const authKey = ref('')
const caseId = ref('')
const mobile = ref('')

const route = useRoute()


onMounted(() => {
  filePath.value = (route.query.filePath as string) || ''
  needRealName.value = route.query.needRealName !== 'false'
})

const handleAuthReject = () => {
  isAuth.value = false
  agreeSign.value = false
}
const handleAuthAgree = () => {
  isAuth.value = false
  agreeSign.value = true
}
const handleSignReback = () => {
  isAuth.value = true
}
const handleSignConfirm = () => {
  completeSign.value = true
}
const getAuthKey = (auKey: string) => {
  authKey.value = auKey
}
const getInstrumentsId = (mid: string) => {
  instrumentsId.value = mid
}
const getPlatformId = (pId: string) => {
  caseId.value = pId
}
const getMobile = (mobileNum: string) => {
  mobile.value = mobileNum
}

const backMiniProgram = async function () {
  if (!window.wx) {
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.3.2.js'
    script.onload = function (e) {
      back()
    }
    window.document.getElementsByTagName('head')[0].appendChild(script)
  } else {
    back()
  }
  function back() {
    window.wx?.miniProgram.navigateBack()
  }
}

window.document.title = '电子签名'
</script>

<style scoped lang="scss">
.custom-sign {
  width: 100%;
  height: 100%;
  &-tips {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    font-size: 20px;
  }
}
.back-wrap {
  margin-top: 30px;
  text-align: center;
}
</style>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
