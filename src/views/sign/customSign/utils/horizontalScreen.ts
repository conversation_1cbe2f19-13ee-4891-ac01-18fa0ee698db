export const horizontalScreen = {
  mounted(el) {
    const getDocumentSize = () => [
      document.documentElement.clientWidth,
      document.documentElement.clientHeight
    ]

    // 设备开启竖屏锁定，强制横屏模式
    const vertical = () => {
      const [width, height] = getDocumentSize()
      el.style.transform = `rotate(90deg)`
      el.style.transformOrigin = width / 2 + 'px center'
      el.style.width = `${height}px`
      el.style.height = `${width}px`
      el.dataset.vertical = true
    }

    // 设备关闭竖屏锁定，横屏时， 还原成正常模式
    const rest = () => {
      const [width, height] = getDocumentSize()
      el.style.transform = `rotate(0deg)`
      el.style.width = `${width}px`
      el.style.height = `${height}px`
      el.dataset.vertical = false
    }

    el.resize = function() {
      if (document.activeElement?.nodeName === 'INPUT') return // 兼容安卓
      if ([null, 180, 0].includes(window.orientation)) {
        vertical()
      } else if ([90, -90].includes(window.orientation)) {
        rest()
      }
    }

    el.resize()

    el.click = e => {
      if (e.target.nodeName === 'INPUT') {
        rest()
      } else if (![90, -90].includes(window.orientation)) {
        vertical()
      }
    }

    window.addEventListener('click', el.click, false)
    window.addEventListener('resize', el.resize, false) // 兼容安卓
    window.addEventListener('orientationchange', el.resize, false)
  },
  unmounted(el) {
    window.removeEventListener('click', el.click, false)
    window.removeEventListener('resize', el.resize, false) // 兼容安卓
    window.removeEventListener('orientationchange', el.resize, false)
  }
}
