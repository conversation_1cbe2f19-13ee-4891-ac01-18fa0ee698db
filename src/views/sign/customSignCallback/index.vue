<template>
  <div class="authentication-callback">
    <template v-if="!loaded"><div class="loading-text">正在获取识别结果，请稍等...</div>  </template>
    <template v-else>
      <a-result :status="result?.success ? 'success': 'error'" :title="resultObj.result">
        <template #subtitle> {{ resultObj.desc }} </template>
        <template #extra>
          <a-space v-if="result?.success">
            <a-button type="primary" @click="goback"> 返回电子签名页面 </a-button>
          </a-space>
        </template>
      </a-result>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { realNameAuthNotice } from '@/api/arcoApi/sign/customSign'
import { useRoute, useRouter } from 'vue-router'
import { defineComponent, ref } from 'vue'

const router = useRouter()
const route = useRoute()

const resultObj = ref({ result: '认证失败', desc: 'BizToken为空', iconName: 'cuowu' })
const result = ref<REQUEST_POST_REALNAMEAUTH_NOTICE_PARAM_TYPE>()
const loaded = ref(false)

const goback = function () {
  // extra=caseId:@:instrumentsId:@:filePath
  // Extra=1267988332336517120:@:1269894795413819392:@:/instruments/test/date-202408051608/instruments-1269894795413819392-date-202408011416%E6%BB%B4%E6%BB%B4%E7%94%B5%E5%AD%90%E5%8F%91%E7%A5%A8.pdf
  if (result.value?.refId) {
    const { Extra } = route.query as { Extra: string }
    const dataArr = Extra?.split(':@:')
    const instrumentsId = dataArr[1]
    const filePath = dataArr[2]
    const caseId = dataArr[0]
    router.push({
      path: 'customSign',
      query: {
        instrumentsId: instrumentsId || result.value.refId || '',
        filePath: filePath || result.value.filePath || '',
        caseId: caseId || result.value.refId2 || '',
        cardNo: result.value.idcardNo,
        mobile: result.value.mobile,
        authKey: result.value.refId,
        source: 'customSign'
      }
    })
  }
}

const init = function () {
  const { BizToken } = route.query as { BizToken: string }
  if (!BizToken) {
    result.value = { errMsg: 'BizToken为空', success: false }
    loaded.value = true
    return
  }
  // 前端跳转通知
  realNameAuthNotice(BizToken)
    .then((res) => {
      result.value = res
      if (result.value.success) {
        resultObj.value = {
          result: '认证通过',
          desc: '恭喜，您已实名认证成功！',
          iconName: 'duigouxiao'
        }
      } else {
        resultObj.value = {
          result: '认证失败',
          desc: result.value.errMsg || '',
          iconName: 'cuowu'
        }
      }
    })
    .finally(() => {
      loaded.value = true
    })
}

init()
</script>

<script lang="ts">
export default defineComponent({ name: 'CustomSignCallback' })
</script>

<style lang="scss" scoped>
.authentication-callback {
  text-align: center;
  flex-direction: column;
  padding-top: 120px;
  &__icon {
    font-size: 160px;
  }
  &__result {
    margin-top: 44px;
    font-size: 44px;
    font-weight: bold;
    color: #333;
  }
  &__desc {
    font-size: 22px;
    color: #999;
    margin-top: 26px;
  }
  &__btn {
    margin-top: 30px;
  }
  .web-iconfont {
    padding: 15px 20px;
    font-size: 70px;
    &.icon-duigouxiao {
      color: #5ad354;
    }
    &.icon-cuowu {
      color: #ff4848;
    }
  }
  .loading-text {
    color: #aaa;
    font-size: 20px;
  }
}
</style>
