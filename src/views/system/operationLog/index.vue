<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="props.caseId?null: lisPageTitle">
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="logTitle" label="操作内容">
                  <a-input v-model="searchForm.logTitle" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="accountName" label="账号名">
                  <a-input v-model="searchForm.accountName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="!props.caseId">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容，支持模糊检索" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsIdList" label="案源方">
                  <a-select
                    v-model="searchForm.entrustsIdList"
                    :options="entrustsOptions"
                    :max-tag-count="1"
                    placeholder="请选择"
                    allow-clear
                    multiple
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="operateType" label="操作类型">
                  <a-select
                    v-model="searchForm.operateType"
                    :options="operateTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="操作时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-table
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #rank="{ rowIndex }"> {{ rowIndex < 9 ? '0' : '' }}{{ rowIndex + 1 }} </template>
        <template #operations="{ record }">
          <a-button type="text" @click="handleFieldInfo(record.logId)">参数信息</a-button>
        </template>
      </a-table>
    </a-card>
    <a-drawer v-model:visible="drawerVisible" :width="480" title="参数信息" @cancel="drawerVisible = false">
      <a-row :gutter="16">
        <a-col :span="24">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">请求参数：</span>
          </div>
          <a-textarea v-model="logParam" :auto-size="{ minRows: 8 }" placeholder="参数信息" readonly />
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { getOperationLogList, byLogIdGetOperationLog } from '@/api/arcoApi/systemManage/operationLog'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'
import dict from '@/dict/systemManage'
import _ from 'lodash'

const props = defineProps(['caseId'])


type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const generateFormModel = () => {
  return {
    accountDeptIdList: [],
    accountId: '',
    accountIdList: [],
    accountName: '',
    accountOrgIdList: [],
    caseNo: '',
    entrustsDeptIdList: [],
    entrustsIdList: [],
    logTitle: '',
    operateType: '',
    optDateEnd: '',
    optDateStart: '',
    requestIp: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('操作日志')
// const pageEnName = ref('Call')

const searchForm = ref<REQUEST_POST_OPERATIONLOG_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_OPERATIONLOG_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const logParam = ref('')

const drawerVisible = ref(false)

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const operateTypeOptions = ref<SelectOptionData[]>(dict.operateTypeOptions)
const entrustsOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 60, title: '序号', dataIndex: 'rank', slotName: 'rank' },
  { width: 180, tooltip: true, ellipsis: true, title: '操作内容', dataIndex: 'logTitle' },
  { width: 180, title: '操作时间', dataIndex: 'createTime' },
  { width: 120, align: 'center', title: '账号名', dataIndex: 'accountName' },
  { width: 220, tooltip: true, ellipsis: true, align: 'center', title: '员工组织名称', dataIndex: 'accountOrgName' },
  { width: 140, align: 'center', title: '员工部门名称', dataIndex: 'accountDeptName' },
  { width: 60, align: 'center', title: '分类', dataIndex: 'operateType', slotName: 'operateType' },
  { width: 200, tooltip: true, ellipsis: true, align: 'center', title: '案件ID', dataIndex: 'caseId' },
  { width: 240, tooltip: true, ellipsis: true, align: 'center', title: '案件编号', dataIndex: 'caseNo' },
  { width: 220, tooltip: true, ellipsis: true, align: 'center', title: '案源方名称', dataIndex: 'entrustsName' },
  { width: 140, title: '案源方部门名称', dataIndex: 'entrustsDeptName', align: 'center' },
  { width: 150, tooltip: true, ellipsis: true, title: '请求IP', dataIndex: 'requestIp', align: 'center' },
  { width: 200, tooltip: true, ellipsis: true, title: '请求路径', dataIndex: 'requestUrl', align: 'center' },
  { width: 100, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleFieldInfo = async (id: string) => {
  let paramRes = await byLogIdGetOperationLog(id)
  if (paramRes) {
    drawerVisible.value = true
    logParam.value = paramRes
  }
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.optDateStart = timeArr[0] || ''
    searchForm.value.optDateEnd = timeArr[1] || ''
  } else {
    searchForm.value.optDateStart = ''
    searchForm.value.optDateEnd = ''
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
		searchForm.value.caseId = props.caseId
    const data = await getOperationLogList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

getAllEntrusts()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
