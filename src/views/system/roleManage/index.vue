<template>
  <div class="role-manage">
    <div class="role-menu">
      <div class="role-menu-tree">
        <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
          <div class="role-menu-tree-body">
            <div v-for="(item, index) in roleList" :key="index" class="role-menu-tree-item">
              <div class="role-menu-tree-item__title">
                {{ groupNameObj[item.groupName] }}
              </div>
              <div
                v-for="(row, rowIndex) in item.roles"
                :key="rowIndex"
                class="role-menu-tree-item__row"
                :class="{ 'is-active': activeItem.roleId === row.roleId }"
                @click="handleClickActive(row)"
              >
                <div class="row-text">
                  <icon-idcard class="f18" />
                  <a-tooltip :content="row.roleName">
                    <span class="role-menu-tree-item__row-text ml6">{{ row.roleName }}</span>
                  </a-tooltip>
                </div>
                <a-popover content-class="tree-popover-btn padding0" trigger="click" position="br">
                  <a-button type="text" @click.stop><icon-more-vertical /></a-button>
                  <template #content>
                    <ul>
                      <li v-auth="['roleSave']" @click="handleRoleEdit(row)">编辑角色</li>
                      <li v-auth="['roleDelete']" class="danger" @click="handleRoleDelete(row)">删除</li>
                    </ul>
                  </template>
                </a-popover>
              </div>
            </div>
          </div>
        </a-scrollbar>
      </div>
      <div class="role-menu-btn">
        <a-button v-auth="['roleSave']" type="outline" @click="handleAddRole">
          <template #icon>
            <icon-user-add />
          </template>
          <template #default>新增角色</template>
        </a-button>
      </div>
    </div>
    <div class="role-content">
      <a-card class="role-content-card" :bordered="false">
        <template #title>
          <span class="bold">{{ activeItem.roleName }}后台权限</span>
        </template>
        <template #extra>
          <a-button v-if="!isEditRole" v-auth="['roleSave']" type="primary" @click="handleRoleSettingEdit">
            编辑
          </a-button>
          <a-button v-if="isEditRole" @click="handleRoleSettingCancel">取消</a-button>
          <a-button v-if="isEditRole" v-auth="['roleSave']" class="ml12" type="primary" @click="handleRoleSettingSave">
            保存
          </a-button>
        </template>
        <div class="role-content-block">
          <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
            <div class="role-content-main">
              <div v-for="(node, index) in roleSettingVO" :key="index" class="setting-item">
                <div class="setting-item-side">
                  <div v-if="isEditRole">
                    <a-checkbox v-model="node.openFlag" @change="handleCheckBoxChange($event, node)">
                      {{ node.meta.title }}
                    </a-checkbox>
                  </div>
                  <div v-else>
                    <icon-check-circle v-if="node.openFlag" class="success" />
                    <icon-close-circle v-else class="danger" />
                    <span class="ml4">{{ node.meta.title }}</span>
                  </div>
                </div>
                <div class="setting-item-main">
                  <role-setting
                    v-if="node.children && node.children.length"
                    :child-node="node.children"
                    :is-edit-role="isEditRole"
                  ></role-setting>
                  <div v-if="node.childNode && node.childNode.length" class="setting-item-node">
                    <role-setting-row :child-node="node.childNode" :is-edit-role="isEditRole"></role-setting-row>
                  </div>
                </div>
              </div>
            </div>
          </a-scrollbar>
        </div>
      </a-card>
    </div>
    <a-modal
      width="600px"
      :title="isEdit ? '编辑角色' : '新增角色'"
      :visible="dialogVisible"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <a-form
        ref="dialogFormRef"
        :model="dialogForm"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <a-form-item field="groupName" label="角色归属" :rules="[{ required: true, message: '请选择角色归属' }]">
          <a-select
            v-model="dialogForm.groupName"
            :options="platformOptions"
            placeholder="请选择"
            allow-clear
            :disabled="isEdit"
          />
        </a-form-item>
        <a-form-item
          field="roleName"
          label="角色名称"
          :rules="[
            { required: true, message: '请输入角色名称' },
            { validator: checkRoleName, message: '角色名称已存在' }
          ]"
        >
          <a-input v-model.trim="dialogForm.roleName" placeholder="请输入角色名称" allow-clear />
        </a-form-item>
        <a-form-item field="roleRemark" label="备注说明">
          <a-textarea v-model="dialogForm.roleRemark" placeholder="请输入备注说明" allow-clear />
        </a-form-item>
        <a-form-item field="isSystem" label="是否系统角色">
          <a-radio-group v-model="dialogForm.isSystem">
            <a-radio :value="true">是</a-radio>
            <a-radio :value="false">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { updateRolePermisiion, getRoleDetail, updateRole } from '@/api/arcoApi/systemManage/roleManage'
import { deleteRole, allFunNew, addRole, getAll } from '@/api/arcoApi/systemManage/roleManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import RoleSettingRow from './components/roleSettingRow.vue'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import RoleSetting from './components/roleSetting.vue'
import { Modal, Message } from '@arco-design/web-vue'
import { ref } from 'vue'
import _ from 'lodash'

const roleList = ref<REQUEST_GET_ROLE_LIST_ALLDATA_TYPE[]>([])
const activeItem = ref<REQUEST_GET_ROLE_DETAILS_DATA_TYPE>({
  roleRemark: '',
  groupName: '',
  roleName: '',
  menuIds: [],
  roleId: ''
})

const dialogForm = ref<REQUEST_ROLE_DATA_TYPE>({
  isSystem: false,
  roleRemark: '',
  groupName: '',
  roleName: '',
  roleId: ''
})

const platformOptions = ref<SelectOptionData[]>([
  { label: '平台方角色', value: '1' },
  { label: '调解组织角色', value: '2' },
  { label: '案源方角色', value: '3' },
  { label: '功能角色', value: '4' }
])

const groupNameObj = ref({
  1: '平台方角色',
  2: '调解组织角色',
  3: '案源方角色',
  4: '功能角色'
})

interface REQUEST_GET_ROLE_AUTHE_DATA_TYPE extends REQUEST_GET_ROLE_AUTH_DATA_TYPE {
  childNode?: REQUEST_GET_ROLE_AUTHE_DATA_TYPE[]
  openFlag?: boolean
}

const customSettingVO = ref<REQUEST_GET_ROLE_AUTHE_DATA_TYPE[]>([])

const oldRoleSettingVO = ref<REQUEST_GET_ROLE_AUTHE_DATA_TYPE[]>([])
const roleSettingVO = ref<REQUEST_GET_ROLE_AUTHE_DATA_TYPE[]>([])
const dialogFormRef = ref<FormInstance>()
const roleNames = ref<string[]>([])

const dialogVisible = ref<boolean>(false)
const isEditRole = ref<boolean>(false)
const menuIdList = ref<string[]>([])
const originalName = ref<string>('')
const isEdit = ref<boolean>(false)

const checkRoleName = (value: string, cb) => {
  if (roleNames.value.includes(value) && value !== originalName.value) {
    cb('该角色名称已重复')
  } else {
    cb()
  }
}

// 角色功能
const getRoleList = async () => {
  let res = await getAll()
  roleList.value = res || []
  roleNames.value = []
  if (roleList.value.length && roleList.value[0].roles && roleList.value[0].roles.length) {
    collectRoleNames(roleList.value)
    let row = roleList.value[0].roles[0]
    let detailsRes = await getRoleDetail(row.roleId)
    activeItem.value = detailsRes
    let list: REQUEST_GET_ROLE_AUTH_DATA_TYPE[] = _.cloneDeep(customSettingVO.value)
    setCurrentRoleSetting(list)
    roleSettingVO.value = _.cloneDeep(list)
    oldRoleSettingVO.value = _.cloneDeep(list)
  }
}

// 遍历角色树形列表push获取角色名称
function collectRoleNames(tree: REQUEST_GET_ROLE_LIST_ALLDATA_TYPE[]) {
  // 遍历当前层级的每个角色
  for (const node of tree) {
    // 如果该角色还有子角色，递归调用此函数处理子角色
    if (node.roles && node.roles.length > 0) {
      roleNames.value = roleNames.value.concat(node.roles.map((role) => role.roleName))
    }
  }
}

const getRoleSettingList = async () => {
  let res = await allFunNew()
  if (res?.length) {
    for (let i = 0; i < res.length; i++) {
      const element = res[i]
      setRoleSetting(element)
    }
    customSettingVO.value = res
  }
  getRoleList()
}
const setRoleSetting = (res: REQUEST_GET_ROLE_AUTHE_DATA_TYPE) => {
  let deleteChildIndex: number[] = []
  res.childNode = []
  for (let i = 0; i < res.children.length; i++) {
    const element = res.children[i]
    if (element.children?.length) {
      setRoleSetting(element)
    } else {
      deleteChildIndex.push(i)
      res.childNode.push(element)
    }
  }
  res.children = res.children.filter((item, index: number) => {
    if (deleteChildIndex.indexOf(index) === -1) {
      return item
    }
  })
}
const setCurrentRoleSetting = (arr: REQUEST_GET_ROLE_AUTHE_DATA_TYPE[]) => {
  let permissions = activeItem.value?.menuIds || []
  for (let i = 0; i < arr.length; i++) {
    const element = arr[i]
    if (permissions.indexOf(element.menuId) !== -1) {
      arr[i].openFlag = true
    } else {
      arr[i].openFlag = false
    }
    if (element.children) {
      setCurrentRoleSetting(element.children)
    }
    if (element.childNode) {
      setCurrentRoleSetting(element.childNode)
    }
  }
}
getRoleSettingList()

const handleAddRole = async () => {
  isEdit.value = false
  dialogVisible.value = true
}

const handleRoleEdit = (row: REQUEST_ROLE_DATA_TYPE) => {
  dialogForm.value = {
    groupName: row.groupName,
    roleId: row.roleId,
    roleName: row.roleName,
    roleRemark: row.roleRemark,
    isSystem: row.isSystem
  }
  isEdit.value = true
  dialogVisible.value = true
  originalName.value = row.roleName
}

const handleRoleDelete = async (row: REQUEST_ROLE_DATA_TYPE) => {
  Modal.warning({
    title: '提示',
    content: '是否确定删除该角色？',
    hideCancel: false,
    onOk: async () => {
      let res = await deleteRole(row.roleId)
      if (res.success === false) {
        Modal.error({ title: '删除角色失败', content: res.message })
      } else {
        Message.success('角色删除成功')
        getRoleList()
      }
    }
  })
}

const handleClickActive = async (row: REQUEST_ROLE_DATA_TYPE) => {
  isEditRole.value = false
  let res = await getRoleDetail(row.roleId)
  activeItem.value = res
  let list = _.cloneDeep(customSettingVO.value)
  setCurrentRoleSetting(list)
  roleSettingVO.value = _.cloneDeep(list)
  oldRoleSettingVO.value = _.cloneDeep(list)
}
const handleDialogConfirm = async () => {
  const res = await dialogFormRef.value?.validate()
  if (!res) {
    let params = { ...dialogForm.value }
    if (!isEdit.value) {
      params.roleId = ''
      await addRole(params)
      Message.success('新增角色成功')
    } else {
      await updateRole(params)
      Message.success('修改角色成功')
    }
    handleDialogCancel()
    getRoleList()
  }
  return false
}
const handleDialogCancel = () => {
  isEdit.value = false
  dialogForm.value = {
    isSystem: false,
    roleRemark: '',
    groupName: '',
    roleName: '',
    roleId: ''
  }
  dialogFormRef.value?.clearValidate()
  dialogVisible.value = false
}
// 角色功能权限
const handleCheckBoxChange = (val: any, node: REQUEST_GET_ROLE_AUTHE_DATA_TYPE) => {
  if (!val) {
    setNodeOpenFlag(node)
  }
}
const setNodeOpenFlag = (node: REQUEST_GET_ROLE_AUTHE_DATA_TYPE) => {
  node.openFlag = false
  if (node.childNode) {
    for (let i = 0; i < node.childNode.length; i++) {
      node.childNode[i].openFlag = false
    }
  }
  if (node.children) {
    for (let i = 0; i < node.children.length; i++) {
      setNodeOpenFlag(node.children[i])
    }
  }
}
const handleRoleSettingEdit = () => {
  isEditRole.value = true
}
const handleRoleSettingCancel = () => {
  roleSettingVO.value = _.cloneDeep(oldRoleSettingVO.value)
  isEditRole.value = false
}
const handleRoleSettingSave = async () => {
  menuIdList.value = []
  for (let i = 0; i < roleSettingVO.value.length; i++) {
    const element = roleSettingVO.value[i]
    setCurrentMenuIds(element)
  }
  let params = {
    roleId: activeItem.value?.roleId,
    menuIds: menuIdList.value
  }
  await updateRolePermisiion(params)
  Message.success('保存成功')
  oldRoleSettingVO.value = _.cloneDeep(roleSettingVO.value)
  isEditRole.value = false
}

const setCurrentMenuIds = (node: REQUEST_GET_ROLE_AUTHE_DATA_TYPE) => {
  if (node.openFlag) {
    menuIdList.value.push(node.menuId)
  }
  if (node.childNode) {
    for (let i = 0; i < node.childNode.length; i++) {
      if (node.childNode[i].openFlag) {
        menuIdList.value.push(node.childNode[i].menuId)
      }
    }
  }
  if (node.children) {
    for (let i = 0; i < node.children.length; i++) {
      setCurrentMenuIds(node.children[i])
    }
  }
}
</script>

<style scoped lang="scss">
.role {
  &-manage {
    display: flex;
    background-color: var(--color-bg-1);
  }
  &-menu {
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dedede;
    &-tree {
      padding: 20px 0 0 16px;
      flex-grow: 1;
      height: 400px;
      &-item {
        margin-bottom: 16px;
        padding-right: 16px;
        &__title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 16px;
        }
        &__row {
          display: flex;
          padding-left: 16px;
          height: 32px;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;
          border-radius: 4px;
          cursor: pointer;
          &-text {
            display: inline-block;
            width: calc(100% - 24px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.is-active,
          &:hover,
          &:focus,
          &:active {
            background-color: lighten($color: $color-primary, $amount: 60%);
            color: $color-primary;
            font-weight: bold;
          }
          .row-text {
            flex-grow: 1;
            height: 32px;
            display: flex;
            align-items: center;
            width: calc(100% - 70px);
          }
          .arco-btn-text {
            color: #303030;
          }
        }
      }
    }
    &-btn {
      padding: 16px;
      .arco-btn {
        width: 100%;
      }
    }
  }
  &-content {
    flex-grow: 1;
    &-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      :deep(.arco-card-body) {
        padding: 0;
        flex-grow: 1;
        height: 400px;
      }
    }
    &-block {
      height: 100%;
    }
    &-main {
      line-height: 50px;
      border-bottom: 1px solid #dedede;
      .setting-item {
        display: flex;
        border-style: solid;
        border-color: #dedede;
        border-width: 0 0 1px 0;
        &:last-child {
          border-width: 0;
        }
        &-side {
          width: 160px;
          padding-left: 12px;
          font-weight: bold;
          .arco-icon {
            font-size: 16px;
            vertical-align: top;
            margin-top: 17px;
          }
        }
        &-main {
          width: calc(100% - 160px);
          flex-grow: 1;
          border-style: solid;
          border-color: #dedede;
          border-width: 0 0 0 1px;
        }
      }
    }
  }
}
.tree-popover-btn {
  ul {
    margin: 4px 0 8px 0;
    list-style: none;
    padding: 0;
    width: 100px;
    li {
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      & + li {
        border-top: 1px solid #dedede;
      }
      &:hover,
      &:focus,
      &:active {
        background-color: lighten($color: $color-primary, $amount: 60%);
        color: $color-primary;
        font-weight: bold;
      }
    }
  }
}
</style>
