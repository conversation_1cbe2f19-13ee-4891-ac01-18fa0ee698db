<template>
  <div v-for="(node, index) in props.childNode" :key="index" class="setting-item">
    <div class="setting-item-side">
      <div v-if="isEditRole">
        <a-checkbox v-model="node.openFlag" @change="handleCheckBoxChange($event, node)">{{ node.meta.title }}</a-checkbox>
      </div>
      <div v-else>
        <icon-check-circle v-if="node.openFlag" class="success" />
        <icon-close-circle v-else class="danger" />
        <span class="ml4">{{ node.meta.title }}</span>
      </div>
    </div>
    <div class="setting-item-main">
      <role-setting v-if="node.children && node.children.length" :child-node="node.children" :is-edit-role="isEditRole"></role-setting>
      <div v-if="node.childNode && node.childNode.length" class="setting-item-node">
        <role-setting-row :child-node="node.childNode" :is-edit-role="isEditRole"></role-setting-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import RoleSettingRow from './roleSettingRow.vue'
  import RoleSetting from './roleSetting.vue'

  const props = defineProps({
    childNode: {
      type:Array,
      default() {
        return []
      }
    },
    isEditRole: {
      type: Boolean,
      default: false
    }
  })

  const handleCheckBoxChange = (val: any, node: any) => {
    if (!val) {
      setNodeOpenFlag(node)
    }
  }
  
  const setNodeOpenFlag = (node: any) => {
    node.openFlag = false
    if (node.childNode) {
      for (let i = 0; i < node.childNode.length; i++) {
        node.childNode[i].openFlag = false
      }
    }
    if (node.children) {
      for (let i = 0; i < node.children.length; i++) {
        setNodeOpenFlag(node.children[i])
      }
    }
  }
</script>

<style scoped lang="scss">
.setting-item {
  display: flex;
  border-style: solid;
  border-color: #dedede;
  border-width: 0 0 1px 0;
  &:last-child {
    border-width: 0;
  }
  &-side {
    width: 160px;
    padding-left: 12px;
    font-weight: bold;
    .arco-icon {
      font-size: 16px;
      vertical-align: top;
      margin-top: 17px;
    }
  }
  &-main {
    width: calc(100% - 160px);
    flex-grow: 1;
    border-style: solid;
    border-color: #dedede;
    border-width: 0 0 0 1px;
  }
}
</style>
