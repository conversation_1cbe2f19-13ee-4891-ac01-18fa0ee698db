<template>
  <div v-for="(node, index) in props.childNode" :key="index" class="setting-item-row">
    <div v-if="isEditRole">
      <a-checkbox v-model="node.openFlag">{{ node.meta.title }}</a-checkbox>
    </div>
    <div v-else>
      <icon-check-circle v-if="node.openFlag" class="success" />
      <icon-close-circle v-else class="danger" />
      <span class="ml4">{{ node.meta.title }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    childNode: {
      type: Array,
      default() {
        return []
      }
    },
    isEditRole: {
      type: <PERSON><PERSON>an,
      default: false
    }
  })
</script>

<style scoped lang="scss">
.setting-item-row {
  display: inline-block;
  padding: 0 12px;
  font-weight: bold;
  .arco-icon {
    font-size: 16px;
    vertical-align: top;
    margin-top: 17px;
  }
}
</style>
