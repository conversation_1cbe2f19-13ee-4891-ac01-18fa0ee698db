<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="loginName" label="登录名">
                  <a-input v-model="searchForm.loginName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="employeeName" label="账号名称">
                  <a-input v-model="searchForm.employeeName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="requestIp" label="登录IP">
                  <a-input v-model="searchForm.requestIp" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="操作时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-table
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #rank="{ rowIndex }"> {{ rowIndex < 9 ? '0' : '' }}{{ rowIndex + 1 }} </template>
        <template #location="{ record }">
          {{record.longitude }} , {{ record.latitude}}
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { getLoginLogList } from '@/api/arcoApi/systemManage/loginLog'
import { getOrg } from '@/api/arcoApi/caseManage/caseManage'
import useLoading from '@/layouts/appArco/hooks/loading'
import { getLocationLinkProps } from '@/utils/locationLink'
import { computed, ref, reactive, watch } from 'vue'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const generateFormModel = () => {
  return {
    employeeName: '',
    optDateStart: '',
    optDateEnd: '',
    loginName: '',
    requestIp: '',
    accountId: '',
    creatorId: '',
    allTime: [],
    orgId: '',
    longitude: '',
    latitude: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('登录日志')
// const pageEnName = ref('Call')

const searchForm = ref<REQUEST_POST_LOGINLOG_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_LOGINLOG_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const orgNameOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 60, title: '序号', dataIndex: 'rank', slotName: 'rank' },
  { width: 180, title: '账号ID', dataIndex: 'accountId' },
  { width: 120, tooltip: true, ellipsis: true, title: '账号名称', dataIndex: 'employeeName' },
  { width: 120, align: 'center', title: '登录名', dataIndex: 'loginName' },
  { width: 220, tooltip: true, ellipsis: true, align: 'center', title: '所属组织名称', dataIndex: 'orgName' },
  { width: 160, title: '登录IP', dataIndex: 'requestIp' },
  { width: 160, title: '登录位置', dataIndex: 'location', slotName: 'location', align: 'center' },
  { width: 180, title: '操作时间', dataIndex: 'createTime' }
])

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.optDateStart = timeArr[0] || ''
    searchForm.value.optDateEnd = timeArr[1] || ''
  } else {
    searchForm.value.optDateStart = ''
    searchForm.value.optDateEnd = ''
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getLoginLogList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getMediationOrganizationList = async () => {
  let orgRes = await getOrg()
  if (orgRes?.length) {
    orgNameOptions.value = orgRes.map((item) => ({ label: item.orgName, value: item.orgId }))
  }
}

getMediationOrganizationList()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.location-link {
  color: #1890ff;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }

  &:visited {
    color: #722ed1;
  }
}

.no-location {
  color: #999;
  font-style: italic;
}
</style>
