<template>
  <a-modal
    width="1200px"
    title="功能配置"
    :visible="visible"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      ref="companySettingFormRef"
      :model="companySettingForm"
      :label-col-props="{ span: 4 }"
      :wrapper-col-props="{ span: 20 }"
      label-align="left"
    >
      <a-form-item field="aiSwitch" label="AI功能开关">
        <a-switch v-model="companySettingForm.aiSwitch" />
      </a-form-item>

      <!-- IP白名单配置 -->
      <a-form-item label="登录IP白名单">
        <div class="table-container">
          <a-table
            :data="companySettingForm.ipWhitelistList"
            :columns="ipWhitelistColumns"
            :pagination="false"
            :bordered="true"
            size="small"

          >
            <template #name="{ record, rowIndex }">
              <a-input
                v-model="record.name"
                placeholder="请输入名称"
                :error="getFieldStatus('ipWhitelistList', rowIndex, 'name')"
                @blur="validateField('ipWhitelistList', rowIndex, 'name', record.name)"
              />
            </template>
            <template #ip="{ record, rowIndex }">
              <a-input
                v-model="record.ip"
                placeholder="***********或***********/24"
                :error="getFieldStatus('ipWhitelistList', rowIndex, 'ip')"
                @blur="validateField('ipWhitelistList', rowIndex, 'ip', record.ip)"
              />
            </template>
            <template #operations="{ rowIndex }">
              <a-button
                type="text"
                size="small"
                status="danger"
                @click="removeIpWhitelist(rowIndex)"
              >
                删除
              </a-button>
            </template>
          </a-table>
          <a-button
            type="dashed"
            @click="addIpWhitelist"
            style="width: 100%; margin-top: 8px"
          >
            <template #icon>
              <icon-plus />
            </template>
            添加IP白名单
          </a-button>
        </div>
      </a-form-item>

      <!-- 白名单位置配置 -->
      <a-form-item label="白名单位置">
        <div class="table-container">
          <a-table
            :data="companySettingForm.whiteLocationList"
            :columns="whiteLocationColumns"
            :pagination="false"
            :bordered="true"
            size="small"
          >
            <template #name="{ record, rowIndex }">
              <a-input
                v-model="record.name"
                placeholder="请输入名称"
                :error="getFieldStatus('whiteLocationList', rowIndex, 'name')"
                @blur="validateField('whiteLocationList', rowIndex, 'name', record.name)"
              />
            </template>
            <template #longitude="{ record, rowIndex }">
              <a-input
                v-model="record.longitude"
                placeholder="经度(-180到180)"
                :error="getFieldStatus('whiteLocationList', rowIndex, 'longitude')"
                @blur="validateField('whiteLocationList', rowIndex, 'longitude', record.longitude)"
              />
            </template>
            <template #latitude="{ record, rowIndex }">
              <a-input
                v-model="record.latitude"
                placeholder="纬度(-90到90)"
                :error="getFieldStatus('whiteLocationList', rowIndex, 'latitude')"
                @blur="validateField('whiteLocationList', rowIndex, 'latitude', record.latitude)"
              />
            </template>
            <template #operations="{ rowIndex }">
              <a-button
                type="text"
                size="small"
                status="danger"
                @click="removeWhiteLocation(rowIndex)"
              >
                删除
              </a-button>
            </template>
          </a-table>
          <a-button
            type="dashed"
            @click="addWhiteLocation"
            style="width: 100%; margin-top: 8px"
          >
            <template #icon>
              <icon-plus />
            </template>
            添加白名单位置
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, computed, reactive, nextTick } from 'vue'
import { getByCompanyId, saveCompanySetting } from '@/api/arcoApi/systemManage/companySettingManage'
import { Message } from '@arco-design/web-vue'
import { IconPlus } from '@arco-design/web-vue/es/icon'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

const emit = defineEmits(['update:visible'])

const props = defineProps<{
  visible: boolean
  companyId: number|string
}>()

const companySettingForm = ref<REQUEST_COMPANY_SETTING_DATA_TYPE>({
  companyId: props.companyId,
  aiSwitch: true,
  ipWhitelistList: [],
  whiteLocationList: []
})

const companySettingFormRef = ref()

// 验证错误状态管理 - 使用 reactive 确保深层响应式
const validationErrors = reactive<Record<string, Record<number, Record<string, string>>>>({
  ipWhitelistList: {},
  whiteLocationList: {}
})

// IP白名单表格列定义
const ipWhitelistColumns = computed<TableColumnData[]>(() => [
  {
    title: '*名称',
    dataIndex: 'name',
    slotName: 'name',
    width: 200
  },
  {
    title: '*IP地址',
    dataIndex: 'ip',
    slotName: 'ip',
    width: 300
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 100,
    align: 'center'
  }
])

// 白名单位置表格列定义
const whiteLocationColumns = computed<TableColumnData[]>(() => [
  {
    title: '*名称',
    dataIndex: 'name',
    slotName: 'name',
    width: 150
  },
  {
    title: '*经度',
    dataIndex: 'longitude',
    slotName: 'longitude',
    width: 150
  },
  {
    title: '*纬度',
    dataIndex: 'latitude',
    slotName: 'latitude',
    width: 150
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 100,
    align: 'center'
  }
])

// IP地址验证函数
const isValidIp = (ip: string): boolean => {
  if (!ip.trim()) return false

  // IPv4地址验证
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  // CIDR格式验证 (如: ***********/24)
  const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/

  // IP范围验证 (如: ***********-*************)
  const rangeRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  return ipv4Regex.test(ip) || cidrRegex.test(ip) || rangeRegex.test(ip)
}

// 获取字段验证状态
const getFieldStatus = (listType: string, rowIndex: number, field: string) => {
  const error = validationErrors[listType]?.[rowIndex]?.[field]
  // 调试信息
  if (error) {
    console.log(`Field ${listType}[${rowIndex}].${field} has error: ${error}`)
  }
  return error ? 'error' : undefined
}

// 验证单个字段
const validateField = async (listType: string, rowIndex: number, field: string, value: string) => {
  // 确保嵌套对象存在
  if (!validationErrors[listType]) {
    validationErrors[listType] = {}
  }
  if (!validationErrors[listType][rowIndex]) {
    validationErrors[listType][rowIndex] = {}
  }

  let errorMessage = ''

  // 获取当前行的所有字段值来判断是否为完全空行
  const currentRow = listType === 'ipWhitelistList'
    ? companySettingForm.value.ipWhitelistList?.[rowIndex]
    : companySettingForm.value.whiteLocationList?.[rowIndex]

  if (!currentRow) return

  // 检查是否为完全空行
  const isEmptyRow = listType === 'ipWhitelistList'
    ? !currentRow.name?.trim() && !currentRow.ip?.trim()
    : !currentRow.name?.trim() && !currentRow.longitude?.trim() && !currentRow.latitude?.trim()

  // 如果是完全空行，不进行验证
  if (isEmptyRow) {
    // 清除该行的所有验证错误
    delete validationErrors[listType][rowIndex]
    return
  }

  // 对非空行进行验证
  if (listType === 'ipWhitelistList') {
    if (field === 'name') {
      if (!value || !value.trim()) {
        errorMessage = '请输入名称'
      }
    } else if (field === 'ip') {
      if (!value || !value.trim()) {
        errorMessage = '请输入IP地址'
      } else if (!isValidIp(value)) {
        errorMessage = '无效的IP地址格式，支持单个IP、CIDR格式或IP范围'
      }
    }
  } else if (listType === 'whiteLocationList') {
    if (field === 'name') {
      if (!value || !value.trim()) {
        errorMessage = '请输入名称'
      }
    } else if (field === 'longitude') {
      if (!value || !value.trim()) {
        errorMessage = '请输入经度'
      } else {
        const longitude = parseFloat(value)
        if (isNaN(longitude)) {
          errorMessage = '经度必须是数字'
        } else if (longitude < -180 || longitude > 180) {
          errorMessage = '经度范围必须在-180到180之间'
        }
      }
    } else if (field === 'latitude') {
      if (!value || !value.trim()) {
        errorMessage = '请输入纬度'
      } else {
        const latitude = parseFloat(value)
        if (isNaN(latitude)) {
          errorMessage = '纬度必须是数字'
        } else if (latitude < -90 || latitude > 90) {
          errorMessage = '纬度范围必须在-90到90之间'
        }
      }
    }
  }

  // 使用 nextTick 确保 DOM 更新
  await nextTick(() => {
    if (errorMessage) {
      validationErrors[listType][rowIndex][field] = errorMessage
    } else {
      delete validationErrors[listType][rowIndex][field]
      if (Object.keys(validationErrors[listType][rowIndex]).length === 0) {
        delete validationErrors[listType][rowIndex]
      }
    }
  })
}

// 验证所有字段
const validateAllFields = async (): Promise<boolean> => {
  let hasError = false

  // 清空之前的验证错误
  Object.assign(validationErrors, {
    ipWhitelistList: {},
    whiteLocationList: {}
  })

  // 验证IP白名单 - 只验证非空行
  const ipPromises = companySettingForm.value.ipWhitelistList?.map(async (item, index) => {
    // 检查是否为完全空行
    const isEmptyRow = !item.name?.trim() && !item.ip?.trim()
    if (!isEmptyRow) {
      await validateField('ipWhitelistList', index, 'name', item.name)
      await validateField('ipWhitelistList', index, 'ip', item.ip)
    }
  }) || []

  // 验证位置白名单 - 只验证非空行
  const locationPromises = companySettingForm.value.whiteLocationList?.map(async (item, index) => {
    // 检查是否为完全空行
    const isEmptyRow = !item.name?.trim() && !item.longitude?.trim() && !item.latitude?.trim()
    if (!isEmptyRow) {
      await validateField('whiteLocationList', index, 'name', item.name)
      await validateField('whiteLocationList', index, 'longitude', item.longitude)
      await validateField('whiteLocationList', index, 'latitude', item.latitude)
    }
  }) || []

  // 等待所有验证完成
  await Promise.all([...ipPromises, ...locationPromises])

  // 检查是否有验证错误
  Object.values(validationErrors).forEach(listErrors => {
    Object.values(listErrors).forEach(rowErrors => {
      if (Object.keys(rowErrors).length > 0) {
        hasError = true
      }
    })
  })

  return !hasError
}

// 添加IP白名单项
const addIpWhitelist = () => {
  companySettingForm.value.ipWhitelistList?.push({ name: '', ip: '' })
}

// 删除IP白名单项
const removeIpWhitelist = (index: number) => {
  if (companySettingForm.value.ipWhitelistList) {
    companySettingForm.value.ipWhitelistList.splice(index, 1)
    // 清除对应的验证错误
    if (validationErrors.ipWhitelistList[index]) {
      delete validationErrors.ipWhitelistList[index]
    }
    // 重新索引验证错误
    const newErrors: Record<number, Record<string, string>> = {}
    Object.keys(validationErrors.ipWhitelistList).forEach(key => {
      const keyNum = parseInt(key)
      if (keyNum > index) {
        newErrors[keyNum - 1] = validationErrors.ipWhitelistList[keyNum]
      } else if (keyNum < index) {
        newErrors[keyNum] = validationErrors.ipWhitelistList[keyNum]
      }
    })
    validationErrors.ipWhitelistList = newErrors
  }
}

// 添加白名单位置项
const addWhiteLocation = () => {
  companySettingForm.value.whiteLocationList?.push({ name: '', longitude: '', latitude: '' })
}

// 删除白名单位置项
const removeWhiteLocation = (index: number) => {
  if (companySettingForm.value.whiteLocationList) {
    companySettingForm.value.whiteLocationList.splice(index, 1)
    // 清除对应的验证错误
    if (validationErrors.whiteLocationList[index]) {
      delete validationErrors.whiteLocationList[index]
    }
    // 重新索引验证错误
    const newErrors: Record<number, Record<string, string>> = {}
    Object.keys(validationErrors.whiteLocationList).forEach(key => {
      const keyNum = parseInt(key)
      if (keyNum > index) {
        newErrors[keyNum - 1] = validationErrors.whiteLocationList[keyNum]
      } else if (keyNum < index) {
        newErrors[keyNum] = validationErrors.whiteLocationList[keyNum]
      }
    })
    validationErrors.whiteLocationList = newErrors
  }
}

const fetchCompanySetting = async () => {
  try {
    const res = await getByCompanyId(props.companyId)
    if (res) {
      companySettingForm.value = {
        ...res,
        // 允许数组为空
        ipWhitelistList: res.ipWhitelistList || [],
        whiteLocationList: res.whiteLocationList || []
      }
    }
    // 清空验证错误
    Object.assign(validationErrors, {
      ipWhitelistList: {},
      whiteLocationList: {}
    })
  } catch (error) {
    Message.error('获取设置失败')
  }
}

watch(
  () => props.companyId,
  (newCompanyId) => {
    if (newCompanyId) {
      companySettingForm.value.companyId = newCompanyId
      fetchCompanySetting()
    }
  }
)

const handleConfirm = async () => {
  // 验证表格中的所有字段
  if (!(await validateAllFields())) {
    Message.error('请检查表格中的输入项')
    return
  }

  // 验证基础表单
  try {
    const res = await companySettingFormRef.value.validate()
    if (res) {
      Message.error('表单验证失败')
      return
    }
  } catch (error) {
    Message.error('表单验证失败')
    return
  }

  // 过滤掉空的项目
  const filteredForm = {
    ...companySettingForm.value,
    ipWhitelistList: companySettingForm.value.ipWhitelistList?.filter(item =>
      item.name.trim() && item.ip.trim()
    ) || [],
    whiteLocationList: companySettingForm.value.whiteLocationList?.filter(item =>
      item.name.trim() && item.longitude.trim() && item.latitude.trim()
    ) || []
  }

  try {
    await saveCompanySetting(filteredForm)
    Message.success('保存设置成功')
    emit('update:visible', false)
  } catch (error) {
    Message.error('保存设置失败')
  }
}

const handleCancel = () => {
  // 清空验证错误
  Object.assign(validationErrors, {
    ipWhitelistList: {},
    whiteLocationList: {}
  })
  emit('update:visible', false)
}

</script>

<style scoped lang="scss">
.table-container {
  :deep(.arco-tooltip) {
    .arco-tooltip-content {
      background-color: #ff4d4f;
      color: white;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      max-width: 200px;
      word-wrap: break-word;
    }

    .arco-tooltip-arrow {
      border-top-color: #ff4d4f;
    }
  }

  // 错误状态的输入框样式
  :deep(.arco-input-wrapper.arco-input-error) {
    border-color: #ff4d4f;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }

  :deep(.arco-input-wrapper.arco-input-error:hover) {
    border-color: #ff7875;
  }

  :deep(.arco-input-wrapper.arco-input-error:focus-within) {
    border-color: #ff4d4f;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
}
</style>
