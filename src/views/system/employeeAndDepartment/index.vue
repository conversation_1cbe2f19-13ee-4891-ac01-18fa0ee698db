<template>
  <div class="ed-manage">
    <a-card class="ed-card" :bordered="false">
      <template #title>
        <span class="bold mr10">{{ companyName }}</span>
        <a-button type="primary" @click="handleToggoleCompany"> 切换</a-button>
      </template>
      <div class="ed-block">
        <div class="ed-menu">
          <div class="ed-menu-tree">
            <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
              <div class="ed-menu-tree-body">
                <div v-for="(item, index) in edList" :key="index" class="ed-menu-tree-item">
                  <div
                    class="item-row"
                    :class="{ 'is-active': activeItem.deptId === item.deptId }"
                    @click="handleClickRow(item)"
                  >
                    <a-tooltip :content="item.deptName">
                      <span class="item-row-text">{{ item.deptName }}</span>
                    </a-tooltip>
                  </div>
                  <div
                    v-for="(row, _i) in item.children"
                    :key="_i"
                    :class="{ 'is-active': activeItem.deptId === row.deptId }"
                    class="item-row"
                    @click="handleClickRow(row)"
                  >
                    <div class="row-text ml12">
                      <icon-folder class="f18" />
                      <a-tooltip :content="row.deptName">
                        <span class="item-row-text ml6">{{ row.deptName }}</span>
                      </a-tooltip>
                    </div>
                    <a-popover content-class="tree-popover-btn padding0" trigger="click" position="br">
                      <a-button type="text" @click.stop>
                        <icon-more-vertical />
                      </a-button>
                      <template #content>
                        <ul>
                          <li v-auth="['deptSave']" @click="handleEditDepartment(row)">编辑名称</li>
                          <li v-if="_i !== 0" @click="handleMoveUpDept(row, index, _i)">上移</li>
                          <li v-if="_i !== item.children.length - 1" @click="handleMoveDownDept(row, index, _i)">
                            下移
                          </li>
                          <li v-auth="['deptDelete']" class="danger" @click="handleDeptDelete(row)">删除</li>
                        </ul>
                      </template>
                    </a-popover>
                  </div>
                </div>
              </div>
            </a-scrollbar>
          </div>
          <a-space class="ed-menu-btn" :direction="'vertical'">
            <a-button type="outline" @click="handleAddDepartment">
              <template #icon>
                <icon-folder-add />
              </template>
              <template #default>新建部门</template>
            </a-button>

            <a-button type="outline" @click="handleCompanySetting">
              <template #icon>
                <icon-settings />
              </template>
              <template #default>功能配置</template>
            </a-button>
          </a-space>
        </div>
        <div ref="contentListRef" class="ed-content">
          <div ref="contentListHeaderRef" style="overflow: hidden">
            <a-row>
              <a-col :flex="1">
                <a-form
                  :model="searchForm"
                  :label-col-props="{ span: 7 }"
                  :wrapper-col-props="{ span: 17 }"
                  label-align="left"
                >
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <a-form-item field="employeeNumber" label="员工编号">
                        <a-input v-model="searchForm.employeeNumber" placeholder="请输入内容" allow-clear />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item field="employeeName" label="姓名">
                        <a-input v-model="searchForm.employeeName" placeholder="请输入内容" allow-clear />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item field="employeeMobile" label="手机号">
                        <a-input v-model="searchForm.employeeMobile" placeholder="请输入内容" allow-clear />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item field="roleIdList" label="员工角色">
                        <a-select
                          v-model="searchForm.roleIdList"
                          :options="rolesOptions"
                          placeholder="请选择"
                          allow-clear
                          multiple
                          :max-tag-count="1"
                          :field-names="{
                            value: 'roleId',
                            label: 'roleName'
                          }"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item field="accountStatus" label="账号状态">
                        <a-select
                          v-model="searchForm.accountStatus"
                          :options="accountStatusOptions"
                          placeholder="请选择"
                          allow-clear
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-form>
              </a-col>
              <a-divider style="height: 84px" direction="vertical" />
              <a-col :flex="'86px'" style="text-align: right">
                <a-space direction="vertical" :size="18">
                  <a-button type="primary" @click="search">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
            <a-divider style="margin-top: 0; margin-bottom: 16px" />
            <a-row style="margin-bottom: 16px">
              <a-col class="flex justify-between items-center" :span="24">
                <div class="tl">
                  <icon-user-group class="f18" />
                  <span class="ml6">员工数量：</span>
                  <span class="ml4">{{ pagination.total }}</span>
                </div>
                <div class="tr">
                  <a-button v-auth="['deptSave']" type="primary" @click="handleAddEmployee">
                    <template #icon>
                      <icon-plus />
                    </template>
                    <template #default>添加成员</template>
                  </a-button>
                </div>
              </a-col>
            </a-row>
          </div>
          <div ref="contentListTableRef">
            <a-table
              row-key="employeeId"
              :loading="loading"
              :pagination="pagination"
              :columns="columns"
              :data="renderData"
              :pageize="size"
              :scroll="{ x: '100%', y: '100%' }"
              @page-change="onPageChange"
              @page-size-change="onPageSizeChange"
            >
              <template #accountStatus="{ record }">
                <a-switch
                  v-model="record.accountStatus"
                  size="medium"
                  :checked-value="1"
                  :unchecked-value="0"
                  @change="handleAccountStatusChange($event, record)"
                />
              </template>
              <template #operat="{ record }">
                <a-button
                  v-auth="['employeeSave']"
                  class="pl6 pr6"
                  type="text"
                  size="mini"
                  @click="handleEditEmployee(record)"
                >
                  编辑
                </a-button>
                <a-button
                  v-auth="['employeeView']"
                  class="pl6 pr6"
                  type="text"
                  size="mini"
                  @click="handleResetPassword(record)"
                >
                  重置密码
                </a-button>
                <a-button
                  v-auth="['employeeDelete']"
                  class="pl6 pr6"
                  type="text"
                  status="danger"
                  size="mini"
                  @click="handleDeleteEmployee(record)"
                >
                  删除
                </a-button>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </a-card>
    <a-modal
      width="500px"
      title="企业切换"
      :visible="toggleVisible"
      @ok="handleToggleConfirm"
      @cancel="handleToggleCancel"
    >
      <a-form
        ref="toggoleFormRef"
        :model="toggoleForm"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <div class="form-title">企业类型</div>
        <a-form-item field="companyType">
          <a-radio-group v-model="toggoleForm.companyType" @change="handleCompanyTypeChange">
            <a-radio value="3">案源方</a-radio>
            <a-radio value="2">调解组织</a-radio>
            <a-radio value="1">平台运营方</a-radio>
          </a-radio-group>
        </a-form-item>
        <div v-if="toggoleForm.companyType !== '1'" class="form-title">选择企业</div>
        <a-form-item
          v-if="toggoleForm.companyType !== '1'"
          field="companyId"
          :label="platformOperator ? '平台运营方' : toggoleForm.companyType === '2' ? '调解组织' : '案源方'"
          :rules="[{ required: true, message: `请选择${toggoleForm.companyType === '2' ? '调解组织' : '案源方'}` }]"
        >
          <a-select
            v-model="toggoleForm.companyId"
            :options="toggoleForm.companyType === '2' ? orgOptions : companyOptions"
            placeholder="请选择"
            allow-clear
            @change="handleCompanyNameChange"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      width="500px"
      :title="deptForm.deptId ? '重命名' : '新建部门'"
      :visible="deptVisible"
      @ok="handleDeptConfirm"
      @cancel="handleDeptCancel"
    >
      <a-form
        ref="deptFormRef"
        :model="deptForm"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <a-form-item
          field="deptName"
          :label="deptForm.deptId ? '重命名' : '部门名称'"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model="deptForm.deptName" placeholder="请输入名称" allow-clear />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      width="600px"
      :title="employeeForm.employeeId ? '编辑员工' : '新增员工'"
      :visible="employeeVisible"
      @ok="handleEmployeeConfirm"
      @cancel="handleEmployeeCancel"
    >
      <a-form
        ref="employeeFormRef"
        :model="employeeForm"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <div class="form-title">员工基本信息</div>
        <a-form-item field="employeeName" label="员工姓名" :rules="[{ required: true, message: '请输入员工姓名' }]">
          <a-input v-model="employeeForm.employeeName" placeholder="请输入员工姓名" allow-clear />
        </a-form-item>
        <a-form-item field="employeeNumber" label="员工编号">
          <a-input v-model="employeeForm.employeeNumber" placeholder="请输入员工编号" allow-clear />
        </a-form-item>
        <a-form-item
          field="employeeMobile"
          label="手机号"
          :rules="[{ required: true, message: '请输入手机号' }, { validator: validateMobiles }]"
        >
          <a-input v-model="employeeForm.employeeMobile" placeholder="请输入手机号" allow-clear />
        </a-form-item>
        <a-form-item field="deptId" label="部门">
          <a-select
            v-model="employeeForm.deptId"
            :options="departmentOptions"
            :field-names="{ value: 'deptId', label: 'deptName' }"
            placeholder="请选择部门"
            allow-clear
          />
        </a-form-item>
        <a-form-item v-if="platformOperator" field="ipConfig" label="IP地址">
          <a-space direction="vertical" :size="10">
            <div class="options-add-btn" @click="handleAddIpAddress">
              <icon-plus :size="12" />
            </div>
            <a-table :pagination="false" :columns="ipcolumns" :scroll="{ maxHeight: '20vh' }" :data="ipData">
              <template #rank="{ rowIndex }"> {{ rowIndex < 9 ? '0' : '' }}{{ rowIndex + 1 }}</template>
              <template #ip="{ record }">
                <a-form-item field="ip" no-style>
                  <a-input v-model.trim="record.ip" size="mini" allow-clear placeholder="请输入" />
                </a-form-item>
              </template>
              <template #operations="{ record }">
                <a-button type="text" @click="handleDeleteIpAddress(record)">删除</a-button>
              </template>
            </a-table>
          </a-space>
        </a-form-item>
        <div class="form-title">角色信息</div>
        <a-form-item field="employeeRoleIds" label="角色选择">
          <a-select
            v-model="employeeForm.employeeRoleIds"
            :options="rolesOptions"
            :field-names="{ value: 'roleId', label: 'roleName' }"
            placeholder="请选择角色"
            allow-clear
            multiple
          />
        </a-form-item>
        <div class="form-title">账号信息</div>
        <a-form-item field="deptId" label="账号状态">
          <a-switch v-model="employeeForm.accountStatus" size="medium" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      width="500px"
      title="密码重置"
      :visible="passwordVisible"
      @ok="handlePasswordConfirm"
      @cancel="handlePasswordCancel"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="formRules"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <a-form-item field="employeeName" label="账号">
          <span>{{ passwordForm.employeeName }}</span>
        </a-form-item>
        <a-form-item field="newLoginPwd" label="新密码">
          <a-input-password v-model="passwordForm.newLoginPwd" placeholder="请输入新密码" allow-clear />
        </a-form-item>
        <a-form-item field="confirmPassword" label="确认密码">
          <a-input-password v-model="passwordForm.confirmPassword" placeholder="请输入确认密码" allow-clear />
        </a-form-item>
      </a-form>
    </a-modal>
    <CompanySetting
      :visible="companySettingVisible"
      :company-id="toggoleForm.companyId"
      @update:visible="companySettingVisible = $event"
    />
    <a-modal v-model:visible="checkVisible" @cancel="handleAccountStatusCancel">
      <template #title> 提示</template>
      <div style="text-align: center">当前账号为关联账号，请选择禁用范围</div>
      <template #footer>
        <div style="text-align: center" class="statue-operation">
          <a-button type="outline" size="small" @click="handleAccountStatusCancel()">取消</a-button>
          <a-button type="primary" size="small" @click="handleAccountStatusDisableAll()">禁用全部</a-button>
          <a-button type="primary" size="small" @click="handleAccountStatusDisableCurrent()">禁用当前</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { moveDept, updateEmployee, deleteDept, addEmployee } from '@/api/arcoApi/systemManage/employeeAndDepartment'
import {
  deptList,
  updateAccountStatus,
  checkRelevanceAccount,
  getEmployeeDetail
} from '@/api/arcoApi/systemManage/employeeAndDepartment'
import { listByCompanyType, addDept, updateDept } from '@/api/arcoApi/systemManage/employeeAndDepartment'
import { deleteEmployee, pageList, resetPwd } from '@/api/arcoApi/systemManage/employeeAndDepartment'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { ref, onMounted, nextTick, computed, reactive, onUnmounted } from 'vue'
import { listByGroupName } from '@/api/arcoApi/systemManage/roleManage'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { removeNullParam } from '@/utils/index'
import { useAppStore } from '@arco/store'
import _ from 'lodash'

import CompanySetting from './CompanySetting.vue'

const employeeFormModel = () => {
  return {
    accountStatus: true,
    employeeRoleIds: [],
    employeeNumber: '',
    employeeMobile: '',
    employeeName: '',
    employeeId: '',
    ipConfig: [],
    deptId: ''
  }
}

const employeeForm = ref<EMPLOYEE_DEPARTMENT_EDIT_FORM_TYPE>(employeeFormModel())
const departmentOptions = ref<SelectOptionData[]>([])
const platformOptions = ref<SelectOptionData[]>([])
const companyOptions = ref<SelectOptionData[]>([])
const orgOptions = ref<SelectOptionData[]>([])
const employeeFormRef = ref<FormInstance>()
const employeeVisible = ref<boolean>(false)
const toggleVisible = ref<boolean>(false)
const ipData = ref<{ ip: string }[]>([])
const appStore = useAppStore()
const companyName = ref('')

const oldToggoleForm = ref({ companyType: '3', companyName: '', companyId: '' })
const toggoleForm = ref({ companyType: '3', companyName: '', companyId: '' })
const toggoleFormRef = ref<FormInstance>()
const ipcolumns = computed<TableColumnData[]>(() => [
  { width: 50, title: '序号', dataIndex: 'rank', slotName: 'rank' },
  { width: 220, title: 'IP地址', dataIndex: 'ip', slotName: 'ip' },
  { width: 80, align: 'center', title: '操作', dataIndex: 'operations', slotName: 'operations' }
])

const platformOperator = computed(() => toggoleForm.value.companyType === '1')

const handleAddIpAddress = () => ipData.value.push({ ip: '' })

const handleDeleteIpAddress = (index: number) => ipData.value.splice(index, 1)

const pwValidatRegExp = computed(() => new RegExp(appStore.pwRegExpConf.pwRegExp))
const pwValidatRegTip = computed(() => appStore.pwRegExpConf.tipText)

const getListByCompanyType = async () => {
  let res = await listByCompanyType({ companyType: '3' })
  if (res && res.length) {
    companyOptions.value = res.map((item) => ({ label: item.companyName, value: item.companyId }))
  }
}
const getListByCompanyTypeForOrg = async () => {
  let res = await listByCompanyType({ companyType: '2' })
  if (res && res.length) {
    orgOptions.value = res.map((item) => ({ label: item.companyName, value: item.companyId }))
  }
}

const getListByCompanyTypeForPlatform = async () => {
  let res = await listByCompanyType({ companyType: '1' })
  if (res && res.length) {
    platformOptions.value = res.map((item) => ({ label: item.companyName, value: item.companyId }))
  }
}
// 获取部门
const getDepartmentList = async () => {
  let res = await deptList(toggoleForm.value.companyId)
  edList.value = [
    {
      deptName: toggoleForm.value.companyName,
      deptId: toggoleForm.value.companyId,
      children: _.cloneDeep(res)
    }
  ]
  departmentOptions.value = _.cloneDeep(res)
  if (edList.value.length) {
    activeItem.value = {
      deptId: edList.value[0].deptId,
      deptName: edList.value[0].deptName
    }
    search()
  }
}

const initCompany = async () => {
  await getListByCompanyType()
  if (!toggoleForm.value.companyName && companyOptions.value.length) {
    toggoleForm.value.companyName = companyOptions.value[0].label || ''
    toggoleForm.value.companyId = companyOptions.value[0].value as string
    companyName.value = companyOptions.value[0].label || ''
    getDepartmentList()
    getRolesList()
  }
}

initCompany()

const handleToggoleCompany = async () => {
  if (!orgOptions.value.length) {
    await getListByCompanyTypeForOrg()
  }
  if (!platformOptions.value.length) {
    await getListByCompanyTypeForPlatform()
  }
  oldToggoleForm.value = _.cloneDeep(toggoleForm.value)
  toggleVisible.value = true
}

const handleCompanyTypeChange = () => {
  toggoleForm.value.companyId = ''
  toggoleForm.value.companyName = ''
}

const handleCompanyNameChange = () => {
  if (!toggoleForm.value.companyId) {
    toggoleForm.value.companyName = ''
    return false
  }
  let companyList: any[] = []
  switch (toggoleForm.value.companyType) {
    case '1':
      companyList = _.cloneDeep(platformOptions.value)
      break
    case '2':
      companyList = _.cloneDeep(orgOptions.value)
      break
    case '3':
      companyList = _.cloneDeep(companyOptions.value)
      break
    default:
      break
  }
  for (let i = 0; i < companyList.length; i++) {
    const element = companyList[i]
    if (element.value === toggoleForm.value.companyId) {
      toggoleForm.value.companyName = element.label
      break
    }
  }
}

const handleToggleConfirm = async () => {
  const res = await toggoleFormRef.value?.validate()
  if (!res) {
    if (platformOperator.value) {
      if (!platformOptions.value.length) {
        Message.warning('平台运营方未配置企业，请联系管理员')
        return false
      }
      toggoleForm.value.companyId = platformOptions.value[0].value as string
      toggoleForm.value.companyName = platformOptions.value[0].label || ''
    }
    companyName.value = toggoleForm.value.companyName
    toggleVisible.value = false
    getDepartmentList()
    getRolesList()
  }
  return false
}

const handleToggleCancel = () => {
  toggoleForm.value = _.cloneDeep(oldToggoleForm.value)
  toggoleFormRef.value?.clearValidate()
  toggleVisible.value = false
}

const activeItem = ref<any>({ deptId: '', deptName: '' })
const edList = ref<any>([])
// 人员列表
type SizeProps = 'mini' | 'small' | 'medium' | 'large'
const searchFormModel = () => {
  return {
    employeeNumber: '',
    employeeName: '',
    employeeMobile: '',
    roleIdList: [],
    accountStatus: ''
  }
}
const { loading, setLoading } = useLoading(true)
const searchForm = ref<EMPLOYEE_DEPARTMENT_SEARCH_FORM_TYPE>(searchFormModel())
const renderData = ref<REQUEST_GET_EMPLOYEE_DEPARTMENT_LIST_TYPE[]>([])
const size = ref<SizeProps>('medium')
const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}
const pagination = reactive({ ...basePagination })
const columns = computed<TableColumnData[]>(() => [
  { width: 140, fixed: 'left', title: '姓名', dataIndex: 'employeeName' },
  { width: 140, title: '员工编号', dataIndex: 'employeeNumber' },
  { width: 160, title: '联系手机号', dataIndex: 'employeeMobile' },
  { width: 150, tooltip: true, ellipsis: true, title: '角色', dataIndex: 'roleNames' },
  { width: 140, tooltip: true, ellipsis: true, title: '部门', dataIndex: 'deptName' },
  { width: 120, title: '账号状态', dataIndex: 'accountStatus', slotName: 'accountStatus' },
  { width: 180, align: 'center', fixed: 'right', title: '操作', dataIndex: 'operat', slotName: 'operat' }
])
const rolesOptions = ref<SelectOptionData[]>([])
const accountStatusOptions = ref<SelectOptionData[]>([
  { label: '启用', value: 1 },
  { label: '停用', value: 0 }
])
const getRolesList = async () => {
  let res: any = await listByGroupName({ groupName: toggoleForm.value.companyType })
  rolesOptions.value = res
}
const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    let params = removeNullParam({
      ...searchForm.value,
      companyId: toggoleForm.value.companyId
    })
    if (activeItem.value.deptId !== toggoleForm.value.companyId) {
      params.deptId = activeItem.value.deptId
    }
    const data: any = await pageList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: params
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
const search = () => {
  pagination.current = 1
  getTableData({ ...pagination })
}
const reset = () => {
  searchForm.value = searchFormModel()
}
const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}
const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}
const checkVisible = ref(false)
let checkCurrentData: any = null
// 取消修改账号状态
const handleAccountStatusCancel = () => {
  for (let item of renderData.value) {
    if (checkCurrentData && checkCurrentData.employeeId == item.employeeId) {
      item.accountStatus = 1
      checkVisible.value = false
      break;
    }
  }
}
// 禁用账号
function disableAccount(disableRelevance?:boolean){
  let row :any = null;
  for (let item of renderData.value) {
    if (checkCurrentData && checkCurrentData.employeeId == item.employeeId) {
      row = item;
      break;
    }
  }
  let params = {
    accountId: checkCurrentData.accountId,
    accountStatus: 0,
    disableRelevance,
    employeeNumber: checkCurrentData.employeeNumber
  }
  updateAccountStatus(params)
    .then(() => {
      checkVisible.value = false
      Message.success('修改账号状态成功')
    })
    .catch((err: any) => {
      Message.success(err.message)
      row.accountStatus = 1;
    })
}
// 禁用全部
const handleAccountStatusDisableAll = () => {
  disableAccount(true)
  // 设置table中相同员工编号的记录设置禁用
  for (let item of renderData.value) {
    if (checkCurrentData && checkCurrentData.employeeNumber == item.employeeNumber) {
      item.accountStatus = 0;
    }
  }
}
// 禁用当前
const handleAccountStatusDisableCurrent = () => {
  disableAccount()
}
const handleAccountStatusChange = async (val: string | number | boolean, row: any) => {
  checkCurrentData = row
  let params = {
    accountId: row.accountId,
    accountStatus: val
  }
  let check = true
  // 有用户编号，切是状态设为禁用是，要校验
  if (row.employeeNumber && val === 0) {
    await checkRelevanceAccount(row.employeeNumber)
      .then((res: any) => {
        if (res) {
          // 返回true代表有多个关联用户
          checkVisible.value = true
          check = false
        }
      })
      .catch((err) => {
        Message.success(err.message)
        row.accountStatus = !val
      })
  }
  if (check) {
    updateAccountStatus(params)
      .then(() => {
        Message.success('修改账号状态成功')
      })
      .catch((err: any) => {
        Message.success(err.message)
        row.accountStatus = !val
      })
  }
}
// 人员新增或编辑
const validateMobiles = (value: string, callback: Function) => {
  const phoneReg = /^([1][0-9]{10})*$/
  if (value) {
    if (!phoneReg.test(value)) {
      callback(new Error('请输入正确的手机号码'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const handleAddEmployee = () => {
  employeeVisible.value = true
}
const handleEditEmployee = async (row: any) => {
  let res: any = await getEmployeeDetail(row.employeeId)
  employeeForm.value = {
    employeeRoleIds: res.employeeRoleIds || [],
    accountStatus: res.accountStatus || 0,
    employeeNumber: res.employeeNumber,
    employeeMobile: res.employeeMobile,
    employeeName: res.employeeName,
    ipConfig: res.ipConfig || [],
    employeeId: res.employeeId,
    deptId: res.deptId
  }
  ipData.value = employeeForm.value.ipConfig.map((ip) => ({ ip }))
  employeeVisible.value = true
}
const handleEmployeeConfirm = async () => {
  const res = await employeeFormRef.value?.validate()
  if (!res) {
    employeeForm.value.ipConfig = ipData.value.map((item) => item.ip)
    let params = removeNullParam({ ...employeeForm.value, companyId: toggoleForm.value.companyId })
    if (params.employeeId) {
      await updateEmployee(params)
      Message.success('修改员工成功')
    } else {
      await addEmployee(params)
      Message.success('新增员工成功')
    }
    handleEmployeeCancel()
    search()
  }
  return false
}
const handleEmployeeCancel = () => {
  employeeForm.value = employeeFormModel()
  employeeFormRef.value?.clearValidate()
  employeeVisible.value = false
  ipData.value = []
}
// 重置密码
const passwordFormModel = () => {
  return {
    accountId: '',
    employeeName: '',
    newLoginPwd: '',
    confirmPassword: ''
  }
}

const passwordForm = ref<EMPLOYEE_DEPARTMENT_PASSWORD_FORM_TYPE>(passwordFormModel())
const passwordFormRef = ref<FormInstance>()
const passwordVisible = ref<boolean>(false)

const confirmPassworddValid = (value: string, cb: Function) => {
  if (passwordForm.value.newLoginPwd) {
    if (value === passwordForm.value.newLoginPwd) cb()
    else cb('两次输入的密码不一致')
  } else cb()
}

const newLoginPwdValid = (value: string, cb: Function) => {
  if (passwordForm.value.confirmPassword) {
    if (value === passwordForm.value.confirmPassword) cb()
    else cb('两次输入的密码不一致')
  } else cb()
}

const validatePwd = (value: string, cb: Function) => {
  if (!pwValidatRegExp.value.test(value)) cb(pwValidatRegTip.value)
  else cb()
}

const formRules = ref({
  newLoginPwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { validator: newLoginPwdValid, trigger: 'blur' },
    { validator: validatePwd, trigger: 'change' }
  ],
  confirmPassword: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    { validator: confirmPassworddValid, trigger: 'blur' }
  ]
})

const handleResetPassword = async (row: any) => {
  passwordForm.value.accountId = row.accountId
  passwordForm.value.employeeName = row.employeeName
  passwordVisible.value = true
}
const handlePasswordConfirm = async () => {
  const res = await passwordFormRef.value?.validate()
  if (!res) {
    let params = {
      accountId: passwordForm.value.accountId,
      newLoginPwd: passwordForm.value.newLoginPwd
    }
    await resetPwd(params)
    Message.success('重置密码成功')
    handlePasswordCancel()
  }
  return false
}
const handlePasswordCancel = () => {
  passwordForm.value = passwordFormModel()
  passwordFormRef.value?.clearValidate()
  passwordVisible.value = false
}
// 删除人员
const handleDeleteEmployee = async (row: any) => {
  Modal.warning({
    title: '提示',
    content: '是否确定删除该人员？',
    hideCancel: false,
    onOk: async () => {
      let res: any = await deleteEmployee(row.employeeId)
      if (res.success === false) {
        Modal.error({
          title: '删除人员失败',
          content: res.message
        })
      } else {
        Message.success('删除人员成功')
        search()
      }
    }
  })
}
// 点击部门
const handleClickRow = (row: any) => {
  activeItem.value = { deptId: row.deptId, deptName: row.deptName }
  search()
}
// 部门设置
const deptForm = ref<any>({ deptId: '', deptName: '' })
const deptVisible = ref<boolean>(false)
const deptFormRef = ref<FormInstance>()
const handleAddDepartment = () => {
  deptVisible.value = true
}
const handleEditDepartment = (row: any) => {
  deptForm.value = { deptId: row.deptId, deptName: row.deptName }
  deptVisible.value = true
}
const handleDeptConfirm = async () => {
  const res = await deptFormRef.value?.validate()
  if (!res) {
    let params: any = {
      companyId: toggoleForm.value.companyId,
      deptName: deptForm.value.deptName
    }
    if (deptForm.value.deptId) {
      params.deptId = deptForm.value.deptId
      await updateDept(params)
      Message.success('修改部门成功')
    } else {
      await addDept(params)
      Message.success('新增部门成功')
    }
    handleDeptCancel()
    getDepartmentList()
  }
  return false
}
const handleDeptCancel = () => {
  deptForm.value = { deptId: '', deptName: '' }
  deptFormRef.value?.clearValidate()
  deptVisible.value = false
}
const handleDeptDelete = (row: any) => {
  Modal.warning({
    title: '提示',
    content: '是否确定删除该部门？',
    hideCancel: false,
    onOk: async () => {
      let res: any = await deleteDept(row.deptId)
      if (res.success === false) {
        Modal.error({
          title: '删除部门失败',
          content: res.message
        })
      } else {
        Message.success('删除部门成功')
        getDepartmentList()
      }
    }
  })
}
const handleMoveUpDept = async (row: any, index: number, rowIndex: number) => {
  Modal.warning({
    title: '提示',
    content: '是否确定上移该部门？',
    hideCancel: false,
    onOk: async () => {
      let preItem: any = edList.value[index].children[rowIndex - 1]
      let params: any = {
        moveDownDeptId: preItem.deptId,
        moveDownSort: preItem.sort,
        moveUpDeptId: row.deptId,
        moveUpSort: row.sort
      }
      await moveDept(params)
      Message.success('上移部门成功')
      getDepartmentList()
    }
  })
}
const handleMoveDownDept = async (row: any, index: number, rowIndex: number) => {
  Modal.warning({
    title: '提示',
    content: '是否确定下移该部门？',
    hideCancel: false,
    onOk: async () => {
      let nextItem: any = edList.value[index].children[rowIndex + 1]
      let params: any = {
        moveDownDeptId: row.deptId,
        moveDownSort: row.sort,
        moveUpDeptId: nextItem.deptId,
        moveUpSort: nextItem.sort
      }
      await moveDept(params)
      Message.success('下移部门成功')
      getDepartmentList()
    }
  })
}

const companySettingVisible = ref<boolean>(false)

const handleCompanySetting = () => {
  if (toggoleForm.value.companyId) {
    companySettingVisible.value = true
  } else {
    Message.warning('请选择企业')
  }
}

// 高度设置
const contentListHeaderRef = ref<HTMLDivElement>()
const contentListTableRef = ref<HTMLDivElement>()
const contentListRef = ref<HTMLDivElement>()

const setTableHeight = () => {
  let oH = (contentListRef.value!.offsetHeight || 0) - (contentListRef.value!.offsetHeight || 0) - 32
  contentListRef.value!.style.height = oH + 'px'
}

onMounted(() => {
  nextTick(() => {
    setTableHeight()
    window.addEventListener('resize', setTableHeight)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', setTableHeight)
})
</script>

<style scoped lang="scss">
.statue-operation button {
  margin: 0px 8px;
}

.ed {
  &-manage {
    display: flex;
  }

  &-card {
    width: 100%;
    height: 100%;

    :deep(.arco-card-body) {
      padding: 0;
      height: calc(100% - 46px);
    }
  }

  &-block {
    width: 100%;
    height: 100%;
    display: flex;
  }

  &-menu {
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dedede;

    &-tree {
      padding: 16px 0 0 16px;
      flex-grow: 1;
      height: 400px;

      &-body {
        padding-right: 16px;
      }

      &-item {
        .item-row {
          display: flex;
          padding-left: 16px;
          height: 32px;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          border-radius: 4px;
          cursor: pointer;

          &.is-active,
          &:hover,
          &:focus,
          &:active {
            background-color: lighten($color: $color-primary, $amount: 60%);
            color: $color-primary;
            font-weight: bold;
          }

          &-text {
            display: inline-block;
            width: calc(100% - 24px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .row-text {
            flex-grow: 1;
            height: 32px;
            display: flex;
            align-items: center;
            width: calc(100% - 70px);
          }

          .arco-btn-text {
            color: #303030;
          }
        }
      }
    }

    &-btn {
      padding: 16px;

      .arco-btn {
        width: 100%;
      }
    }
  }

  &-content {
    flex-grow: 1;
    padding: 16px;
    width: calc(100% - 280px);
    overflow-y: scroll;
  }
}

.form-title {
  display: flex;
  height: 20px;
  align-items: center;
  border-left: 4px solid $color-primary;
  font-size: 15px;
  font-weight: bold;
  padding-left: 10px;
  margin-bottom: 16px;
}

.tree-popover-btn {
  ul {
    margin: 4px 0 8px 0;
    list-style: none;
    padding: 0;
    width: 100px;

    li {
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      & + li {
        border-top: 1px solid #dedede;
      }

      &:hover,
      &:focus,
      &:active {
        background-color: lighten($color: $color-primary, $amount: 60%);
        color: $color-primary;
        font-weight: bold;
      }
    }
  }
}
</style>
