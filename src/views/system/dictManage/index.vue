<template>
  <div class="dict-manage">
    <a-card class="dict-card" :bordered="false">
      <template #title>
        <span class="bold mr10">系统字典管理</span>
      </template>
      <div class="dict-block">
        <div class="dict-menu">
          <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
            <div class="dict-menu-body">
              <div class="mb12">
                <a-input
                  v-model="searchForm.dictName"
                  placeholder="请输入内容并点击Enter查询"
                  allow-clear
                  @press-enter="handleSearchDict"
                  @clear="handleSearchDict"
                >
                  <template #prefix>
                    <icon-search />
                  </template>
                </a-input>
              </div>
              <div
                v-for="(item, index) in dictList"
                :key="index"
                class="dict-menu-item"
                :class="{ 'is-active': activeItem.dictId === item.dictId }"
                @click="handleClickRow(item)"
              >
                <div class="row-text ml12">
                  <icon-bookmark class="f18" />
                  <a-tooltip :content="item.dictName">
                    <span class="item-row-text ml6">{{ item.dictName }}</span>
                  </a-tooltip>
                </div>
                <a-popover content-class="tree-popover-btn padding0" trigger="click" position="br">
                  <a-button type="text" @click.stop><icon-more-vertical /> </a-button>
                  <template #content>
                    <ul>
                      <li v-auth="['dictSave']" @click="handleEditDict(item)">编辑</li>
                    </ul>
                  </template>
                </a-popover>
              </div>
            </div>
          </a-scrollbar>
        </div>
        <div ref="dictListRef" class="dict-content">
          <div ref="dictListHeaderRef" style="overflow: hidden">
            <a-row class="mb16">
              <a-col class="flex justify-start items-center" :span="24">
                <a-button v-auth="['dictSave']" type="primary" @click="handleAddDictValue">
                  <template #icon>
                    <icon-plus />
                  </template>
                  <template #default>新增字典值</template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          <div ref="dictListTableRef">
            <a-table
              row-key="dictDataId"
              :loading="loading"
              :columns="columns"
              :data="renderData"
              :pagination="false"
              :scroll="{
                x: '100%',
                y: '100%'
              }"
            >
              <template #enableFlag="{ record }">
                <span v-if="record.enableFlag" class="success">正常</span>
                <span v-else class="danger">禁用</span>
              </template>
              <template #operations="{ record }">
                <a-button
                  v-auth="['dictSave']"
                  class="pl6 pr6"
                  type="text"
                  size="mini"
                  @click="handleEditDictValue(record)"
                >
                  编辑
                </a-button>
                <a-button
                  v-auth="['dictDelete']"
                  class="pl6 pr6"
                  type="text"
                  status="danger"
                  size="mini"
                  @click="handleDeleteDictValue(record)"
                >
                  删除
                </a-button>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </a-card>
    <a-modal
      width="600px"
      title="修改字典信息"
      :visible="dictEditVisible"
      @ok="handleDictEditConfirm"
      @cancel="handleDictEditCancel"
    >
      <a-form :model="dictEditForm" :label-col-props="{ span: 5 }" :wrapper-col-props="{ span: 19 }" label-align="left">
        <a-form-item field="dictName" label="字典名称">
          {{ dictEditForm.dictName }}
        </a-form-item>
				<a-form-item field="dictType" label="字典类型">
					{{ dictEditForm.dictType }}
				</a-form-item>
        <a-form-item field="remark" label="字典描述">
          <a-textarea v-model="dictEditForm.remark" placeholder="请输入字典描述" allow-clear />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      width="600px"
      :title="dictValueEditForm.dictDataId ? '更新字典值' : '新增字典值'"
      :visible="dictValueEditVisible"
      @ok="handleDictValueEditConfirm"
      @cancel="handleDictValueEditCancel"
    >
      <a-form
        ref="dictValueEditFormRef"
        :model="dictValueEditForm"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 19 }"
        label-align="left"
      >
        <a-form-item field="dictTag" label="参数键名称" :rules="[{ required: true, message: '请输入参数键名称' }]">
          <a-input v-model="dictValueEditForm.dictTag" placeholder="请输入参数键名称" allow-clear />
        </a-form-item>
        <a-form-item field="parentId" label="父级">
          <dict-data-select v-model="dictValueEditForm.parentId" :dict-type="activeItem.dictType" :model-value-witch="'id'" :check-strictly="true" @selected="selected"/>
        </a-form-item>
        <a-form-item field="enableFlag" label="状态">
          <a-switch v-model="dictValueEditForm.enableFlag" size="medium" />
        </a-form-item>
        <a-form-item field="remark" label="描述">
          <a-textarea v-model="dictValueEditForm.remark" placeholder="请输入描述" allow-clear />
        </a-form-item>
				<a-form-item field="businessType" label="业务类型">
<!--					<a-select-->
<!--						v-model="dictValueEditForm.businessType"-->
<!--						:options="businessTypeOptions"-->
<!--						placeholder="请选择"-->
<!--						allow-clear-->
<!--					/>-->
          <BusinessTypeSelect v-model="dictValueEditForm.businessType" :disabled="disabled"></BusinessTypeSelect>
				</a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>

import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import BusinessTypeSelect from '@/components/businessType/businessTypeSelect.vue'

import { pageList, update, listByDictId } from '@/api/arcoApi/systemManage/dictManage'
import { addVal, updateVal, deleteById } from '@/api/arcoApi/systemManage/dictManage'
import { ref, onMounted, nextTick, computed, onUnmounted } from 'vue'

import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { removeNullParam } from '@/utils/index'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
const businessTypeOptions = ref<SelectOptionData[]>(dict.businessType)

import _ from 'lodash'
import { useDictStore } from '@arco/store'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import dict, { businessType } from '@/dict/caseManage.ts'

const dictStore = useDictStore()

const { loading, setLoading } = useLoading(true)
const editFormModel = () => {
  return {
    dictId: null,
    dictName: '',
    dictStatus: '',
    dictType: '',
    remark: '',
    sysDictDataList: [],
    sysFlag: null
  }
}
const dictList = ref<REQUEST_GET_DICT_LIST_TYPE[]>([])
const activeItem = ref<REQUEST_GET_DICT_LIST_TYPE>(editFormModel())
const searchFormModel = () => {
  return {
    dictName: ''
  }
}
const searchForm = ref<DICT_MANAGE_SEARCH_FORM_TYPE>(searchFormModel())
// 获取字典类型
const getPageList = async () => {
  setLoading(true)
  try {
    let params = removeNullParam({
      ...searchForm.value
    })
    const data: any = await pageList({
      pageInfo: { size: 100, pageNumber: 1 },
      param: params
    })
    dictList.value = data.list
    if (dictList.value.length && !activeItem.value.dictId) {
      handleClickRow(dictList.value[0])
    }
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
getPageList()
const handleSearchDict = () => {
  activeItem.value = editFormModel()
  getPageList()
}
// 切换字典类型
const handleClickRow = (row: any) => {
  activeItem.value = _.cloneDeep(row)
  getTableData()
}

const columns = computed<TableColumnData[]>(() => [
  { width: 120, title: '键代码', dataIndex: 'dictKey' },
  { width: 170, tooltip: true, ellipsis: true, title: '字典标签', dataIndex: 'dictTag' },
  { width: 200, tooltip: true, ellipsis: true, title: '描述', dataIndex: 'remark' },
  { width: 200, tooltip: true, ellipsis: true, title: '业务类型', dataIndex: 'businessTypeName'},
  { width: 100, title: '状态', dataIndex: 'enableFlag', slotName: 'enableFlag' },
  {
    width: 130,
    align: 'center',
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations'
  }
])
const renderData = ref<REQUEST_GET_DICT_VALUE_LIST_TYPE[]>([])

//删除u所有空children
const cleanEmptyChildren = (list)=>{
	for (let e of list) {
		if(!e.children || e.children.length==0){
			delete e.children
		}else{
			cleanEmptyChildren(e.children)
		}
	}
}

// 获取字典数据值
const getTableData = async () => {
  let res: any = await listByDictId(activeItem.value.dictId)

	//过滤掉空children
	res = res || []
	cleanEmptyChildren(res)
  renderData.value = res;
}


// 编辑字典类型
const dictEditVisible = ref<boolean>(false)
const dictEditForm = ref<REQUEST_GET_DICT_LIST_TYPE>(editFormModel())
const handleEditDict = (item: any) => {
  dictEditForm.value = _.cloneDeep(item)
  dictEditVisible.value = true
}
const handleDictEditConfirm = async () => {
  await update(dictEditForm.value)
  Message.success('修改字典信息成功')
  handleDictEditCancel()
}
const handleDictEditCancel = () => {
  dictEditForm.value = editFormModel()
  dictEditVisible.value = false
}
// 新增或编辑字典数据值
const dictValueEditFormModel = () => {
  return {
    dictDataId: '',
		parentId: null,
    dictId: '',
    dictKey: null,
    dictTag: '',
    enableFlag: true,
    remark: ''
  }
}
const dictValueEditVisible = ref<boolean>(false)
const dictValueEditForm = ref<DICT_VALUE_EDIT_FORM_TYPE>(dictValueEditFormModel())
const dictValueEditFormRef = ref<FormInstance>()
const handleAddDictValue = () => {
  dictValueEditForm.value.dictId = activeItem.value.dictId
  dictValueEditVisible.value = true
  disabled.value = false;
}
const handleEditDictValue = (item: any) => {
  dictValueEditForm.value = _.cloneDeep(item)
  dictValueEditVisible.value = true
  if(dictValueEditForm.value.parentId || (item.children != null && item.children.length > 0)){
    disabled.value = true;
  }else{
    disabled.value = false;
  }
}
const handleDictValueEditConfirm = async () => {
  const res = await dictValueEditFormRef.value?.validate()
  if (!res) {
    let params: any = removeNullParam({
      ...dictValueEditForm.value
    })
    if (params.dictDataId) {
      await updateVal(params)
      Message.success('更新字典值成功')
    } else {
      await addVal(params)
      Message.success('新增字典值成功')
    }
		//清空缓存
		await dictStore.initializeDictData();

    handleDictValueEditCancel()
    getTableData()
  }
  return false
}
const handleDictValueEditCancel = () => {
  dictValueEditForm.value = dictValueEditFormModel()
  dictValueEditFormRef.value?.clearValidate()
  dictValueEditVisible.value = false
}
const disabled = ref(false);
function findDictDataById(data: REQUEST_GET_DICT_VALUE_LIST_TYPE[], dictDataId: number): REQUEST_GET_DICT_VALUE_LIST_TYPE | null {
  for (const item of data) {
    if (item.dictDataId === dictDataId) return item;
    if (item.children?.length) {
      const found = findDictDataById(item.children, dictDataId);
      if (found) return found;
    }
  }
  return null;
}
const selected = (value:any)=>{
  const dictData = findDictDataById(renderData.value,value);
  dictValueEditForm.value.businessType = dictData?.businessType;
  if(dictData?.businessType){
    disabled.value = true;
  }else{
    disabled.value = false;
  }
}
// 删除字典数据值
const handleDeleteDictValue = (item: any) => {
  Modal.warning({
    title: '提示',
    content: '是否确定删除该字典值？',
    hideCancel: false,
    onOk: async () => {
      let res: any = await deleteById(item.dictDataId)
      if (res.success === false) {
        Modal.error({
          title: '删除字典值失败',
          content: res.message
        })
      } else {
        Message.success('删除字典值成功')
        getTableData()
        // 重新初始化字典
        dictStore.initializeDictData();
      }
    }
  })
}

// 高度设置
const dictListRef = ref<HTMLDivElement>()
const dictListHeaderRef = ref<HTMLDivElement>()
const dictListTableRef = ref<HTMLDivElement>()

const setTableHeight = () => {
  let oH = (dictListRef.value!.offsetHeight || 0) - (dictListRef.value!.offsetHeight || 0) - 32
  dictListRef.value!.style.height = oH + 'px'
}

onMounted(() => {
  nextTick(() => {
    setTableHeight()
    window.addEventListener('resize', setTableHeight)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', setTableHeight)
})
</script>

<style scoped lang="scss">
.dict {
  &-manage {
    display: flex;
  }
  &-card {
    width: 100%;
    height: 100%;
    :deep(.arco-card-body) {
      padding: 0;
      height: calc(100% - 46px);
    }
  }
  &-block {
    width: 100%;
    height: 100%;
    display: flex;
  }
  &-menu {
    width: 280px;
    height: 100%;
    border-right: 1px solid #dedede;
    padding: 16px 0 0 16px;
    &-body {
      padding-right: 16px;
    }
    &-item {
      display: flex;
      padding-left: 16px;
      height: 32px;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      border-radius: 4px;
      cursor: pointer;
      &.is-active,
      &:hover,
      &:focus,
      &:active {
        background-color: lighten($color: $color-primary, $amount: 60%);
        color: $color-primary;
        font-weight: bold;
      }
      &-text {
        display: inline-block;
        width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .row-text {
        flex-grow: 1;
        height: 32px;
        display: flex;
        align-items: center;
        width: calc(100% - 70px);
      }
      .arco-btn-text {
        color: #303030;
      }
    }
  }
  &-content {
    flex-grow: 1;
    padding: 16px;
    width: calc(100% - 280px);
  }
}
.form-title {
  display: flex;
  height: 20px;
  align-items: center;
  border-left: 4px solid $color-primary;
  font-size: 15px;
  font-weight: bold;
  padding-left: 10px;
  margin-bottom: 16px;
}
.tree-popover-btn {
  ul {
    margin: 4px 0 8px 0;
    list-style: none;
    padding: 0;
    width: 100px;
    li {
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      & + li {
        border-top: 1px solid #dedede;
      }
      &:hover,
      &:focus,
      &:active {
        background-color: lighten($color: $color-primary, $amount: 60%);
        color: $color-primary;
        font-weight: bold;
      }
    }
  }
}
</style>
