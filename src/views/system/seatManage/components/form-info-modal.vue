<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        :rules="baseInfoRules"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">坐席管理</span>
          </div>
          <a-form-item field="companyType">
            <a-radio-group v-model="baseInfoForm.companyType" @change="handleCompanyTypeChange">
              <a-radio value="3">案源方</a-radio>
              <a-radio value="2">调解组织</a-radio>
              <a-radio value="1">平台运营方</a-radio>
            </a-radio-group>
          </a-form-item>
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">坐席信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item v-if="baseInfoForm.companyType === '3'" field="entrustsId" label="案源方">
                <a-select
                  v-model="baseInfoForm.entrustsId"
                  :options="entrustsOptions"
                  placeholder="请选择"
                  allow-clear
                  @change="handleOrgChange"
                />
              </a-form-item>
              <a-form-item v-if="baseInfoForm.companyType === '2'" field="orgId" label="调解组织">
                <a-select
                  v-model="baseInfoForm.orgId"
                  :options="orgNameOptions"
                  placeholder="请选择"
                  allow-clear
                  @change="handleOrgChange"
                />
              </a-form-item>
              <a-form-item v-if="baseInfoForm.companyType === '1'" field="plantFormId" label="平台方">
                <a-select
                  v-model="baseInfoForm.plantFormId"
                  :options="plantFormOptions"
                  placeholder="请选择"
                  allow-clear
                  @change="handleOrgChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item field="deptId" :label="deptText">
                <a-select
                  v-model="baseInfoForm.deptId"
                  :options="deptNameOptions"
                  :disabled="!formDisabled"
                  placeholder="请选择"
                  allow-clear
                  @change="handleDeptChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item field="accountId" :label="mediatorText">
                <a-select
                  v-model="baseInfoForm.accountId"
                  :options="mediatorOptions"
                  :disabled="!baseInfoForm.deptId"
                  placeholder="请选择"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { getAllEntrusts, getDeptInfoById } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import { deptList, getPlantFormCompany } from '@/api/arcoApi/systemManage/employeeAndDepartment'
import { getOrg, getDept, getMediator } from '@/api/arcoApi/caseManage/caseManage'
import { getListByDeptId } from '@/api/arcoApi/systemManage/employeeAndDepartment'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { RadioInstance } from '@arco-design/web-vue/es/radio'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { saveSeat } from '@/api/arcoApi/systemManage/seatManage'
import { Message } from '@arco-design/web-vue'
import { ref } from 'vue'
import _ from 'lodash'
import { computed } from 'vue'

interface Props {
  width?: string | number
  title: string
}

withDefaults(defineProps<Props>(), {
  width: '500px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    companyType: '3',
    agentNumber: '',
    accountName: '',
    plantFormId: '',
    entrustsId: '',
    agentPhone: '',
    agentEmail: '',
    agentNick: '',
    agentName: '',
    accountId: '',
    deptName: '',
    agentId: '',
    orgName: '',
    deptId: '',
    orgId: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_SEAT_SAVE_PARAM_TYPE>(generateFormModel())
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const plantFormOptions = ref<SelectOptionData[]>([])
const mediatorOptions = ref<SelectOptionData[]>([])
const deptNameOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const orgNameOptions = ref<SelectOptionData[]>([])

const deptEnum = { '3': '调解部门', '2': '调解团队', '1': '所属部门' }
const mediatorEnum = { '3': '部门成员', '2': '调解员', '1': '部门员工' }

const formDisabled = computed(() => {
  let { orgId, entrustsId, plantFormId } = baseInfoForm.value
  return orgId || entrustsId || plantFormId
})

const deptText = computed(() => {
  return deptEnum[baseInfoForm.value.companyType] || '部门'
})

const mediatorText = computed(() => {
  return mediatorEnum[baseInfoForm.value.companyType] || '人员'
})

const baseInfoRules = ref({
  accountId: [{ required: true, message: `请选择${mediatorText.value}` }],
  deptId: [{ required: true, message: `请选择调解${deptText.value}` }],
  plantFormId: [{ required: true, message: '请选择平台方' }],
  entrustsId: [{ required: true, message: '请选择案源方' }],
  orgId: [{ required: true, message: '请选择调解组织' }]
})

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    if (baseInfoForm.value.agentId) {
      okLoading.value = true
      saveSeat(baseInfoForm.value)
        .then(() => {
          Message.success('操作成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    } else {
      Message.error('腾讯云呼技能组agentId不存在！')
    }
  }
  return false
}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = async (modal: REQUEST_POST_SEAT_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
  if (!modal.companyType) {
    baseInfoForm.value.companyType = '3'
  }
  await getTypeOrgList(baseInfoForm.value.companyType)
  await getTypeDeptList()
  await getTypeMediatorList()
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

const handleOrgChange = () => {
  baseFormRef.value?.resetFields(['deptId', 'accountId'])
  mediatorOptions.value = []
  deptNameOptions.value = []
  getTypeDeptList()
}

const handleDeptChange = () => {
  baseFormRef.value?.resetFields(['accountId'])
  mediatorOptions.value = []
  getTypeMediatorList()
}

const handleCompanyTypeChange: RadioInstance['onChange'] = (value) => {
  baseFormRef.value?.resetFields(['deptId', 'accountId'])
  getTypeOrgList(value as string)
}

const getTypeOrgList = async (companyType: string) => {
  if (companyType) {
    let res
    baseInfoForm.value.companyType = companyType
    switch (companyType) {
      case '3':
        res = await getAllEntrusts()
        baseInfoForm.value.orgId = ''
        baseInfoForm.value.plantFormId = ''
        entrustsOptions.value = res.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
        break
      case '2':
        res = await getOrg()
        baseInfoForm.value.entrustsId = ''
        baseInfoForm.value.plantFormId = ''
        orgNameOptions.value = res.map((item) => ({ label: item.orgName, value: item.orgId }))
        break
      case '1':
        res = await getPlantFormCompany()
        baseInfoForm.value.orgId = ''
        baseInfoForm.value.entrustsId = ''
        plantFormOptions.value = res.map((item) => ({ label: item.companyName, value: item.companyId }))
        break
      default:
        break
    }
  }
}

const getTypeDeptList = async () => {
  let { companyType, orgId, entrustsId, plantFormId } = baseInfoForm.value
  if (formDisabled.value) {
    let res
    if (companyType === '3') res = await getDeptInfoById(entrustsId)
    if (companyType === '2') res = await getDept([orgId])
    if (companyType === '1') res = await deptList(plantFormId)
    if (res) {
      deptNameOptions.value = res.map((item) => ({ label: item.deptName, value: item.deptId }))
    }
  }
}

const getTypeMediatorList = async () => {
  let { deptId, companyType, plantFormId } = baseInfoForm.value
  if (deptId) {
    let res
    if (companyType === '3') res = await getMediator([deptId])
    if (companyType === '2') res = await getMediator([deptId])
    if (companyType === '1') res = await getListByDeptId({ deptId, companyId: plantFormId })
    if (res) {
      mediatorOptions.value = res.map((item) => ({ label: item.employeeName, value: item.accountId }))
    }
  }
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
