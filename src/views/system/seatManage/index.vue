<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="false"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        :scroll="{ maxHeight: '65vh' }"
      >
        <template #operations="{ record }">
          <a-space v-auth="['seatSave']">
            <a-button type="text" @click="handleEdit(record)">选择</a-button>
            <a-button v-if="record.accountId" type="text" @click="handleUnbind(record)"> 解绑 </a-button>
          </a-space>
        </template>
      </a-table>
      <FormInfoModal
        ref="formInfoRef"
        v-model:visible="modalVisible"
        :title="modalTitle"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { getSeatList, saveSeat } from '@/api/arcoApi/systemManage/seatManage'
import FormInfoModal from './components/form-info-modal.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { computed, ref, watch, nextTick } from 'vue'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const { loading, setLoading } = useLoading(true)
const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('坐席管理')
// const pageEnName = ref('Seat')

const renderData = ref<REQUEST_GET_SEAT_LIST_DATA_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof FormInfoModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const modalVisible = ref(false)

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, fixed: 'left', title: '坐席工号', dataIndex: 'agentNumber' },
  { width: 120, tooltip: true, ellipsis: true, title: '坐席姓名', dataIndex: 'agentNick' },
  { width: 120, tooltip: true, ellipsis: true, title: '坐席账号', dataIndex: 'agentId' },
  { width: 120, tooltip: true, ellipsis: true, title: '调解员', dataIndex: 'accountName' },
  { width: 120, tooltip: true, ellipsis: true, title: '所属团队', dataIndex: 'deptName' },
  { width: 120, tooltip: true, ellipsis: true, title: '所属调解组织', dataIndex: 'orgName' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const modalTitle = computed(() => {
  return `${pageZhName.value}-选择调解员`
})

const handleEdit = async (row: REQUEST_GET_SEAT_LIST_DATA_TYPE) => {
  if (row.agentId) {
    if (row) {
      modalType.value = 'edit'
      modalVisible.value = true
      nextTick(() => {
        formInfoRef.value?.setFormModel(row)
      })
    }
  }
}

const handleUnbind = (row: REQUEST_GET_SEAT_LIST_DATA_TYPE) => {
  Modal.warning({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `是否确认解绑该账号“${row.agentId}”？`,
    onOk: () => {
      let param: REQUEST_GET_SEAT_LIST_DATA_TYPE = _.cloneDeep(row)
      param.accountId = ''
      saveSeat(param)
        .then(() => {
          Message.success('操作成功')
          getTableData()
        })
        .catch(() => {
          Modal.error({ title: '解绑失败', content: `该坐席下正在使用中，请关联账号登出登出登录后再解绑！` })
        })
    },
    onCancel: () => {}
  })
}

const handleComfirm = () => {
  modalVisible.value = false
  getTableData()
}
const handleCancel = () => {
  modalVisible.value = false
}

const getTableData = async () => {
  setLoading(true)
  try {
    const data = await getSeatList()
    renderData.value = data
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
