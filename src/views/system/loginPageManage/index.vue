<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="appearanceName" label="名称">
                  <a-input v-model="searchForm.appearanceName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="appearanceUserName" label="所属用户">
                  <a-input v-model="searchForm.appearanceUserName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="appearanceEnableStatus" label="启用状态">
                  <a-select
                    v-model="searchForm.appearanceEnableStatus"
                    :options="appearanceEnableStatusptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="appearanceDomainName" label="域名">
                  <a-input v-model="searchForm.appearanceDomainName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="appearancePath" label="路径">
                  <a-input v-model="searchForm.appearancePath" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="updaterName" label="修改人">
                  <a-input v-model="searchForm.updaterName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button type="primary" @click="handleAdd">新建 </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #appearanceEnableStatus="{ record }">
          <a-switch
            :default-checked="record.appearanceEnableStatus === 1 ? true : false"
            checked-value="1"
            unchecked-value="0"
            @click="handleSwitchStatus(record.appearanceId)"
          ></a-switch>
        </template>
        <template #appearanceUrl="{ record }">
          <a-typography-text :copy-text="record.appearanceUrl" type="danger" ellipsis copyable>
            {{ record.appearanceUrl }}
          </a-typography-text>
        </template>

        <template #operations="{ record }">
          <a-button v-auth="[]" type="text" @click="handleConfig(record)">配置</a-button>
          <a-button v-auth="[]" type="text" @click="handleDelete(record)">删除</a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { updateLoginAppearancetate, findLoginAppearanceList } from '@/api/arcoApi/systemManage/loginPageManage'
import { deleteLoginAppearance } from '@/api/arcoApi/systemManage/loginPageManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'

import { Message, Modal } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'
import dict from '@/dict/systemManage'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    appearanceEnableStatus: '',
    appearanceDomainName: '',
    appearanceUserName: '',
    navigationName: '',
    appearanceName: '',
    appearancePath: '',
    appearanceUrl: '',
    loginBoxTitle: '',
    appearanceId: '',
    updaterName: '',
    brandTitle: '',
    fileList: [],
    tabName: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('登录页')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<REQUEST_POST_LOGINAPPEARANCE_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_LOGINAPPEARANCE_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const appearanceIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const appearanceEnableStatusptions = ref<SelectOptionData[]>(dict.appearanceEnableStatusptions)

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 210, tooltip: true, ellipsis: true, fixed: 'left', title: '名称', dataIndex: 'appearanceName' },
  { width: 160, tooltip: true, ellipsis: true, title: '所属用户', dataIndex: 'appearanceUserName' },
  { width: 140, tooltip: true, ellipsis: true, title: '路径', dataIndex: 'appearancePath' },
  { width: 180, tooltip: true, ellipsis: true, title: '域名', dataIndex: 'appearanceDomainName' },
  { width: 200, tooltip: true, ellipsis: true, title: '登录地址', slotName: 'appearanceUrl', align: 'center' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建人', dataIndex: 'creatorName' },
  { width: 180, title: '创建时间', dataIndex: 'createTime' },
  { width: 120, tooltip: true, ellipsis: true, title: '修改人', dataIndex: 'updaterName' },
  { width: 180, tooltip: true, ellipsis: true, title: '最后修改时间', dataIndex: 'updateTime' },
  { width: 120, title: '启用状态', dataIndex: 'appearanceEnableStatus', slotName: 'appearanceEnableStatus' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleAdd = () => {
  router.push({ name: `loginPageConf` })
}

const handleConfig = (row: REQUEST_GET_LOGINAPPEARANCE_LIST_DATA_TYPE) => {
  router.push({ path: `loginPageConf`, query: { id: row.appearanceId } })
}

const handleDelete = (row: REQUEST_GET_LOGINAPPEARANCE_LIST_DATA_TYPE) => {
  Modal.info({
    title: '提示',
    hideCancel: false,
    bodyStyle: { padding: '0 30px' },
    content: '确认删除该登录页配置？',
    onOk: () => {
      deleteLoginAppearance(row.appearanceId).then(() => {
        Message.success('操作成功')
        getTableData()
      })
    },
    onCancel: () => {}
  })
}

const handleSwitchStatus = (val: string | number) => {
  updateLoginAppearancetate(val).then(() => {
    Message.success('操作成功')
    getTableData()
  })
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  appearanceIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  appearanceIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await findLoginAppearanceList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
