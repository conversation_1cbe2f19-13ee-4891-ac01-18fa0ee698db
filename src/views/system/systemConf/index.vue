<template>
  <div class="conf-container">
    <a-card title="系统配置" :bordered="true">
      <template #title>
        <a-row class="conf-wrapper">
          <a-col :flex="6">系统配置</a-col>
          <a-col :span="18" class="mdt-col-flexend">
            <a-space :size="10">
              <a-button type="primary" @click="save">
                <template #icon><icon-save /> </template> 保存
              </a-button>
              <a-button @click="setSystemFormParam">
                <template #icon><icon-loop /> </template> 取消
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </template>
      <a-form
        ref="confRef"
        :model="confForm"
        :rules="formRules"
        label-align="left"
        :label-col-props="{ span: 4 }"
        :wrapper-col-props="{ span: 19 }"
        class="conf-card-container"
      >
        <!-- 电话脱敏 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 电话脱敏 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <div class="security-content">
              <a-typography-paragraph class="security-tip">
                <a-tag :color="confForm.isPhoneSign ? 'green' : 'red'" size="small">
                  {{ confForm.isPhoneSign ? '已开启' : '已关闭' }}
                </a-tag>
                <span class="ml10 security-info"> 开启后，将对所有用户案件下当事人电话信息进行脱敏处理 </span>
              </a-typography-paragraph>
            </div>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.isPhoneSign" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 密码强度设置 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 密码强度设置 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <a-typography-paragraph class="security-tip">
              <a-tag :color="confForm.isPasswordSetting ? 'green' : 'red'" size="small">
                {{ confForm.isPasswordSetting ? '已开启' : '已关闭' }}
              </a-tag>
              <span class="ml10 security-info"> 开启后，用户修改密码时将根据已配置规则进行密码强度效验 </span>
            </a-typography-paragraph>
            <a-form-item field="passwordContent" label="密码内容包含(多选)">
              <a-checkbox-group v-model="confForm.passwordContent" :options="mapMethodsOptions"> </a-checkbox-group>
            </a-form-item>
            <a-form-item field="passwordSize" label="密码位数(最小长度)">
              <a-input-number
                v-model="confForm.passwordSize"
                style="width: 300px"
                :min="6"
                :max="30"
                placeholder="请输入密码位数,最小为6位,最大为30位"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.isPasswordSetting" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 账号超时自动退出 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 账号超时自动退出 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <a-typography-paragraph class="security-tip">
              <a-tag :color="confForm.isTimeoutLogout ? 'green' : 'red'" size="small">
                {{ confForm.isTimeoutLogout ? '已开启' : '已关闭' }}
              </a-tag>
              <span class="ml10 security-info"> 开启后，用户登录状态超过时限后将自动登出系统 </span>
            </a-typography-paragraph>
            <a-form-item field="logoutTime" label="超时时间">
              <a-input-number
                v-model="confForm.logoutTime"
                :min="10"
                placeholder="请输入超时时间，最小限定为10分钟"
                style="width: 300px"
              >
                <template #append><a-typography-text type="warning">(分钟)</a-typography-text></template>
              </a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.isTimeoutLogout" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 密码多次错误自动锁定 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 密码多次错误自动锁定 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <a-typography-paragraph class="security-tip">
              <a-tag :color="confForm.passwordErrorLock ? 'green' : 'red'" size="small">
                {{ confForm.passwordErrorLock ? '已开启' : '已关闭' }}
              </a-tag>
              <span class="ml10 security-info">
                开启后，用户尝试登录系统时，一旦超出限定登录次数，该账号自动锁定，直至解锁时间结束自动解锁
              </span>
            </a-typography-paragraph>
            <a-row :gutter="10">
              <a-col :span="12">
                <a-form-item
                  :label-col-props="{ span: 8 }"
                  :wrapper-col-props="{ span: 14 }"
                  field="passwordErrorNumber"
                  label="错误次数"
                >
                  <a-input-number
                    v-model="confForm.passwordErrorNumber"
                    :min="1"
                    placeholder="请输入错误次数，最小设置1此"
                    style="width: 300px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  :label-col-props="{ span: 8 }"
                  :wrapper-col-props="{ span: 14 }"
                  field="autoUnlockTime"
                  label="自动解锁时间"
                >
                  <a-input-number v-model="confForm.autoUnlockTime" :min="0" placeholder="请输入" style="width: 300px">
                    <template #append><a-typography-text type="warning">(分钟)</a-typography-text></template>
                  </a-input-number>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.passwordErrorLock" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 定期更换密码 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 定期更换密码 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <a-typography-paragraph class="security-tip">
              <a-tag :color="confForm.regularPasswordUpdate ? 'green' : 'red'" size="small">
                {{ confForm.regularPasswordUpdate ? '已开启' : '已关闭' }}
              </a-tag>
              <span class="ml10 security-info">
                开启后，用户密码在更换周期内未进行过一次密码修改操作时，自动弹出密码修改窗口提示修改
              </span>
            </a-typography-paragraph>
            <a-form-item field="updateCycle" label="更换周期">
              <a-input-number v-model="confForm.updateCycle" :min="0" placeholder="请输入" style="width: 300px">
                <template #append><a-typography-text type="warning">(天)</a-typography-text></template>
              </a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.regularPasswordUpdate" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 用户初此登录修改密码 -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 用户初次登录修改密码 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <div class="security-content">
              <a-typography-paragraph class="security-tip">
                <a-tag :color="confForm.passwordModifyFirstTime ? 'green' : 'red'" size="small">
                  {{ confForm.passwordModifyFirstTime ? '已开启' : '已关闭' }}
                </a-tag>
                <span class="ml10 security-info"> 开启后，新用户初次登录系统成功后将自动弹出密码修改窗口提示修改 </span>
              </a-typography-paragraph>
            </div>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.passwordModifyFirstTime" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
        <a-divider :margin="10"></a-divider>
        <!-- 登录双因子验证(手机验证码验证) -->
        <a-row :gutter="16">
          <a-col :span="3">
            <a-typography-paragraph :ellipsis="{ showTooltip: true }"> 登录双因子验证 </a-typography-paragraph>
          </a-col>
          <a-col :flex="1">
            <div class="security-content">
              <a-typography-paragraph class="security-tip">
                <a-tag :color="confForm.twoFactorAuthentication ? 'green' : 'red'" size="small">
                  {{ confForm.twoFactorAuthentication ? '已开启' : '已关闭' }}
                </a-tag>
                <span class="ml10 security-info"> 开启后，登录页面账号登录将增加[手机验证码]验证输入 </span>
              </a-typography-paragraph>
            </div>
          </a-col>
          <a-col :span="2" class="mdt-col-flexend">
            <a-switch v-model="confForm.twoFactorAuthentication" v-bind="switchAttr"></a-switch>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { FormInstance, Message, SelectOptionData } from '@arco-design/web-vue'
import { saveSystemParam } from '@/api/commonApi/config'
import { useAppStore } from '@arco/store'
import { reactive, ref } from 'vue'
import _ from 'lodash'

const appStore = useAppStore()

const generateFormModel1 = () => {
  return {
    passwordModifyFirstTime: 0,
    twoFactorAuthentication: 0,
    regularPasswordUpdate: 0,
    passwordErrorNumber: 1,
    passwordErrorLock: 0,
    isPasswordSetting: 0,
    isTimeoutLogout: 0,
    passwordContent: [],
    autoUnlockTime: 0,
    passwordSize: 6,
    logoutTime: 120,
    updateCycle: 0,
    isPhoneSign: 0
  }
}

const formRules = ref({
  passwordErrorNumber: [{ required: true, message: '请输入错误次数', trigger: 'blur' }],
  autoUnlockTime: [{ required: true, message: '请输入自动解锁时间', trigger: 'blur' }],
  passwordSize: [{ required: true, message: '请输入最小密码位数', trigger: 'blur' }],
  logoutTime: [{ required: true, message: '请输入超时时间', trigger: 'blur' }],
  updateCycle: [{ required: true, message: '请输入更换周期', trigger: 'blur' }]
})

const mapMethodsOptions = ref<SelectOptionData[]>([
  { label: '大写字母', value: 1 },
  { label: '小写字母', value: 2 },
  { label: '特殊字符', value: 3 },
  { label: '数字', value: 4 }
])

const confForm = ref<REQUEST_GET_APP_SYSTEM_PARAM_TYPE>(generateFormModel1())
const confRef = ref<FormInstance>()
const switchAttr = reactive({
  uncheckedValue: 0,
  checkedValue: 1
})

const save = () => {
  confRef.value?.validate((error) => {
    if (!error) {
      saveSystemParam(confForm.value).then(() => {
        appStore.setSystemParam(confForm.value)
        Message.success('修改状态成功')
      })
    }
  })
}

const setSystemFormParam = () => {
  if (appStore.systemParam) confForm.value = _.cloneDeep(appStore.systemParam)
}

setSystemFormParam()
</script>

<style scoped lang="less">
.conf-container {
  padding: 20px;
}
.security-tip {
  color: rgb(var(--gray-6));
}
</style>
