<template>
  <div style="padding: 16px">
    <a-row>
      <a-button type="primary" @click="newBusinessType">新建</a-button>
    </a-row>
    <a-row>
      <a-table
        :columns="columns"
        :data="data"
        row-key="businessTypeId"
        :pagination="false"
        :loading="loading"
        style="margin-top: 20px; width: 100%"
      >
        <template #processType="{ record }">
          <a-tag style="margin-right: 5px;" v-for="p of record.processTypeList" :key="p" :color="ApprovalStatusEnum.getColorByCode(p)">{{
						ApprovalStatusEnum.getTitleByCode(p)
						}}</a-tag>
        </template>
        <template #statusFlag="{ record }">
          <span v-if="record.status == 1" class="success">正常</span>
          <span v-else class="danger">禁用</span>
        </template>
        <template #operations="{ record }">
          <a-button class="pl6 pr6" type="text" size="mini" @click="editBusinessType(record)"> 编辑 </a-button>
          <a-button class="pl6 pr6" type="text" status="danger" size="mini" @click="addSubBusinessType(record)">
            添加子级
          </a-button>
        </template>
      </a-table>
    </a-row>
    <EditForm :editData="editData" :visible="visible" :title="title" :formKey="formKey" @closeForm="closeForm" :openType="openType" @reload="reload"></EditForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { businessTypeTree } from '@/api/arcoApi/systemManage/businessTypeManage.ts'
import EditForm from '@/views/system/businessTypeManage/components/edit-form.vue'
import { ApprovalStatusEnum } from '@/dict/caseAuditManage.ts'

const columns = [
  {title: '序号',dataIndex: 'businessTypeId'},
  {title: '业务类型名称',dataIndex: 'businessTypeName'},
  {title: '描述',dataIndex: 'description'},
  {title: '办结流程',dataIndex: 'processTypeList',slotName: 'processType',align: 'center' },
  {title: '状态',dataIndex: 'status', slotName: 'statusFlag'},
  {width: 180, align: 'center', title: '操作', dataIndex: 'operations', slotName: 'operations'}
]
const closeForm = ()=>{
  visible.value = false;
}
const formKey = ref(0)
const openType = ref(0)
const loading = ref(true)
const visible = ref(false);
const title = ref('');
const editData = ref<BUSINESS_TYPE>({
  businessTypeId: null,
  businessTypeName: '',
  description: '',
  status: 1,
  parentId: null,
  createTime: null,
  processTypeList: [],
  children: null
});

// 新建业务类型
const newBusinessType = ()=>{
  formKey.value += 1;
  title.value="新增业务类型"
  visible.value = true;
  openType.value=1;
  editData.value.businessTypeId = null;
  editData.value.businessTypeName = '';
  editData.value.description = '';
  editData.value.status = 1;
  editData.value.parentId = null;
  editData.value.processTypeList = [];
}
// 修改业务类型
const editBusinessType = (record: any) => {
  openType.value=2;
  formKey.value += 1;
  title.value="修改业务类型"
  visible.value = true;
  editData.value = {...record};
}
// 添加子级
const addSubBusinessType = (record: any) => {
  openType.value=3;
  formKey.value += 1;
  title.value="添加子业务类型"
  visible.value = true;
  editData.value.businessTypeId = null;
  editData.value.businessTypeName = '';
  editData.value.description = '';
  editData.value.status = 1;
  editData.value.processTypeList = [];
  editData.value.parentId = record.businessTypeId;
}
const data = ref<BUSINESS_TYPE[]>([]);
//删除u所有空children
const cleanEmptyChildren = (list)=>{
  for (let e of list) {
    if(!e.children || e.children.length==0){
      delete e.children
    }else{
      cleanEmptyChildren(e.children)
    }
  }
}
const reload = ()=>{
  loading.value = true;
  businessTypeTree().then((res:any)=>{
    cleanEmptyChildren(res);
    data.value = res;
    loading.value = false;
  })
}
businessTypeTree().then((res:any)=>{
  cleanEmptyChildren(res);
  data.value = res;
  loading.value = false;
})
</script>
<style scoped lang="less"></style>
