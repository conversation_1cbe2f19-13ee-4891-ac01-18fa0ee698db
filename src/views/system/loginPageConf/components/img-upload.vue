<template>
  <a-space direction="vertical" :style="{ width: '100%' }">
    <a-upload
      ref="imguploadRef"
      action="/"
      :file-list="file ? [file] : []"
      :show-file-list="false"
      accept=".png,.jpg,.jpeg,.gif,.svg,.dsg,.ico"
      :auto-upload="false"
      @change="onChange"
      @progress="onProgress"
    >
      <template #upload-button>
        <div :class="`arco-upload-list-item${file && file.status === 'error' ? ' arco-upload-list-item-error' : ''}`">
          <div v-if="file && file.url" class="arco-upload-list-picture custom-upload-avatar">
            <img :src="file.url" />
            <div class="arco-upload-list-picture-mask">
              <a-space :size="10">
                <IconEdit />
                <IconDelete @click.stop="onClear" />
              </a-space>
            </div>
            <a-progress
              v-if="file.status === 'uploading' && file.percent && file.percent < 100"
              :percent="file.percent"
              type="circle"
              size="mini"
              :style="{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translateX(-50%) translateY(-50%)'
              }"
            />
          </div>
          <div v-else class="arco-upload-picture-card">
            <div class="arco-upload-picture-card-text">
              <IconPlus />
            </div>
          </div>
        </div>
      </template>
    </a-upload>
  </a-space>
</template>

<script lang="ts" setup>
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'
import type { UploadInstance } from '@arco-design/web-vue/es/upload'
import { getImgFilePath } from '@/utils/proto'
import { onMounted, ref, watch } from 'vue'

const props = defineProps<{ fileType: string; url: string | undefined }>()

const emits = defineEmits<{
  (e: 'change', fileType: string, file: FileItem | null): void
  (e: 'progress', fileType: string, file: FileItem | null): void
}>()

const uploadRef = ref<UploadInstance>()

const file = ref<FileItem>()

const onChange = (_, currentFile: FileItem) => {
  file.value = { ...currentFile }
  emits('change', props.fileType, file.value)
}
const onProgress = (currentFile: FileItem) => {
  file.value = currentFile
  emits('progress', props.fileType, file.value)
}

const onClear = () => {
  file.value = undefined
  emits('change', props.fileType, null)
}

onMounted(() => {})

watch(() => props.url, (val) => {
  if (val) {
    let previewUrl = getImgFilePath(val)
    file.value = { uid: new Date().getTime() + 'xz', url: previewUrl }
  }
})
</script>
<style lang="scss" scoped>
.arco-upload-list-item {
  margin-top: 0;
}
.arco-upload-picture-card,
.arco-upload-list-picture {
  width: 180px;
  height: 120px;
  img {
    object-fit: contain;
  }
}

.arco-upload-list-picture-mask {
  line-height: 120px;
}
</style>
