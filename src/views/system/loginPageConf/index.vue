<template>
  <div class="login-config-page">
    <a-form
      ref="baseFormRef"
      :model="baseInfoForm"
      :rules="baseInfoRules"
      :label-col-props="{ span: 9 }"
      :wrapper-col-props="{ span: 15 }"
      :wrapper-col-style="{ width: '20vw' }"
      label-align="left"
      layout="vertical"
    >
      <a-space direction="vertical" :size="10" fill>
        <a-card class="mdt-card-common" title="基本信息">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="appearanceName" label="登录页名称">
                <a-input v-model="baseInfoForm.appearanceName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="appearanceUserName" label="所属用户">
                <a-input v-model="baseInfoForm.appearanceUserName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="appearanceEnableStatus" label="启用状态">
                <a-switch
                  v-model="baseInfoForm.appearanceEnableStatus"
                  :checked-value="1"
                  :unchecked-value="0"
                  :default-checked="baseInfoForm.appearanceEnableStatus === 1 ? true : false"
                ></a-switch>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="mdt-card-common" title="地址信息">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="appearancePath" label="路径">
                <a-input
                  v-model="baseInfoForm.appearancePath"
                  :max-length="15"
                  placeholder="请输入内容"
                  allow-clear
                  @change="handleAppearancePath"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="appearanceDomainName" label="域名">
                <a-typography-text :copy-text="baseInfoForm.appearanceDomainName" type="danger" ellipsis copyable>
                  <a-link>{{ baseInfoForm.appearanceDomainName }}</a-link>
                </a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="appearanceUrl" label="登录地址">
                <a-typography-text :copy-text="baseInfoForm.appearanceUrl" type="danger" ellipsis copyable>
                  <a-link>{{ baseInfoForm.appearanceUrl }}</a-link>
                </a-typography-text>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="mdt-card-common" title="浏览器标签页配置">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="tabName" label="标签页展示名称">
                <a-input v-model="baseInfoForm.tabName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="tabPath" label="标签页logo">
                <ImgUpload file-type="tab" :url="baseInfoForm.tabPath" @change="uploadChange" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="mdt-card-common" title="登录页面配置">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="loginBoxTitle" label="登录框标题">
                <a-input v-model="baseInfoForm.loginBoxTitle" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="brandTitle" label="品牌标题">
                <a-input v-model="baseInfoForm.brandTitle" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="backgroundPath" label="背景图">
                <ImgUpload file-type="background" :url="baseInfoForm.backgroundPath" @change="uploadChange" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="brandPath" label="品牌logo">
                <ImgUpload file-type="brand" :url="baseInfoForm.brandPath" @change="uploadChange" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="mdt-card-common" title="首页导航栏配置">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="navigationName" label="导航栏展示名称">
                <a-input v-model="baseInfoForm.navigationName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="navigationPath" label="导航栏logo">
                <ImgUpload file-type="navigation" :url="baseInfoForm.navigationPath" @change="uploadChange" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="mdt-card-common">
          <div class="login-config-footer">
            <a-space :size="18">
              <a-button :loading="loading" type="primary" @click="save">保存 </a-button>
              <a-button @click="back">返回 </a-button>
            </a-space>
          </div>
        </a-card>
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { byIdfindSingleLoginAppearance, updateLoginAppearance } from '@/api/arcoApi/systemManage/loginPageManage'
import { addLoginAppearance } from '@/api/arcoApi/systemManage/loginPageManage'
// import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { FileItem, Modal } from '@arco-design/web-vue'
import ImgUpload from './components/img-upload.vue'
import { useRoute, useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()
const route = useRoute()

const appearanceId = ref<string>(route.query.id as string)

const generateFormModel = () => {
  return {
    appearanceUrl: `${location.origin}/login`,
    appearanceDomainName: location.host,
    appearanceEnableStatus: 1,
    backgroundFile: null,
    navigationFile: null,
    appearanceUserName: '',
    navigationPath: '',
    navigationName: '',
    appearanceName: '',
    appearancePath: '',
    backgroundPath: '',
    brandFile: null,
    loginBoxTitle: '',
    appearanceId: '',
    tabFile: null,
    brandTitle: '',
    brandPath: '',
    tabName: '',
    tabPath: ''
  }
}

// const appearanceEnableStatusptions = ref<SelectOptionData[]>(dict.appearanceEnableStatusptions)
const baseInfoForm = ref<REQUEST_POST_LOGINAPPEARANCE_ADD_PARAM_TYPE>(generateFormModel())
const fileForm = ref<OBJ_KEY_STR_ANY_TYPE>({
  backgroundFile: null,
  navigationFile: null,
  brandFile: null,
  tabFile: null
})
const baseFormRef = ref<FormInstance>()
const loading = ref(false)

const baseInfoRules = ref({
  appearanceName: [{ required: true, message: '请选择登录页名称' }],
  appearancePath: [
    { required: true, message: '请输入路径' },
    { match: new RegExp(/^[a-zA-Z0-9]+$/), message: '禁止输入非英文字母数字组合' }
  ]
})

const handleAppearancePath = (val: string) => {
  baseInfoForm.value.appearanceUrl = `${location.origin}/login${`/${val}`}`
}

const uploadChange = (prefix: string, file: FileItem | null) => {
  if (file) {
    baseInfoForm.value[`${prefix}Path`] = ''
    fileForm.value[`${prefix}File`] = file
  } else {
    baseInfoForm.value[`${prefix}Path`] = ''
    fileForm.value[`${prefix}File`] = null
  }

  // let targetIndex = baseInfoForm.value.fileList.findIndex((item) => item.fileType === fileType)
  // if (targetIndex >= 0) {
  //   baseInfoForm.value.fileList.splice(targetIndex, 1)
  // } else {
  // let fileForm = new FormData()
  // fileForm.append('file', file)
  // fileForm.append('fileType', fileType)
  // baseInfoForm.value.fileList.push(fileForm)
  // let fileRow = file
  // fileRow.fileType = fileType
  // baseInfoForm.value.fileList.push(fileRow)
  // }
}

const save = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    loading.value = true
    let param = new FormData()
    for (const key in baseInfoForm.value) {
      if (['backgroundFile', 'navigationFile', 'brandFile', 'tabFile'].includes(key) && fileForm.value[key]) {
        param.append(key, fileForm.value[key] ? fileForm.value[key].file : '')
      } else {
        param.append(key, baseInfoForm.value[key])
      }
    }
    if (appearanceId.value) {
      baseInfoForm.value.appearanceId = appearanceId.value || ''
      updateLoginAppearance(param)
        .then(() => {
          operationTip('edit')
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      addLoginAppearance(param)
        .then(() => {
          operationTip('add')
        })
        .finally(() => {
          loading.value = false
        })
    }
  }
}

const operationTip = (type: 'add' | 'edit') => {
  Modal.info({
    title: '操作成功',
    hideCancel: false,
    // bodyStyle: { padding: '0 30px' },
    content: '是否返回登录页管理列表页面？',
    cancelText: type === 'add' ? '继续新增' : '继续编辑',
    okText: '返回列表',
    onOk: () => {
      back()
    },
    onCancel: () => {
      if (type === 'add') {
        baseInfoForm.value = generateFormModel()
      }
    }
  })
}

const back = () => {
  router.push({ name: 'loginPageManage' })
}

if (appearanceId.value) {
  byIdfindSingleLoginAppearance(appearanceId.value)
    .then((res) => {
      if (res) {
        for (const key in baseInfoForm.value) {
          if (Object.prototype.hasOwnProperty.call(res, key)) {
            baseInfoForm.value[key] = res[key]
          }
        }
      }
    })
    .catch(() => {
      baseInfoForm.value = generateFormModel()
    })
}
</script>

<script lang="ts">
export default {
  name: 'Basic'
}
</script>

<style lang="scss" scoped>
.login-config-page {
  background-color: var(--color-fill-2) !important;
}

.login-config-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
