<template>
  <div class="home">
    <div class="home_header">
      <div class="home_header_left">
        <div class="home_header_left_phoContain">
          <img src="@/assets/images/dol.png" class="home_header_left_phoContain_pho" />
          <a> {{ state === 1 ? '延期申请审批' : '调解成功案件数量' }}</a>
          <span @click="state === 1 ? toCaseAudit({ approvalType: 2 }) : toCaseList({ mediateResult: 1 })">
            {{ state === 1 ? homeData.caseApprovalDelayCount : homeData.successCount || 0 }}
          </span>
          <span>{{ state === 1 ? '办结申请审批' : '调解成功案件标的' }}</span>
          <a @click="state === 1 ? toCaseAudit({ approvalType: 1 }) : ''">
            {{ state === 1 ? homeData.caseApprovalCloseCount : homeData.caseSubjectMatterSum || 0 }}
          </a>
        </div>
      </div>

      <div class="home_header_right">
        <div v-if="state === 1" class="home_header_right_phoContain">
          <img src="@/assets/images/genjin.png" class="home_header_right_phoContain_pho" />
          <span>待分派案件</span>
          <a @click="toCaseList({ mdtCaseStatuses: ['30'] })">{{ homeData.casePendingAmount || 0 }}</a>
        </div>
        <div class="home_header_right_phoContain">
          <img src="@/assets/images/zaiban.png" class="home_header_right_phoContain_pho" />
          <span>在办案件量</span>
          <a @click="toCaseList({ mdtCaseStatuses: ['35'] })">{{ homeData.caseWorkingAmount || 0 }}</a>
        </div>
        <div class="home_header_right_phoContain">
          <img src="@/assets/images/yihuankuan.png" class="home_header_right_phoContain_pho" />
          <span>已结案案件量</span>
          <a @click="toCaseList({ mdtCaseStatuses: ['40'] })">{{ homeData.caseCloseAmount || 0 }}</a>
        </div>
        <div v-if="state === 0" class="home_header_right_phoContain">
          <img src="@/assets/images/genjin.png" class="home_header_right_phoContain_pho" />
          <span>待跟进案件量</span>
          <a @click="toCaseList({ mediateStatuses: ['4', '5'] })">{{ homeData.caseFollowingAmount || 0 }}</a>
        </div>
        <div class="home_header_right_redPhoContain">
          <img src="@/assets/images/weigenjin.png" class="home_header_right_redPhoContain_pho" />
          <span>7日内即将超时</span>
          <a @click="toCaseList({ expirationTimeStart: 0, expirationTimeEnd: 7 })">
            {{ homeData.caseNearTimeoutAmount || 0 }}
          </a>
        </div>
      </div>
			<!-- 任务统计卡片 -->
			<div  class="home_header_task">
				<div class="home_header_task_phoContain">
					<img src="@/assets/images/genjin.png" class="home_header_task_phoContain_pho" />
					<span>代办事项数量</span>
					<a @click="toCaseListWithTaskQuery({ managerIdList: [userStore.userInfo.accountId],taskStatuses:TaskStatus.IN_PROGRESS })">
            {{ homeData.pendingTaskAmount || 0 }}
          </a>
					<span>即将截止数量</span>
					<a @click="toCaseListWithTaskQuery({ managerIdList: [userStore.userInfo.accountId],taskStatuses:TaskStatus.IN_PROGRESS,deadlineStart: '1970-01-01 00:00:00' ,deadlineEnd: dayjs().add(24, 'hour').format('YYYY-MM-DD HH:mm:ss') })">
            {{ homeData.nearDeadlineTaskAmount || 0 }}
          </a>
				</div>
			</div>
    </div>

    <div v-if="state === 1" class="mt10 home_main">
      <a-row :gutter="10">
        <a-col :span="12">
          <a-card class="general-card">
            <template #title> 调解作业排名 </template>
            <a-table
              :data="tableDataList"
              :pagination="false"
              :bordered="false"
              :columns="tableColumns"
              :scroll="{ maxHeight: '40vh' }"
              style="margin-bottom: 10px"
            >
              <template #rank="{ rowIndex }"> {{ rowIndex + 1 }} </template>
              <template #successRate="{ record }"> {{ record.successRate || 0 }}% </template>
            </a-table>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card class="mdt-common-scrollbar" style="overflow: auto">
            <template #title>
              <span class="f14 bold">作业情况</span>
            </template>
            <template #extra>
              <a-space :size="12">
                <span>统计条件：</span>
                <a-select
                  v-model="validDurationForm.type"
                  style="width: 120px"
                  :options="analysisOptions"
                  placeholder="请选择"
                  size="mini"
                  @change="handleChange"
                />
                <!-- <a-month-picker
                v-model="validDurationForm.dateEnd"
                
                size="mini"
                :disabled-date="disabledDate"
                :allow-clear="false"
              /> -->
                <a-range-picker
                  v-model="validDurationForm.allTime"
                  style="width: 160px"
                  size="mini"
                  :disabled-date="disabledDate"
                  @change="handlePickerSelect"
                >
                  <template #suffix-icon><icon-schedule /></template>
                </a-range-picker>
              </a-space>
            </template>
            <div ref="fal" class="fal-chart" style="height: 40vh; min-width: 600px; position: relative"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <div v-else :gutter="20" class="mt10 home_main">
      <div class="home_LefBot">
        <div class="home_LefBot_Title">
          <i class="el-icon-s-grid"></i>
          <span>本月案件办结统计</span>
        </div>
        <div class="home_LefBot_Total">
          <div>
            <span>调解成功案件统计</span>
            <span>{{ `${homeData.successRate || 0}%` }} </span>
          </div>
          <div>
            <span>调解终止案件统计</span>
            <span> {{ `${homeData.failRate || 0}%` }} </span>
          </div>
        </div>
        <div ref="pie" class="home_LefBot_Pie"></div>
      </div>

      <div class="home_RigBot">
        <div class="home_RigBot_Title">
          <i class="el-icon-s-grid"></i>
          <span>近六月调成案件统计</span>
          <span>单位：件</span>
        </div>
        <div ref="bar" class="home_RigBot_Bar"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { getMediateWorkSituation, getMediationRank, getTaskBoardStatistics } from '@/api/arcoApi/dashboard/statistics'
import type { VIEWS_DESKTOP_INITDATA_TYPE } from './types/desktop'
import { useUserStore, useTabBarStore } from '@arco/store'
import * as echarts from 'echarts'
import { markRaw } from 'vue'
import dayjs from 'dayjs'
import _ from 'lodash'
import { TaskStatus } from '@/const/task.ts'

export default {
  name: 'Desktop',
	computed: {
		TaskStatus() {
			return TaskStatus
		},
		dayjs() {
			return dayjs
		}
	},
  components: {},
  data() {
    // 获取
    const endDate = new Date()
    const startDate = new Date()
    startDate.setTime(startDate.getTime() - 3600 * 1000 * 24 * 30)

    const sortable = { sortDirections: ['ascend', 'descend'] }
    let initData: VIEWS_DESKTOP_INITDATA_TYPE = {
      validDurationForm: {
        type: '1',
        dateEnd: dayjs(endDate).format('YYYY-MM-DD'),
        dateStart: dayjs(startDate).format('YYYY-MM-DD'),
        allTime: [dayjs(startDate).format('YYYY-MM-DD'), dayjs(endDate).format('YYYY-MM-DD')]
      },
      sourcePartiesData: {
        duration: { type: '人均在线时长', yAxisData: [], xAxisData: [] },
        active: { type: '活跃人数', yAxisData: [], xAxisData: [] }
      },
      analysisOptions: [
        { label: '活跃人数', value: '1' },
        { label: '人均在线时长', value: '2' }
      ],
      disabledDate: function (current: any) {
        return current > new Date()
      },
      tableDataList: [],
      tableColumns: [
        { width: 60, title: '排名', dataIndex: 'rank', slotName: 'rank' },
        { width: 140, tooltip: true, ellipsis: true, sortable, title: '调解组织', dataIndex: 'orgName' },
        { width: 150, align: 'center', title: '受理案件数量', sortable, dataIndex: 'pendAmount' },
        { width: 130, title: '调解成功率', sortable, dataIndex: 'successRate', slotName: 'successRate' },
        { width: 140, align: 'center', title: '平均办理周期', sortable, dataIndex: 'averageProcessingCycle' }
      ],
      cspChartBar: null,
      mdtChartBar: null,
      mdtChartPie: null,
      homeData: {
        caseApprovalCloseCount: 0, //	integer($int64) 办结申请审批数量
        caseApprovalDelayCount: 0, //	integer($int64) 延期申请审批数量
        caseNearTimeoutAmount: 0, //	integer($int64) 7日内即将超时案件量
        caseSubjectMatterSum: 0, //	integer($int32) 调解成功案件标的
        caseFollowingAmount: 0, //	integer($int64) 待跟进案件量
        casePendingAmount: 0, //	integer($int64) 案件待分配量
        caseWorkingAmount: 0, //	integer($int64) 在办案件量
        caseCloseAmount: 0, //	integer($int64) 已结案案件量
        successCount: 0, //	integer($int64) 调解成功案件数量
        failCount: 0, //	integer($int64) 案件终止量
        pendingTaskAmount: 0, //	integer($int64) 代办任务数量
        nearDeadlineTaskAmount: 0, //	integer($int64) 即将截至任务数量

        monthlySuccessCounts: [],
        successRate: '', // string 案件成功率
        failRate: '' //	string 案件终止率
      },
      // 六月案件统计柱状图参数
      amount: [],
      month: [],
      state: 0,
      userStore: useUserStore()
    }
    return initData
  },
  created() {
    this.init()
    const userStore = useUserStore()
    const companyType = userStore.userInfo.companyType
    if (companyType && ['1', '3'].includes(companyType)) {
      this.state = 1
    } else {
      this.state = 0
    }
  },
  methods: {
    // 首页初始化
    init() {
      getTaskBoardStatistics().then((res) => {
        if (res) {
          this.homeData = res
          this.month = res.monthlySuccessCounts?.map((val) => val.xAxis)
          this.amount = res.monthlySuccessCounts?.map((val) => val.yAxis)
          this.state === 1 ? this.initCaseSourcePartiesDraw() : this.initMediationOrgDraw()
        }
      })
    },
    // 初始化调解组织图表
    initMediationOrgDraw() {
      let pieHtml = this.$refs.pie as HTMLElement
      let barHtml = this.$refs.bar as HTMLElement
      this.mdtChartPie = markRaw(echarts.init(pieHtml))
      this.mdtChartBar = markRaw(echarts.init(barHtml))
      // 图表自适应
      window.onresize = () => {
        this.mdtChartBar?.resize()
        this.mdtChartPie?.resize()
      }
      this.$nextTick(() => {
        this.mdtDrawPie()
        this.mdtDrawBar()
      })
    },
    // 初始化案源方图表
    async initCaseSourcePartiesDraw() {
      getMediationRank().then((res) => {
        if (res) this.tableDataList = res
      })
      await this.getMediateWorkSituationData()
      let falHtml = this.$refs.fal as HTMLElement
      this.cspChartBar = markRaw(echarts.init(falHtml))
      // 图表自适应
      window.onresize = () => this.cspChartBar?.resize()
      this.$nextTick(() => this.cspDrawBar())
    },
    async getMediateWorkSituationData() {
      let res = await getMediateWorkSituation(this.validDurationForm)
      let type = this.validDurationForm.type === '1' ? 'active' : 'duration'
      if (res && res.length) {
        this.sourcePartiesData[type].yAxisData = res.map((workItem) => workItem.yAxis)
        this.sourcePartiesData[type].xAxisData = res.map((workItem) => workItem.xAxis)
      } else {
        this.sourcePartiesData[type].yAxisData = []
        this.sourcePartiesData[type].xAxisData = []
      }
    },
    gcd(a: number, b: number) {
      while (b != 0) {
        let temp = b
        b = a % b
        a = temp
      }
      return a
    },
    // 案源方-绘制柱状图
    cspDrawBar() {
      let type = this.validDurationForm.type === '1' ? 'active' : 'duration'
      let maxNum = _.max(this.sourcePartiesData[type].yAxisData)
      let orgNum = this.sourcePartiesData[type].xAxisData.length
      let interval = maxNum > 10 ? Math.floor(maxNum / 10) : 1
      let provideNumber = 15 - orgNum < 1 ? 1 : 15 - orgNum // 单行显示文字个数
      this.cspChartBar?.setOption({
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { top: '30px', bottom: '70px', left: '30px', right: '30px' },
        xAxis: {
          triggerEvent: true,
          interval: 0,
          type: 'category',
          data: this.sourcePartiesData[type].xAxisData,
          axisTick: { show: false },
          axisLabel: {
            formatter: function (params) {
              return params.length > provideNumber ? params.substring(0, provideNumber) + '...' : params
            }
          }
        },
        dataZoom: [
          {
            handleSize: '80%',
            type: 'slider',
            show: true,
            statr: 0,
            end: 100
          }
        ],
        yAxis: { type: 'value', min: 0, max: maxNum + interval, interval },
        series: [
          {
            name: '作业情况',
            data: this.sourcePartiesData[type].yAxisData,
            type: 'bar',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: '#97bcff' },
                { offset: 0, color: '#3a9189' }
              ])
            }
          }
        ]
      })
      this.cspChartBar?.on('mouseover', (e) => {
        let strValue = e.value as string
        if (e.componentType === 'xAxis' && strValue.length > provideNumber) {
          let tipDom = document.querySelector('#x-axis-tip') as HTMLDivElement
          if (!tipDom) {
            var createTipDom = document.createElement('div')
            createTipDom.setAttribute('id', 'x-axis-tip')
            createTipDom.style.display = 'block'
            createTipDom.style.position = 'absolute'
            createTipDom.style.padding = '4px'
            createTipDom.style.borderRadius = '4px'
            createTipDom.style.border = '1px solid rgb(204, 204, 204)'
            if (e.event) {
              createTipDom.style.top = e.event.offsetY + 10 + 'px'
              createTipDom.style.left = e.event.offsetX - 20 + 'px'
            }
            createTipDom.innerHTML = strValue
            document.querySelector('.fal-chart')?.appendChild(createTipDom)
          } else {
            tipDom.style.display = 'block'
            tipDom.style.position = 'absolute'
            tipDom.style.padding = '4px'
            tipDom.style.borderRadius = '4px'
            tipDom.style.border = '1px solid rgb(204, 204, 204)'

            if (e.event) {
              tipDom.style.top = e.event.offsetY + 10 + 'px'
              tipDom.style.left = e.event.offsetX - 20 + 'px'
            }
            tipDom.innerHTML = strValue
          }
        }
      })
      this.cspChartBar?.on('mouseout', (params) => {
        if (params.componentType === 'xAxis') {
          let tipDom = document.querySelector('#x-axis-tip') as HTMLDivElement
          if(tipDom) tipDom.style.display = 'none'
        }
      })
    },
    // 数据切换
    async handleChange() {
      await this.getMediateWorkSituationData()
      this.cspDrawBar()
    },
    async handlePickerSelect(timeArr: any) {
      if (timeArr && timeArr.length) {
        this.validDurationForm.dateStart = timeArr[0] || ''
        this.validDurationForm.dateEnd = timeArr[1] || ''
      } else {
        this.validDurationForm.dateStart = ''
        this.validDurationForm.dateEnd = ''
      }
      await this.getMediateWorkSituationData()
      this.cspDrawBar()
    },
    toCaseList(params: OBJ_KEY_STR_T_TYPE<string | number | (string | number)[]>) {
      this.$router.push({ name: 'caseManage' }).then(() => {
        const tabBarStore = useTabBarStore()
        tabBarStore.updateTagParams('caseManage', params)
      })
    },
    toCaseAudit(query: OBJ_KEY_STR_T_TYPE<string | number | (string | number)[]>) {
      this.$router.push({ name: 'caseAuditManage', query })
    },
    // 跳转到案件列表并填充任务查询参数
    toCaseListWithTaskQuery(taskParams: OBJ_KEY_STR_ANY_TYPE) {
      const params = {
        taskInstance: taskParams
      }
      this.$router.push({ name: 'caseManage' }).then(() => {
        const tabBarStore = useTabBarStore()
        tabBarStore.updateTagParams('caseManage', params)
      })
    },
    // 调解组织-绘制饼图
    mdtDrawPie() {
      this.mdtChartPie?.setOption({
        tooltip: {
          trigger: 'item',
          formatter: (data: { value: number; percent: number; name: string; marker: string }) => {
            return (
              data.marker +
              data.name +
              '<br/>' +
              `案件数量：${data.value}(件)` +
              '&nbsp;&nbsp;&nbsp;&nbsp;' +
              `${data.percent}%`
            )
          },
          textStyle: { align: 'left', fontSize: 12, fontWeight: 'bold' }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              boderWidth: 2
            },
            label: {
              show: false,
              position: 'center',
              formatter: (data: { dataIndex: number; name: string }) => {
                switch (data.dataIndex) {
                  case 0:
                    return `{b1|${data.name}}`
                  case 1:
                    return `{b2|${data.name}}`
                }
              },
              rich: {
                b1: { color: '#397ee6', fontSize: '24', fontWeight: 'bold' },
                b2: { color: '#ffa048', fontSize: '24', fontWeight: 'bold' }
              }
            },
            emphasis: { label: { show: true } },
            data: [
              { value: this.homeData.successCount || 0, name: '已完成', itemStyle: { color: '#397ee6' } },
              { value: this.homeData.failCount || 0, name: '已终止', itemStyle: { color: '#ffa048' } }
            ]
          }
        ]
      })
    },
    // 调解组织-绘制柱状图
    mdtDrawBar() {
      this.mdtChartBar?.setOption({
        tooltip: { trigger: 'item', axisPointer: { type: 'shadow' }, name: '案件' },
        xAxis: { type: 'category', data: this.month, axisTick: { show: false } },
        yAxis: { type: 'value', min: 0, max: 100, interval: 25 },
        series: [
          {
            name: '案件',
            data: this.amount,
            type: 'bar',
            barWidth: 40,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: '#97bcff' },
                { offset: 0, color: '#5284e7' }
              ])
            }
          }
        ]
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.home {
  width: 100%;
  padding: 10px;
  &_header {
    display: flex;
    align-items: center;
    width: 100%;
    // 左上
    &_left {
      padding: 10px;
      margin-right: 10px;
      background-color: var(--color-bg-1);
      border: solid 1px #e0e0e0;
      display: flex;
      justify-content: center;
      align-items: center;
      // 左上图片与内容容器
      &_phoContain {
        width: 280px;
        height: 150px;
        background: linear-gradient(to bottom right, #fff9f0, #ffedcb);
        border: solid 1px #eedfbf;
        border-radius: $--radius-large;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 20px;
        // 左上图片
        &_pho {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 30%;
          height: 60%;
        }
        // 左上内容(1)
        :nth-child(2) {
          font-size: 14px;
          font-weight: bold;
          color: #c66031;
        }
        // 左上内容(2)
        :nth-child(3) {
          display: inline-block;
          line-height: 30px;
          font-size: 20px;
          font-weight: bold;
          color: #c66031;
          cursor: pointer;
        }
        // 左上内容(3)
        :nth-child(4) {
          font-size: 14px;
          font-weight: bold;
          color: #c66031;
        }
        // 左上内容(4)
        :nth-child(5) {
          display: inline-block;
          line-height: 30px;
          font-size: 20px;
          font-weight: bold;
          color: #c66031;
          cursor: pointer;
        }
      }
    }
    // 任务统计卡片
    &_task {
      padding: 10px;
      margin-right: 10px;
      background-color: var(--color-bg-1);
      border: solid 1px #e0e0e0;
      display: flex;
      justify-content: center;
      align-items: center;
      // 任务统计图片与内容容器
      &_phoContain {
        width: 280px;
        height: 150px;
        background: linear-gradient(to bottom right, #f0fff4, #e6ffed);
        border: solid 1px #b7eb8f;
        border-radius: $--radius-large;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 20px;
        // 任务统计图片
        &_pho {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 30%;
          height: 60%;
        }
        // 任务统计内容(1)
        :nth-child(2) {
          font-size: 14px;
          font-weight: bold;
          color: #389e0d;
        }
        // 任务统计内容(2)
        :nth-child(3) {
          display: inline-block;
          line-height: 30px;
          font-size: 20px;
          font-weight: bold;
          color: #389e0d;
          cursor: pointer;
        }
        // 任务统计内容(3)
        :nth-child(4) {
          font-size: 14px;
          font-weight: bold;
          color: #389e0d;
        }
        // 任务统计内容(4)
        :nth-child(5) {
          display: inline-block;
          line-height: 30px;
          font-size: 20px;
          font-weight: bold;
          color: #389e0d;
          cursor: pointer;
        }
      }
    }
    // 右上
    &_right {
      flex: 1;
      padding: 10px;
      background-color: var(--color-bg-1);
      border: solid 1px #e0e0e0;
      display: flex;
      align-items: center;
      // 右上左三图片与内容容器
      &_phoContain {
        width: 230px;
        height: 150px;
        margin-right: 20px;
        background: linear-gradient(to bottom right, #f0f6ff, #dfecff);
        border: solid 1px #b9cbef;
        border-radius: $--radius-large;
        position: relative;
        // 右上左三图片
        &_pho {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 100%;
          height: 100%;
        }
        // 右上左三图片内容(1)
        :nth-child(2) {
          position: absolute;
          font-size: $--font-size-base;
          font-weight: bold;
          top: 20px;
          left: 24px;
          color: $color-primary-dark;
        }
        // 右上左三图片内容(2)
        :nth-child(3) {
          position: absolute;
          font-size: 24px;
          font-weight: bold;
          top: 48px;
          left: 24px;
          cursor: pointer;
          color: $color-primary-dark;
        }
      }
      // 右上右一图片与内容容器
      &_redPhoContain {
        width: 230px;
        height: 150px;
        background: linear-gradient(to bottom right, #fff3f3, #ffd0cc);
        border: solid 1px #ffc0bc;
        border-radius: $--radius-large;
        position: relative;
        // 右上右一图片
        &_pho {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 100%;
          height: 100%;
        }
        // 右上右一图片内容(1)
        :nth-child(2) {
          position: absolute;
          font-size: $--font-size-base;
          font-weight: bold;
          top: 20px;
          left: 24px;
          color: #d46363;
        }
        // 右上右一图片内容(2)
        :nth-child(3) {
          position: absolute;
          font-size: 24px;
          font-weight: bold;
          top: 48px;
          left: 24px;
          cursor: pointer;
          color: #d46363;
        }
      }
    }
  }
  &_main {
    display: flex;
  }
  // 左下
  &_LefBot {
    width: 25%;
    margin-top: 10px;
    height: 500px;
    background-color: var(--color-bg-1);
    border: solid 1px #e0e0e0;
    position: relative;
    // 左下标题
    &_Title {
      width: 100%;
      height: 10%;
      position: relative;
      // 左下标题内图标
      :nth-child(1) {
        position: absolute;
        left: 1vw;
        top: 0.8vw;
        font-size: 1.1vw;
        background-image: linear-gradient(90deg, #fff 0%, #174299 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }
      // 左下标题内描述
      :nth-child(2) {
        position: absolute;
        font-weight: bold;
        font-size: 1vw;
        color: $color-dark;
        left: 2.5vw;
        top: 0.8vw;
      }
    }
    // 左下案件统计容器
    &_Total {
      width: 100%;
      height: 20%;
      display: flex;
      justify-content: space-evenly;
      // 左下案件统计容器(1)
      :nth-child(1) {
        width: 45%;
        height: 100%;
        background-color: #f0f7ff;
        border-radius: $--radius-large;
        position: relative;
        :nth-child(1) {
          all: initial;
          position: absolute;
          font-size: 0.9vw;
          color: $color-dark;
          font-weight: bold;
          top: 20%;
          left: 10%;
        }
        :nth-child(2) {
          all: initial;
          position: absolute;
          font-size: 1.4vw;
          font-weight: bold;
          color: $color-primary;
          top: 50%;
          left: 10%;
        }
      }
      // 左下案件统计容器(2)
      :nth-child(2) {
        width: 45%;
        height: 100%;
        background-color: #e0f7e9;
        border-radius: $--radius-large;
        position: relative;
        :nth-child(1) {
          all: initial;
          position: absolute;
          font-size: 0.9vw;
          color: $color-dark;
          font-weight: bold;
          top: 20%;
          left: 10%;
        }
        :nth-child(2) {
          all: initial;
          position: absolute;
          font-weight: bold;
          font-size: 1.4vw;
          color: #3fbe6f;
          top: 50%;
          left: 10%;
        }
      }
    }
    // 左下饼图
    &_Pie {
      width: 100%;
      height: 70%;
    }
  }
  // 右下
  &_RigBot {
    margin-left: 10px;
    margin-top: 10px;
    flex: 1;
    height: 500px;
    background-color: var(--color-bg-1);
    border: solid 1px #e0e0e0;
    position: relative;
    // 右下标题
    &_Title {
      width: 100%;
      height: 10%;
      position: relative;
      // 右下标题内图标
      :nth-child(1) {
        position: absolute;
        left: 1vw;
        top: 0.8vw;
        font-size: 1.1vw;
        background-image: linear-gradient(90deg, #fff 0%, #174299 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }
      // 右下标题内描述(左)
      :nth-child(2) {
        position: absolute;
        font-weight: bold;
        font-size: 1vw;
        color: $color-dark;
        left: 2.5vw;
        top: 0.8vw;
      }
      // 右下标题内描述(右)
      :nth-child(3) {
        position: absolute;
        font-size: 0.6vw;
        color: $color-weak;
        right: 2vw;
        top: 1vw;
      }
    }
    //右下柱状图
    &_Bar {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 90%;
    }
  }
}
</style>
