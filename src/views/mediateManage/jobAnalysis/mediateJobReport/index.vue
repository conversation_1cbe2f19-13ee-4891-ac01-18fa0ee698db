<template>
  <div class="job-report">
    <div class="job-report-block">
      <a-row class="mb12">
        <a-col>
          <a-card>
            <template #title>
              <span class="f14 bold">案件调解进展</span>
            </template>
            <template #extra>
              <div>
                <a-range-picker v-model="progressForm.date" :disabled-date="disabledDate" @change="getProgressData">
                  <a-button class="ml6 width170" size="mini">
                    {{
                      (progressForm.date && progressForm.date.join(' - ')) || '请选择日期范围'
                    }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleProgressFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div class="height160">
              <a-row class="height-100">
                <a-col :span="6" class="height-100 pr8">
                  <div class="pt20 pb12 pl20 pr12 height-100 bg1">
                    <div class="lineHeight20">收案总数</div>
                    <div class="flex job-item">
                      <div class="width-40 height-100 flex job-item-text">
                        <div class="f22 mb12">
                          {{ formatWithThousandSeparator(progressData.caseAmount) }}
                        </div>
                        <div class="lineHeight20 mb12">
                          <span class="f12 color9 mr4">较昨天</span>
                          <span
                            :class="
                              progressData.total && progressData.total.type === 1
                                ? 'rise'
                                : progressData.total && progressData.total.type === 2
                                  ? 'decline'
                                  : ''
                            "
                          >
                            <span v-if="progressData.total && progressData.total.type !== 3">{{
                              progressData.total.amount
                            }}</span>
                            <img
                              v-if="progressData.total && progressData.total.type === 1"
                              class="progress-img"
                              src="./assets/rise.png"
                            />
                            <img
                              v-else-if="progressData.total && progressData.total.type === 2"
                              class="progress-img"
                              src="./assets/decline.png"
                            />
                            <span v-else>持平</span>
                          </span>
                        </div>
                      </div>
                      <div id="total-charts" class="width-60 height-100"></div>
                    </div>
                  </div>
                </a-col>
                <a-col :span="6" class="height-100 pl4 pr6">
                  <div class="pt20 pb12 pl20 pr12 height-100 bg2">
                    <div class="lineHeight20">处理中</div>
                    <div class="flex job-item">
                      <div class="width-40 height-100 flex job-item-text">
                        <div class="f22 mb12">
                          {{ formatWithThousandSeparator(progressData.caseWorkingAmount) }}
                        </div>
                        <div class="lineHeight20 mb12">
                          <span class="f12 color9 mr4">较昨天</span>
                          <span
                            :class="
                              progressData.handling && progressData.handling.type === 1
                                ? 'rise'
                                : progressData.handling && progressData.handling.type === 2
                                  ? 'decline'
                                  : ''
                            "
                          >
                            <span v-if="progressData.handling && progressData.handling.type !== 3">{{
                              progressData.handling.amount
                            }}</span>
                            <img
                              v-if="progressData.handling && progressData.handling.type === 1"
                              class="progress-img"
                              src="./assets/rise.png"
                            />
                            <img
                              v-else-if="progressData.handling && progressData.handling.type === 2"
                              class="progress-img"
                              src="./assets/decline.png"
                            />
                            <span v-else>持平</span>
                          </span>
                        </div>
                      </div>
                      <div id="handling-charts" class="width-60 height-100"></div>
                    </div>
                  </div>
                </a-col>
                <a-col :span="6" class="height-100 pl6 pr4">
                  <div class="pt20 pb12 pl20 pr12 height-100 bg3">
                    <div class="lineHeight20">已办结</div>
                    <div class="flex job-item">
                      <div class="width-40 height-100 flex job-item-text">
                        <div class="f22 mb12">
                          {{ formatWithThousandSeparator(progressData.caseCloseAmount) }}
                        </div>
                        <div class="lineHeight20 mb12">
                          <span class="f12 color9 mr4">较昨天</span>
                          <span
                            :class="
                              progressData.closed && progressData.closed.type === 1
                                ? 'rise'
                                : progressData.closed && progressData.closed.type === 2
                                  ? 'decline'
                                  : ''
                            "
                          >
                            <span v-if="progressData.closed && progressData.closed.type !== 3">{{
                              progressData.closed.amount
                            }}</span>
                            <img
                              v-if="progressData.closed && progressData.closed.type === 1"
                              class="progress-img"
                              src="./assets/rise.png"
                            />
                            <img
                              v-else-if="progressData.closed && progressData.closed.type === 2"
                              class="progress-img"
                              src="./assets/decline.png"
                            />
                            <span v-else>持平</span>
                          </span>
                        </div>
                      </div>
                      <div id="closed-charts" class="width-60 height-100"></div>
                    </div>
                  </div>
                </a-col>
                <a-col :span="6" class="height-100 pl8">
                  <div class="pt20 pb12 pl20 pr12 height-100 bg4">
                    <div class="lineHeight20">调解结果</div>
                    <div class="flex job-item">
                      <div class="width-40 height-100 flex job-item-text">
                        <div class="f16 mb12 bold">
                          成功：{{ formatWithThousandSeparator(progressData.successCount) }}
                        </div>
                        <div class="f16 mb12 bold">失败：{{ formatWithThousandSeparator(progressData.failCount) }}</div>
                      </div>
                      <div id="mediate-charts" class="width-60 height-100"></div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row class="mb12">
        <a-col :span="15">
          <a-card>
            <template #title>
              <div class="flex items-center">
                <span class="f14 bold">调解成功率</span>
                <a-tooltip position="bl">
                  <template #content>
                    <div>
                      <div>计算公式：</div>
                      <div>调解成功率=调解成功案件数量/收案总数*100%</div>
                    </div>
                  </template>
                  <icon-exclamation-circle class="ml4" />
                </a-tooltip>
              </div>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="successForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getSuccessData"
                />
                <a-range-picker v-model="successForm.date" :disabled-date="disabledDate" @change="getSuccessData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (successForm.date && successForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleSuccessFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="success-charts" class="height300"></div>
          </a-card>
        </a-col>
        <a-col :span="9" class="pl12">
          <a-card>
            <template #title>
              <div class="flex items-center">
                <span class="f14 bold">案件突增预警</span>
                <a-tooltip position="bl">
                  <template #content>
                    <div>
                      <div>计算公式：</div>
                      <div>红色预警：同一原告平均每天提交案件数量>=15件</div>
                      <div>橙色预警：同一原告平均每天提交案件数量>=10件</div>
                      <div>蓝色预警：同一原告平均每天提交案件数量>=7件</div>
                    </div>
                  </template>
                  <icon-exclamation-circle class="ml4" />
                </a-tooltip>
              </div>
            </template>
            <template #extra>
              <div>
                <a-range-picker v-model="warningForm.date" :disabled-date="disabledDate" @change="getWarningData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (warningForm.date && warningForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
              </div>
            </template>
            <div class="height300">
              <a-table
                row-key="litigantName"
                :scroll="{ maxHeight: '300px' }"
                :columns="columns"
                :data="renderData"
                :pagination="false"
                :bordered="false"
              >
                <template #leval="{ record }">
                  <img v-if="record.leval === 1" class="leval" src="./assets/warning_01.png" />
                  <img v-else-if="record.leval === 2" class="leval" src="./assets/warning_02.png" />
                  <img v-else class="leval" src="./assets/warning_03.png" />
                </template>
                <template #caseSubjectMatterSum="{ record }">
                  <span>{{ formatWithThousandSeparator(record.caseSubjectMatterSum || 0) }}</span>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row class="mb12">
        <a-col :span="12" class="pr6">
          <a-card>
            <template #title>
              <div class="flex items-center">
                <span class="f14 bold">诉调分流率</span>
                <a-tooltip position="bl">
                  <template #content>
                    <div>
                      <div>计算公式：</div>
                      <div>
                        诉调分流率=调解撤诉案件数量/(调解撤诉案件数量-不立案案件数量+调解不成转立案案件数量)*100%
                      </div>
                    </div>
                  </template>
                  <icon-exclamation-circle class="ml4" />
                </a-tooltip>
              </div>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="shuntForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getShuntData"
                />
                <a-range-picker v-model="shuntForm.date" :disabled-date="disabledDate" @change="getShuntData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (shuntForm.date && shuntForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleShuntFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="shunt-charts" class="height240"></div>
          </a-card>
        </a-col>
        <a-col :span="12" class="pl6">
          <a-card>
            <template #title>
              <div class="flex items-center">
                <span class="f14 bold">结案率</span>
                <a-tooltip position="bl">
                  <template #content>
                    <div>
                      <div>计算公式：</div>
                      <div>结案率=(调解成功案件数量+调解不成转立案案件数量)/收案总数*100%</div>
                    </div>
                  </template>
                  <icon-exclamation-circle class="ml4" />
                </a-tooltip>
              </div>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="closeCaseForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getCloseCaseData"
                />
                <a-range-picker v-model="closeCaseForm.date" :disabled-date="disabledDate" @change="getCloseCaseData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (closeCaseForm.date && closeCaseForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleCloseCaseFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="closeCase-charts" class="height240"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-card>
            <template #title>
              <div class="flex items-center">
                <span class="f14 bold">案件触达率</span>
                <a-tooltip position="bl">
                  <template #content>
                    <div>
                      <div>计算公式：</div>
                      <div>案件触达率=触达案件数量/案件总数*100%</div>
                      <div>触达案件统计标准：</div>
                      <div>
                        案件下所有当事人都有通话记录切通话记录为有效通话(通话时长>=30S为有效通话)的案件为已触达案件。
                      </div>
                    </div>
                  </template>
                  <icon-exclamation-circle class="ml4" />
                </a-tooltip>
              </div>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="rearchForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getRearchData"
                />
                <a-range-picker v-model="rearchForm.date" :disabled-date="disabledDate" @change="getRearchData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (rearchForm.date && rearchForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleRearchFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="rearch-charts" class="height300"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <a-modal
      width="600px"
      title="统计范围选择"
      :visible="dialogVisible"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <div class="modal-block">
        <div class="modal-row pr6">
          <div class="modal-title">调解组织：</div>
          <div class="modal-col">
            <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
              <a-checkbox
                :model-value="checkedAllTree"
                :indeterminate="indeterminateTree"
                @change="handleChangeAllTree"
              >
                全部
              </a-checkbox>
              <a-tree
                ref="modalTree"
                v-model:checked-keys="dialogForm.trees"
                :checkable="true"
                check-strictly
                :data="analysisOptions"
                @check="handleCheckTrees"
              />
            </a-scrollbar>
          </div>
        </div>
        <div class="modal-row pl6">
          <div class="modal-title">案由：</div>
          <div class="modal-col">
            <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
              <a-checkbox
                :model-value="checkedAllNature"
                :indeterminate="indeterminateNature"
                @change="handleChangeAllNature"
              >
                全部
              </a-checkbox>
              <a-tree
                ref="modalNature"
                v-model:checked-keys="dialogForm.natures"
                :checkable="true"
                check-strictly
                :data="natureOptions"
                @check="handleCheckNature"
              />
            </a-scrollbar>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getMediationSuccessRate, getDiversionRate } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getStatisticsCondition, getCaseProgress } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getCaseCloseRate, getCaseReachRate } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getCaseSurgeWarning } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { CheckboxInstance } from '@arco-design/web-vue/es/checkbox'

import { ref, computed, onMounted, nextTick, markRaw, reactive } from 'vue'
import dataJson from './data.json'

import * as echarts from 'echarts'
import _ from 'lodash'

const selectOptions = ref<SelectOptionData[]>([])
const analysisOptions = ref<any[]>([])
const checkAllAnysisList = ref<(string | number)[]>([])
const checkAllAnysisNodeList = ref<STATISTICS_ROUND_TYPE[]>([])
const natureOptions = ref<any[]>([])
const checkAllNatureList = ref<(string | number)[]>([])
const checkAllNatureTitleList = ref<string[]>([])

const getStatisticsConditionData = async () => {
  let res: any = await getStatisticsCondition()
  if (res && res.groupConditions) {
    selectOptions.value = []
    for (const key in res.groupConditions) {
      if (Object.prototype.hasOwnProperty.call(res.groupConditions, key)) {
        const element = res.groupConditions[key]
        selectOptions.value.push({ label: element, value: key })
      }
    }
  }
  if (res && res.statisticsTreeNodeList?.length) {
    let list: any[] = _.cloneDeep(res.statisticsTreeNodeList)
    analysisOptions.value = list
    let obj: any = treeToArrayAnysis(list)
    checkAllAnysisList.value = _.cloneDeep(obj.ids)
    checkAllAnysisNodeList.value = _.cloneDeep(obj.nodes)
  }
  if (res && res.natureInfo?.length) {
    let list: any[] = _.cloneDeep(res.natureInfo)
    natureOptions.value = list.map((item: any) => ({ key: item.dictKey, title: item.dictTag }))
    let obj: any = treeToArrayNature(list)
    checkAllNatureList.value = _.cloneDeep(obj.ids)
    checkAllNatureTitleList.value = _.cloneDeep(obj.titles)
  }
}
function treeToArrayAnysis(root: any) {
  const stack = [...root]
  const ids: (string | number)[] = []
  const nodes: any[] = []

  while (stack.length > 0) {
    const node: any = stack.pop()
    ids.push(node.key)
    nodes.push({ key: node.key, type: node.type })

    if (node.children) {
      stack.push(...node.children.reverse())
    }
  }

  return { ids, nodes }
}
function treeToArrayNature(root: any) {
  const stack = [...root]
  const ids: (string | number)[] = []
  const titles: string[] = []

  while (stack.length > 0) {
    const node: any = stack.pop()
    ids.push(node.dictKey)
    titles.push(node.dictTag)

    if (node.children) {
      stack.push(...node.children.reverse())
    }
  }

  return { ids, titles }
}
getStatisticsConditionData()

const datetimeFormat = (date: string | Date | number = '', fmt = 'yyyy-MM-dd') => {
  let _date = date ? new Date(date) : new Date()
  let o: { [x: string]: number } = {
    'M+': _date.getMonth() + 1, // 月份
    'd+': _date.getDate(), // 日
    'h+': _date.getHours(), // 小时
    'm+': _date.getMinutes(), // 分
    's+': _date.getSeconds(), // 秒
    'q+': Math.floor(_date.getMonth() / 3) + 1, // 季度
    S: _date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (_date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k].toString() : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fmt
}

const initDate = reactive<[string | Date | number, string | Date | number]>([
  datetimeFormat(Date.now() - 30 * 24 * 60 * 60 * 1000),
  datetimeFormat()
])

const progressForm = ref<COMMON_FORM_TYPE>({ date: initDate, trees: [], treesNode: [], natures: [], naturesTitle: [] })

const successForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const warningForm = ref<COMMON_FORM_TYPE>({ date: initDate })

const shuntForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const closeCaseForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const rearchForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const disabledDate = (current: Date | string | number) => {
  return current > new Date()
}

interface PROGRESS_DATA_TYPE {
  caseAmount?: number
  caseCloseAmount: number
  caseWorkingAmount?: number
  failCount?: number
  successCount?: number
  dailyChangeDTOList?: []
  total?: { type: number; amount: number | string }
  handling?: { type: number; amount: number | string }
  closed?: { type: number; amount: number | string }
}
interface REQUEST_GET_RENDER_DATA_TYPE {
  leval?: string | number
  litigantName?: string
  caseAmount?: number
  caseSubjectMatterSum?: number
}
const progressData = ref<PROGRESS_DATA_TYPE>({
  caseAmount: 0,
  caseCloseAmount: 0,
  caseWorkingAmount: 0,
  failCount: 0,
  successCount: 0,
  dailyChangeDTOList: [],
  total: { type: 3, amount: 0 },
  handling: { type: 3, amount: 0 },
  closed: { type: 3, amount: 0 }
})
const successData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const renderData = ref<REQUEST_GET_RENDER_DATA_TYPE[]>([])
const columns = computed<TableColumnData[]>(() => [
  { width: 70, align: 'center', title: '级别', dataIndex: 'leval', slotName: 'leval' },
  { width: 130, tooltip: true, ellipsis: true, title: '原告姓名', dataIndex: 'litigantName' },
  { width: 100, tooltip: true, ellipsis: true, align: 'center', title: '案件数量', dataIndex: 'caseAmount' },
  {
    width: 100,
    tooltip: true,
    ellipsis: true,
    title: '标的额',
    dataIndex: 'caseSubjectMatterSum',
    slotName: 'caseSubjectMatterSum'
  }
])
const formatWithThousandSeparator = (number: number = 0) => {
  return number.toLocaleString()
}
const shuntData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const closeCaseData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const rearchData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })

const totalCharts = ref<echarts.EChartsType>()
const handlingCharts = ref<echarts.EChartsType>()
const closedCharts = ref<echarts.EChartsType>()
const mediateCharts = ref<echarts.EChartsType>()
const successCharts = ref<echarts.EChartsType>()
const shuntCharts = ref<echarts.EChartsType>()
const closeCaseCharts = ref<echarts.EChartsType>()
const rearchCharts = ref<echarts.EChartsType>()

interface PROGRESS_FALSE_DATA_TYPE {
  xAxisData: string[]
  yAxisData: (string | number)[]
}
const initProgressCharts = (
  totalData: PROGRESS_FALSE_DATA_TYPE,
  handlingData: PROGRESS_FALSE_DATA_TYPE,
  closedData: PROGRESS_FALSE_DATA_TYPE
) => {
  if (!totalCharts.value) {
    totalCharts.value = markRaw(echarts.init(document.getElementById('total-charts')))
  }
  let totalOption = setNoDataLineOption(totalData)
  totalCharts.value.setOption(totalOption)

  if (!handlingCharts.value) {
    handlingCharts.value = markRaw(echarts.init(document.getElementById('handling-charts')))
  }
  let handlingOption = setNoDataBarOption(handlingData)
  handlingCharts.value.setOption(handlingOption)

  if (!closedCharts.value) {
    closedCharts.value = markRaw(echarts.init(document.getElementById('closed-charts')))
  }
  let closedOption = setNoDataLineOption(closedData)
  closedCharts.value.setOption(closedOption)

  if (!mediateCharts.value) {
    mediateCharts.value = markRaw(echarts.init(document.getElementById('mediate-charts')))
  }
  let mediateOption = setNoDataPieOption()
  mediateCharts.value.setOption(mediateOption)
}

const initSuccessCharts = () => {
  if (!successCharts.value) {
    successCharts.value = markRaw(echarts.init(document.getElementById('success-charts')))
  }
  let successOption = setSuccesssOption()
  successCharts.value.setOption(successOption)
}

const initShuntCharts = () => {
  if (!shuntCharts.value) {
    shuntCharts.value = markRaw(echarts.init(document.getElementById('shunt-charts')))
  }
  let shuntOption = setSlideBarOption(shuntData.value, '诉调分流率', '#ffe5bb')
  shuntCharts.value.setOption(shuntOption)
}

const initCloseCaseCharts = () => {
  if (!closeCaseCharts.value) {
    closeCaseCharts.value = markRaw(echarts.init(document.getElementById('closeCase-charts')))
  }
  let closeCaseOption = setSlideBarOption(closeCaseData.value, '收结比', '#b5d389')
  closeCaseCharts.value.setOption(closeCaseOption)
}

const initRearchCharts = () => {
  if (!rearchCharts.value) {
    rearchCharts.value = markRaw(echarts.init(document.getElementById('rearch-charts')))
  }
  let rearchOption = setRearchOption()
  nextTick(() => {
    rearchCharts.value?.setOption(rearchOption, true)
  })
}

const setNoDataLineOption = (data: PROGRESS_FALSE_DATA_TYPE) => {
  let option = {
    grid: { top: '20px', bottom: '20px' },
    xAxis: { show: false, type: 'category', data: data.xAxisData },
    yAxis: { show: false },
    series: [{ data: data.yAxisData, type: 'line', smooth: true, symbol: 'none' }]
  }
  return option
}

const setNoDataBarOption = (data: PROGRESS_FALSE_DATA_TYPE) => {
  let option = {
    grid: { top: '20px', bottom: '10px' },
    xAxis: { show: false, type: 'category', data: data.xAxisData },
    yAxis: { show: false },
    series: [
      {
        data: data.yAxisData,
        type: 'bar',
        itemStyle: {
          color: function (params: any) {
            if (params.dataIndex % 2 === 0) {
              return '#2cab40'
            } else {
              return '#85df6c'
            }
          }
        }
      }
    ]
  }
  return option
}

const setNoDataPieOption = () => {
  let option = {
    tooltip: { trigger: 'item' },
    grid: { top: '20px', bottom: '10px' },
    legend: { top: '30%', right: '10px', orient: 'vertical', textStyle: { fontSize: 10 } },
    series: [
      {
        type: 'pie',
        radius: ['50%', '90%'],
        left: 0,
        right: 90,
        color: ['#8d4eda', '#165dff'],
        itemStyle: { borderRadius: 4, borderColor: '#fff', borderWidth: 2 },
        label: { show: false },
        labelLine: { show: false },
        data: [
          { name: '调解成功', value: progressData.value.successCount },
          { name: '调解失败', value: progressData.value.failCount }
        ]
      }
    ]
  }
  return option
}

const setSuccesssOption = () => {
  let data: XY_DATA_TYPE = successData.value
  let series: echarts.BarSeriesOption[] = []
  data.yAxis.map((item) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'bar',
        data: item.date,
        name: item.type,
        stack: '调解成功率',
        barWidth: '20px',
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          }
        }
      })
    }
  })
  let xAxisData = data.xAxis.map((item) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '20px', bottom: '80px', left: '40px', right: '10px' },
    legend: { bottom: '10px', textStyle: { fontSize: 10 } },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0,
        formatter: function (params) {
          var newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber =
            xAxisData.length <= 3 ? 18 : xAxisData.length === 4 ? 14 : xAxisData.length === 5 ? 10 : 6 // 单行显示文字个数
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p === rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%' // 在这里设置单位
      }
    },
    color: ['#246eff', '#00b2ff', '#81e2ff', '#8d4eda', '#2cab40', '#85df6c'],
    series: series
  }
  return option
}

const setSlideBarOption = (data: XY_DATA_TYPE, name: string, color: string) => {
  let xAxisData = data.xAxis.map((item) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let series: echarts.BarSeriesOption[] = []
  data.yAxis.map((item) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'bar',
        data: item.date,
        name: name,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          }
        }
      })
    }
  })
  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '30px', bottom: '60px', left: '40px', right: '10px' },
    legend: { top: 0, textStyle: { fontSize: 10 } },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0,
        formatter: function (params) {
          var newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber = xAxisData.length <= 3 ? 9 : xAxisData.length === 4 ? 7 : 6 // 单行显示文字个数
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p === rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%' // 在这里设置单位
      }
    },
    color: [color],
    series: series,
    dataZoom: [
      {
        type: 'slider',
        show: true,
        startValue: 0, //数据窗口范围的起始百分比
        endValue: 4,
        height: 15,
        bottom: 10,
        left: 40,
        right: 10
      }
    ]
  }
  return option
}

const setRearchOption = () => {
  let data: XY_DATA_TYPE = rearchData.value
  let series: echarts.LineSeriesOption[] = []
  data.yAxis.map((item) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'line',
        smooth: true,
        symbol: 'none',
        data: item.date,
        name: item.type,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          }
        }
      })
    }
  })
  let option = {
    grid: { top: '60px', bottom: '60px', left: '40px', right: '30px' },
    legend: { top: '10px', textStyle: { fontSize: 10 } },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    color: ['#8d4eda', '#165dff', '#81e2ff'],
    xAxis: { type: 'category', data: data.xAxis },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%' // 在这里设置单位
      }
    },
    series: series,
    dataZoom: [
      {
        type: 'slider',
        show: true,
        startValue: 0, //数据窗口范围的起始百分比
        height: 15,
        bottom: 10,
        left: 40,
        right: 30
      }
    ]
  }
  return option
}

onMounted(() => {
  nextTick(() => {
    initData()
  })
})

const initData = async () => {
  const apiPromises = [
    getProgressData(),
    getSuccessData(),
    getWarningData(),
    getShuntData(),
    getCloseCaseData(),
    getRearchData()
  ]
  await Promise.all(apiPromises)
}

const commonParams = (formData: COMMON_FORM_TYPE) => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = {}
  if (formData.date && formData.date[0]) {
    params.dateStart = formData.date[0]
  }
  if (formData.date && formData.date[1]) {
    params.dateEnd = formData.date[1]
  }
  if (formData.keyword) {
    params.groupCondition = formData.keyword
  }
  if (formData.treesNode) {
    params.statisticsRound = formData.treesNode
  }
  if (formData.naturesTitle) {
    params.caseCauseList = formData.naturesTitle
  }
  return params
}

const getProgressData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(progressForm.value)
  let res: any = await getCaseProgress(params)
  progressData.value = {
    ...res,
    total: { type: 3, amount: 0 },
    handling: { type: 3, amount: 0 },
    closed: { type: 3, amount: 0 }
  }
  if (res?.dailyChangeDTOList.length) {
    for (let i = 0; i < res.dailyChangeDTOList.length; i++) {
      const element = res.dailyChangeDTOList[i]
      if (element.dataName === '收案总数') {
        progressData.value.total = { type: element.type, amount: element.amount }
      } else if (element.dataName === '处理中') {
        progressData.value.handling = { type: element.type, amount: element.amount }
      } else if (element.dataName === '已办结') {
        progressData.value.closed = { type: element.type, amount: element.amount }
      }
    }
  }
  initProgressCharts(dataJson.totalData, dataJson.handlingData, dataJson.closedData)
}
const getSuccessData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(successForm.value)
  let res: any = await getMediationSuccessRate(params)
  if (res) {
    successData.value = res
  }
  initSuccessCharts()
}
const getWarningData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(warningForm.value)
  let res: any = await getCaseSurgeWarning(params)
  if (res?.length) {
    renderData.value = res || []
  }
}
const getShuntData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(shuntForm.value)
  let res: any = await getDiversionRate(params)
  if (res) {
    shuntData.value = res
  }
  initShuntCharts()
}
const getCloseCaseData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(closeCaseForm.value)
  let res: any = await getCaseCloseRate(params)
  if (res) {
    closeCaseData.value = res
  }
  initCloseCaseCharts()
}
const getRearchData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(rearchForm.value)
  let res: any = await getCaseReachRate(params)
  if (res) {
    rearchData.value = res
  }
  initRearchCharts()
}

const handleProgressFilter = () => {
  commonFilter('progress', progressForm.value)
}
const handleSuccessFilter = () => {
  commonFilter('success', successForm.value)
}
const handleShuntFilter = () => {
  commonFilter('shunt', shuntForm.value)
}
const handleCloseCaseFilter = () => {
  commonFilter('closeCase', closeCaseForm.value)
}
const handleRearchFilter = () => {
  commonFilter('rearch', rearchForm.value)
}

const dialogVisible = ref<boolean>(false)
const checkedAllTree = ref<boolean>(false)
const indeterminateTree = ref<boolean>(false)
const checkedAllNature = ref<boolean>(false)
const indeterminateNature = ref<boolean>(false)
const dialogForm = ref<COMMON_FORM_TYPE>({ key: '', trees: [], treesNode: [], natures: [], naturesTitle: [] })
const modalTree = ref()
const modalNature = ref()
const commonFilter = (key: string, formData) => {
  dialogForm.value = {
    key,
    trees: _.cloneDeep(formData.trees),
    treesNode: _.cloneDeep(formData.treesNode),
    natures: _.cloneDeep(formData.natures),
    naturesTitle: _.cloneDeep(formData.naturesTitle)
  }
  if (dialogForm.value.trees?.length) {
    if (dialogForm.value.trees?.length === checkAllAnysisList.value.length) {
      checkedAllTree.value = true
      indeterminateTree.value = false
    } else {
      checkedAllTree.value = false
      indeterminateTree.value = true
    }
  }
  if (dialogForm.value.natures?.length) {
    if (dialogForm.value.natures.length === checkAllNatureList.value.length) {
      checkedAllNature.value = true
      indeterminateNature.value = false
    } else {
      checkedAllNature.value = false
      indeterminateNature.value = true
    }
  }
  dialogVisible.value = true
  nextTick(() => {
    modalTree.value?.expandAll(true)
    modalNature.value?.expandAll(true)
  })
}
const handleDialogConfirm = async () => {
  switch (dialogForm.value.key) {
    case 'progress':
      progressForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      progressForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      progressForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      progressForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getProgressData()
      break
    case 'success':
      successForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      successForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      successForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      successForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getSuccessData()
      break
    case 'shunt':
      shuntForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      shuntForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      shuntForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      shuntForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getShuntData()
      break
    case 'closeCase':
      closeCaseForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      closeCaseForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      closeCaseForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      closeCaseForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getCloseCaseData()
      break
    case 'rearch':
      rearchForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      rearchForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      rearchForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      rearchForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getRearchData()
      break
    default:
      break
  }
  handleDialogCancel()
}
const handleDialogCancel = () => {
  dialogForm.value = { key: '', trees: [], treesNode: [], natures: [], naturesTitle: [] }
  modalTree.value?.selectAll(false)
  modalNature.value?.selectAll(false)
  checkedAllTree.value = false
  indeterminateTree.value = false
  checkedAllNature.value = false
  indeterminateNature.value = false
  dialogVisible.value = false
}
const handleChangeAllTree: CheckboxInstance['onChange'] = (value) => {
  indeterminateTree.value = false
  if (value) {
    checkedAllTree.value = true
    dialogForm.value.trees = _.cloneDeep(checkAllAnysisList.value)
    dialogForm.value.treesNode = _.cloneDeep(checkAllAnysisNodeList.value)
  } else {
    checkedAllTree.value = false
    dialogForm.value.trees = []
    dialogForm.value.treesNode = []
  }
}
const handleCheckTrees = (values: Array<string | number>, node: any) => {
  dialogForm.value.treesNode = []
  if (node.checkedNodes?.length) {
    node.checkedNodes.map((item: any) => {
      dialogForm.value.treesNode?.push({ key: item.key, type: item.type })
    })
  }
  if (values.length === checkAllAnysisList.value.length) {
    checkedAllTree.value = true
    indeterminateTree.value = false
  } else if (values.length === 0) {
    checkedAllTree.value = false
    indeterminateTree.value = false
  } else {
    checkedAllTree.value = false
    indeterminateTree.value = true
  }
}
const handleChangeAllNature: CheckboxInstance['onChange'] = (value) => {
  indeterminateNature.value = false
  if (value) {
    checkedAllNature.value = true
    dialogForm.value.natures = _.cloneDeep(checkAllNatureList.value)
    dialogForm.value.naturesTitle = _.cloneDeep(checkAllNatureTitleList.value)
  } else {
    checkedAllNature.value = false
    dialogForm.value.natures = []
    dialogForm.value.naturesTitle = []
  }
}
const handleCheckNature = (values: Array<string | number>, node: any) => {
  dialogForm.value.naturesTitle = []
  if (node.checkedNodes?.length) {
    node.checkedNodes.map((item: any) => {
      dialogForm.value.naturesTitle?.push(item.title)
    })
  }
  if (values.length === checkAllNatureList.value.length) {
    checkedAllNature.value = true
    indeterminateNature.value = false
  } else if (values.length === 0) {
    checkedAllNature.value = false
    indeterminateNature.value = false
  } else {
    checkedAllNature.value = false
    indeterminateNature.value = true
  }
}
</script>

<style lang="scss" scoped>
.job-report {
  background-color: inherit !important;
  :deep(.job-report-block) {
    @for $i from 0 through 10 {
      .width-#{10 * $i} {
        width: 10% * $i;
      }
      .height-#{10 * $i} {
        height: 10% * $i;
      }
    }
    @for $i from 0 through 40 {
      .width#{80+(10 * $i)} {
        width: 80px + 10px * $i;
      }
      .height#{100+(10 * $i)} {
        height: 100px + 10px * $i;
      }
    }
    .arco-table-header {
      .arco-table-th-title {
        font-weight: bold;
      }
    }
    .leval {
      width: 14px;
      height: 14px;
    }
    .bg1 {
      background-color: #f0f8fe;
    }
    .bg2 {
      background-color: #f1fef1;
    }
    .bg3 {
      background-color: #ffe5bb;
    }
    .bg4 {
      background-color: #f4f4ff;
    }
    .job-item {
      height: calc(100% - 20px);
      &-text {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
    }
    .rise {
      color: #f53f3f;
    }
    .decline {
      color: #00b42a;
    }
    .progress-img {
      width: 12px;
      height: 12px;
      margin-left: 2px;
    }
  }
}
.modal {
  &-block {
    display: flex;
  }
  &-row {
    width: 50%;
  }
  &-title {
    margin-bottom: 12px;
  }
  &-col {
    height: 400px;
    border: 1px solid #dedede;
    padding: 6px;
    border-radius: 4px;
    .arco-checkbox {
      display: block;
      line-height: 32px;
    }
  }
}
</style>
