<template>
  <div class="call-report">
    <div class="call-report-block">
      <a-row class="mb12">
        <a-col :span="15">
          <a-card>
            <template #title>
              <span class="f14 bold">呼叫数据总览</span>
            </template>
            <template #extra>
              <div>
                <a-range-picker v-model="callTotalForm.date" :disabled-date="disabledDate" @change="getCallTotalData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (callTotalForm.date && callTotalForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleCallTotalFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div class="height530">
              <div class="height110 flex">
                <div class="call-item">
                  <div class="call-item-title">通话总数</div>
                  <div class="call-item-block">
                    <img src="./assets/call.png" style="background-color: #ffe5bb" />
                    <span>{{ formatWithThousandSeparator(callTotalData.callCount || 0) }}</span>
                  </div>
                </div>
                <div class="call-item">
                  <div class="call-item-title">接通总数</div>
                  <div class="call-item-block">
                    <img src="./assets/like.png" style="background-color: #e8fffb" />
                    <span>{{ formatWithThousandSeparator(callTotalData.throughCount || 0) }}</span>
                  </div>
                </div>
                <div class="call-item">
                  <div class="call-item-title">通话总时长</div>
                  <div class="call-item-block">
                    <img src="./assets/time.png" style="background-color: #e9f3ff" />
                    <span>{{ formatWithThousandSeparator(callTotalData.callTimeTotal || 0) }}</span>
                  </div>
                </div>
                <div class="call-item">
                  <div class="call-item-title">活跃坐席数</div>
                  <div class="call-item-block">
                    <img src="./assets/people.png" style="background-color: #f5e8ff" />
                    <span>{{ formatWithThousandSeparator(callTotalData.agentCount || 0) }}</span>
                  </div>
                </div>
              </div>
              <div id="call-total-charts" class="height420"></div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="9" class="pl12">
          <a-card>
            <template #title>
              <span class="f14 bold">在线坐席统计</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="seatsTotalForm.keyword"
                  class="width100"
                  :options="selectAgentOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getSeatsTotalData"
                />
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleSeatsTotalFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="seats-total-charts" class="height220"></div>
          </a-card>
          <a-card class="mt10">
            <template #title>
              <span class="f14 bold">有效接通率</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="connectForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getConnectData"
                />
                <a-range-picker v-model="connectForm.date" :disabled-date="disabledDate" @change="getConnectData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (connectForm.date && connectForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleConnectFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="connect-charts" class="height220"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row class="mb12">
        <a-col :span="8" class="pr8">
          <a-card>
            <template #title>
              <span class="f14 bold">人均通话数</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="perCallForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getPerCallData"
                />
                <a-range-picker v-model="perCallForm.date" :disabled-date="disabledDate" @change="getPerCallData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (perCallForm.date && perCallForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handlePerCallFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="perCall-charts" class="height240"></div>
          </a-card>
        </a-col>
        <a-col :span="8" class="pl4 pr4">
          <a-card>
            <template #title>
              <span class="f14 bold">人均拨打时长</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="perDurationForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getPerDurationData"
                />
                <a-range-picker
                  v-model="perDurationForm.date"
                  :disabled-date="disabledDate"
                  @change="getPerDurationData"
                >
                  <a-button class="ml6 width170" size="mini">
                    {{ (perDurationForm.date && perDurationForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handlePerDurationFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="perDuration-charts" class="height240"></div>
          </a-card>
        </a-col>
        <a-col :span="8" class="pl8">
          <a-card>
            <template #title>
              <span class="f14 bold">有效通话平均时长</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="validDurationForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getValidDurationData"
                />
                <a-range-picker
                  v-model="validDurationForm.date"
                  :disabled-date="disabledDate"
                  @change="getValidDurationData"
                >
                  <a-button class="ml6 width170" size="mini">
                    {{ (validDurationForm.date && validDurationForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleValidDurationFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="validDuration-charts" class="height240"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row>
        <a-col>
          <a-card>
            <template #title>
              <span class="f14 bold">呼损率趋势分析</span>
            </template>
            <template #extra>
              <div>
                <a-select
                  v-model="tendencyForm.keyword"
                  class="width100"
                  :options="selectOptions"
                  placeholder="请选择"
                  size="mini"
                  allow-clear
                  @change="getTendencyData"
                />
                <a-range-picker v-model="tendencyForm.date" :disabled-date="disabledDate" @change="getTendencyData">
                  <a-button class="ml6 width170" size="mini">
                    {{ (tendencyForm.date && tendencyForm.date.join(' - ')) || '请选择日期范围' }}
                  </a-button>
                </a-range-picker>
                <a-tooltip content="筛选">
                  <a-button class="ml6" size="mini" type="text" @click="handleTendencyFilter">
                    <template #icon>
                      <icon-filter />
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </template>
            <div id="tendency-charts" class="height300"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <a-modal
      width="600px"
      title="统计范围选择"
      :visible="dialogVisible"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <div class="modal-block">
        <div class="modal-row pr6">
          <div class="modal-title">调解组织：</div>
          <div class="modal-col">
            <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
              <a-checkbox
                :model-value="checkedAllTree"
                :indeterminate="indeterminateTree"
                @change="handleChangeAllTree"
              >
                全部
              </a-checkbox>
              <a-tree
                ref="modalTree"
                v-model:checked-keys="dialogForm.trees"
                :checkable="true"
                check-strictly
                :data="analysisOptions"
                @check="handleCheckTrees"
              />
            </a-scrollbar>
          </div>
        </div>
        <div class="modal-row pl6">
          <div class="modal-title">案由：</div>
          <div class="modal-col">
            <a-scrollbar outer-style="height: 100%;" style="height: 100%; overflow-y: auto">
              <a-checkbox
                :model-value="checkedAllNature"
                :indeterminate="indeterminateNature"
                @change="handleChangeAllNature"
              >
                全部
              </a-checkbox>
              <a-tree
                ref="modalNature"
                v-model:checked-keys="dialogForm.natures"
                :checkable="true"
                check-strictly
                :data="natureOptions"
                @check="handleCheckNature"
              />
            </a-scrollbar>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getStatisticsCondition, getCallDataOverview } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getAverageEffectiveCall, getCallFailureRate } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getOnlineAgentStatistics, getValidAnswerRate } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'
import { getCallsPerPerson, getAvgCallTime } from '@/api/arcoApi/mediateManage/jobAnalysis/analysisReport'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { CheckboxInstance } from '@arco-design/web-vue/es/checkbox'
import { ref, onMounted, nextTick, markRaw, reactive } from 'vue'
import * as echarts from 'echarts'
import _ from 'lodash'

const selectOptions = ref<SelectOptionData[]>([])
const selectAgentOptions = ref<SelectOptionData[]>([])
const analysisOptions = ref<any[]>([])
const checkAllAnysisList = ref<(string | number)[]>([])
const checkAllAnysisNodeList = ref<STATISTICS_ROUND_TYPE[]>([])
const natureOptions = ref<any[]>([])
const checkAllNatureList = ref<(string | number)[]>([])
const checkAllNatureTitleList = ref<string[]>([])

const getStatisticsConditionData = async () => {
  let res: any = await getStatisticsCondition()
  if (res && res.groupConditions) {
    selectOptions.value = []
    for (const key in res.groupConditions) {
      if (Object.prototype.hasOwnProperty.call(res.groupConditions, key)) {
        const element = res.groupConditions[key]
        selectOptions.value.push({ label: element, value: key })
      }
    }
  }
  if (res && res.agentCondition) {
    selectAgentOptions.value = []
    for (const key in res.agentCondition) {
      if (Object.prototype.hasOwnProperty.call(res.agentCondition, key)) {
        const element = res.agentCondition[key]
        selectAgentOptions.value.push({ label: element, value: key })
      }
    }
  }
  if (res && res.statisticsTreeNodeList?.length) {
    let list: any[] = _.cloneDeep(res.statisticsTreeNodeList)
    analysisOptions.value = list
    let obj: any = treeToArrayAnysis(list)
    checkAllAnysisList.value = _.cloneDeep(obj.ids)
    checkAllAnysisNodeList.value = _.cloneDeep(obj.nodes)
  }
  if (res && res.natureInfo?.length) {
    let list: any[] = _.cloneDeep(res.natureInfo)
    natureOptions.value = list.map((item: any) => ({ key: item.dictKey, title: item.dictTag }))
    let obj: any = treeToArrayNature(list)
    checkAllNatureList.value = _.cloneDeep(obj.ids)
    checkAllNatureTitleList.value = _.cloneDeep(obj.titles)
  }
}
function treeToArrayAnysis(root: any) {
  const stack = [...root]
  const ids: (string | number)[] = []
  const nodes: any[] = []

  while (stack.length > 0) {
    const node: any = stack.pop()
    ids.push(node.key)
    nodes.push({ key: node.key, type: node.type })

    if (node.children) {
      stack.push(...node.children.reverse())
    }
  }

  return { ids, nodes }
}
function treeToArrayNature(root: any) {
  const stack = [...root]
  const ids: (string | number)[] = []
  const titles: string[] = []

  while (stack.length > 0) {
    const node: any = stack.pop()
    ids.push(node.dictKey)
    titles.push(node.dictTag)

    if (node.children) {
      stack.push(...node.children.reverse())
    }
  }

  return { ids, titles }
}
getStatisticsConditionData()

const datetimeFormat = (date: string | Date | number = '', fmt = 'yyyy-MM-dd') => {
  let _date = date ? new Date(date) : new Date()
  let o: { [x: string]: number } = {
    'M+': _date.getMonth() + 1, // 月份
    'd+': _date.getDate(), // 日
    'h+': _date.getHours(), // 小时
    'm+': _date.getMinutes(), // 分
    's+': _date.getSeconds(), // 秒
    'q+': Math.floor(_date.getMonth() / 3) + 1, // 季度
    S: _date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (_date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k].toString() : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fmt
}

const initDate = reactive<[string | Date | number, string | Date | number]>([
  datetimeFormat(Date.now() - 30 * 24 * 60 * 60 * 1000),
  datetimeFormat()
])

const callTotalForm = ref<COMMON_FORM_TYPE>({ date: initDate, trees: [], treesNode: [], natures: [], naturesTitle: [] })

const seatsTotalForm = ref<COMMON_FORM_TYPE>({ keyword: '', trees: [], treesNode: [], natures: [], naturesTitle: [] })

const connectForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const perCallForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const perDurationForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const validDurationForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const tendencyForm = ref<COMMON_FORM_TYPE>({
  keyword: '',
  date: initDate,
  trees: [],
  treesNode: [],
  natures: [],
  naturesTitle: []
})

const disabledDate = (current: Date | string | number) => {
  return current > new Date()
}

const formatWithThousandSeparator = (number: number) => {
  return number.toLocaleString()
}

interface CALL_TOTAL_DATA_TYPE {
  agentCount?: number
  callCount?: number
  callTimeTotal?: number
  throughCount?: number
  dailyStatistic?: XY_DATA_TYPE
}
const callTotalData = ref<CALL_TOTAL_DATA_TYPE>({
  agentCount: 0,
  callCount: 0,
  callTimeTotal: 0,
  throughCount: 0,
  dailyStatistic: { xAxis: [], yAxis: [] }
})
const seatsTotalData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const connectData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const perCallData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const perDurationData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const validDurationData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })
const tendencyData = ref<XY_DATA_TYPE>({ xAxis: [], yAxis: [] })

const callTotalCharts = ref<echarts.EChartsType>()
const seatsTotalCharts = ref<echarts.EChartsType>()
const connectCharts = ref<echarts.EChartsType>()
const perCallCharts = ref<echarts.EChartsType>()
const perDurationCharts = ref<echarts.EChartsType>()
const validDurationCharts = ref<echarts.EChartsType>()
const tendencyCharts = ref<echarts.EChartsType>()

const initCallTotalCharts = () => {
  if (!callTotalCharts.value) {
    callTotalCharts.value = markRaw(echarts.init(document.getElementById('call-total-charts')))
  }
  let callTotalOption = setCallTotalOption()
  callTotalCharts.value.setOption(callTotalOption)
}

const initSeatsTotalCharts = () => {
  if (!seatsTotalCharts.value) {
    seatsTotalCharts.value = markRaw(echarts.init(document.getElementById('seats-total-charts')))
  }
  let seatsTotalOption = setSeatsTotalOption()
  seatsTotalCharts.value.setOption(seatsTotalOption)
}

const initConnectCharts = () => {
  if (!connectCharts.value) {
    connectCharts.value = markRaw(echarts.init(document.getElementById('connect-charts')))
  }
  let connectOption = setConnectOption()
  connectCharts.value.setOption(connectOption)
}

const initPerCallCharts = () => {
  if (!perCallCharts.value) {
    perCallCharts.value = markRaw(echarts.init(document.getElementById('perCall-charts')))
  }
  let perCallOption = setSlideBarOption(perCallData.value, '人均通话数', ['#2478f2', '#84b7f9'], '个')
  perCallCharts.value.setOption(perCallOption)
}

const initPerDurationCharts = () => {
  if (!perDurationCharts.value) {
    perDurationCharts.value = markRaw(echarts.init(document.getElementById('perDuration-charts')))
  }
  let perDurationOption = setSlideBarOption(perDurationData.value, '人均拨打时长', ['#3f95c2', '#6dc8e1'], '分')
  perDurationCharts.value.setOption(perDurationOption)
}

const initValidDurationCharts = () => {
  if (!validDurationCharts.value) {
    validDurationCharts.value = markRaw(echarts.init(document.getElementById('validDuration-charts')))
  }
  let validDurationOption = setSlideBarOption(validDurationData.value, '有效通话平均时长', ['#f9c78b', '#ffe5bb'], '分')
  validDurationCharts.value.setOption(validDurationOption)
}

const initTendencyCharts = () => {
  if (!tendencyCharts.value) {
    tendencyCharts.value = markRaw(echarts.init(document.getElementById('tendency-charts')))
  }
  let tendencyOption = setTendencyOption()
  tendencyCharts.value.setOption(tendencyOption, true)
}

const setCallTotalOption = () => {
  let data: CALL_TOTAL_DATA_TYPE = callTotalData.value
  let series: echarts.LineSeriesOption[] = []
  let xAxisData: any[] = []
  if (data.dailyStatistic) {
    data.dailyStatistic.yAxis.map((item: YAXIS_TYPE) => {
      if (item.type && item.date?.length) {
        series.push({
          type: 'line',
          smooth: true,
          symbol: 'none',
          data: item.date,
          name: item.type,
          areaStyle: {
            color:
              item.type === '通话总数'
                ? '#fff9ec'
                : item.type === '接通总数'
                  ? '#e4fafc'
                  : item.type === '通话总时长'
                    ? '#ecf3ff'
                    : item.type === '活跃坐席数'
                      ? '#f1eaef'
                      : ''
          }
        })
      }
    })
    xAxisData = data.dailyStatistic.xAxis.map((item: string) => {
      return { value: item, textStyle: { fontSize: 10 } }
    })
  }

  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '60px', bottom: '60px', left: '60px', right: '10px' },
    legend: { top: '10px', textStyle: { fontSize: 10 } },
    xAxis: { type: 'category', data: xAxisData },
    yAxis: { type: 'value' },
    color: ['#ff830b', '#63e7e9', '#3c6fff', '#9964ca'],
    series: series,
    dataZoom: [
      {
        type: 'slider',
        show: true,
        startValue: 0, //数据窗口范围的起始百分比
        height: 15,
        bottom: 10,
        left: 40,
        right: 10
      }
    ]
  }
  return option
}

const setSeatsTotalOption = () => {
  let data: XY_DATA_TYPE = seatsTotalData.value
  let series: echarts.BarSeriesOption[] = []
  data.yAxis.map((item: YAXIS_TYPE) => {
    if (item.date?.length) {
      series.push({
        data: item.date,
        name: '坐席数',
        type: 'bar',
        barWidth: 10,
        color: '#4086ff',
        itemStyle: { borderRadius: [0, 10, 10, 0] },
        tooltip: {
          valueFormatter: function (value) {
            return value + ' 人'
          }
        }
      })
    }
  })
  let xAxisData = data.xAxis.map((item: string) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '20px', bottom: '20px', left: '80px', right: '10px' },
    xAxis: { type: 'value', minInterval: 1 },
    yAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0,
        formatter: function (params) {
          var newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber = 6 // 单行显示文字个数
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p === rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      },
      axisLine: { show: false },
      axisTick: { show: false }
    },
    series: series
  }
  return option
}

const setConnectOption = () => {
  let data: XY_DATA_TYPE = connectData.value
  let series: echarts.BarSeriesOption[] = []
  data.yAxis.map((item: YAXIS_TYPE) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'bar',
        data: item.date,
        name: item.type,
        stack: item.type,
        barWidth: '20px',
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          }
        }
      })
    }
  })
  let xAxisData = data.xAxis.map((item: string) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '40px', bottom: '30px', left: '40px', right: '10px' },
    legend: { top: 0, textStyle: { fontSize: 10 } },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0,
        formatter: function (params) {
          var newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber = xAxisData.length <= 3 ? 9 : xAxisData.length === 4 ? 7 : 6 // 单行显示文字个数
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p === rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%' // 在这里设置单位
      }
    },
    color: ['#3673e8', '#15c968'],
    series: series
  }
  return option
}

const setSlideBarOption = (data: XY_DATA_TYPE, name: string, color: Array<string>, unit: string) => {
  let series: echarts.BarSeriesOption[] = []
  data.yAxis.map((item: YAXIS_TYPE) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'bar',
        data: item.date,
        name: item.type,
        stack: name,
        tooltip: {
          valueFormatter: function (value) {
            return value + '' + unit
          }
        }
      })
    }
  })
  let xAxisData = data.xAxis.map((item: string) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { top: '30px', bottom: '60px', left: '50px', right: '10px' },
    legend: { top: 0, textStyle: { fontSize: 10 } },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0,
        formatter: function (params) {
          var newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber = xAxisData.length <= 3 ? 9 : xAxisData.length === 4 ? 7 : 6 // 单行显示文字个数
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p === rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
    },
    yAxis: { type: 'value' },
    color: color,
    series: series,
    dataZoom: [
      {
        type: 'slider',
        show: true,
        startValue: 0, //数据窗口范围的起始百分比
        endValue: 4,
        height: 15,
        bottom: 10,
        left: 40,
        right: 10
      }
    ]
  }
  return option
}

const setTendencyOption = () => {
  let data: XY_DATA_TYPE = tendencyData.value
  let series: echarts.LineSeriesOption[] = []
  data.yAxis.map((item: YAXIS_TYPE) => {
    if (item.type && item.date?.length) {
      series.push({
        type: 'line',
        smooth: true,
        symbol: 'none',
        data: item.date,
        name: item.type,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %'
          }
        }
      })
    }
  })
  let xAxisData = data.xAxis.map((item: string) => {
    return { value: item, textStyle: { fontSize: 10 } }
  })
  let option = {
    grid: { top: '60px', bottom: '60px', left: '40px', right: '30px' },
    legend: { top: '10px' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    color: [
      '#9964ca',
      '#3c6fff',
      '#63e7e9',
      '#ff830b',
      '#3673e8',
      '#15c968',
      '#2478f2',
      '#84b7f9',
      '#3f95c2',
      '#6dc8e1'
    ],
    xAxis: { type: 'category', data: xAxisData },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%' // 在这里设置单位
      }
    },
    series: series,
    dataZoom: [
      {
        type: 'slider',
        show: true,
        startValue: 0, //数据窗口范围的起始百分比
        height: 15,
        bottom: 10,
        left: 40,
        right: 30
      }
    ]
  }
  return option
}

onMounted(() => {
  nextTick(() => {
    initData()
  })
})

const initData = async () => {
  const apiPromises = [
    getCallTotalData(),
    getSeatsTotalData(),
    getConnectData(),
    getPerCallData(),
    getPerDurationData(),
    getValidDurationData(),
    getTendencyData()
  ]
  await Promise.all(apiPromises)
}

const commonParams = (formData: COMMON_FORM_TYPE) => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = {
    caseCauseList: formData.naturesTitle,
    statisticsRound: formData.treesNode
  }
  if (formData.keyword) {
    params.groupCondition = formData.keyword
  }
  if (formData.date && formData.date.length === 2) {
    params.dateStart = formData.date[0]
    params.dateEnd = formData.date[1]
  }
  return params
}
const getCallTotalData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(callTotalForm.value)
  let res: any = await getCallDataOverview(params)
  if (res) {
    callTotalData.value = {
      agentCount: parseInt(res.agentCount || 0,10),
      callCount: parseInt(res.callCount || 0,10),
      callTimeTotal: parseFloat(res.callTimeTotal || 0,10),
      throughCount: parseInt(res.throughCount || 0,10),
      dailyStatistic: res.dailyStatistic
    }
  }
  initCallTotalCharts()
}
const getSeatsTotalData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(seatsTotalForm.value)
  let res: any = await getOnlineAgentStatistics(params)
  if (res) {
    seatsTotalData.value = res
  }
  initSeatsTotalCharts()
}
const getConnectData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(connectForm.value)
  let res: any = await getValidAnswerRate(params)
  if (res) {
    connectData.value = res
  }
  initConnectCharts()
}
const getPerCallData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(perCallForm.value)
  let res: any = await getCallsPerPerson(params)
  if (res) {
    perCallData.value = res
  }
  initPerCallCharts()
}
const getPerDurationData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(perDurationForm.value)
  let res: any = await getAvgCallTime(params)
  if (res) {
    perDurationData.value = res
  }
  initPerDurationCharts()
}
const getValidDurationData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(validDurationForm.value)
  let res: any = await getAverageEffectiveCall(params)
  if (res) {
    validDurationData.value = res
  }
  initValidDurationCharts()
}
const getTendencyData = async () => {
  let params: REQUEST_POST_STATISTIC_PARAM_TYPE = commonParams(tendencyForm.value)
  let res: any = await getCallFailureRate(params)
  if (res) {
    tendencyData.value = res
  }
  initTendencyCharts()
}
const dialogVisible = ref<boolean>(false)
const checkedAllTree = ref<boolean>(false)
const indeterminateTree = ref<boolean>(false)
const checkedAllNature = ref<boolean>(false)
const indeterminateNature = ref<boolean>(false)
const dialogForm = ref<COMMON_FORM_TYPE>({ key: '', trees: [], treesNode: [], natures: [], naturesTitle: [] })
const modalTree = ref()
const modalNature = ref()
const commonFilter = (key: string, formData) => {
  dialogForm.value = {
    key,
    trees: _.cloneDeep(formData.trees),
    treesNode: _.cloneDeep(formData.treesNode),
    natures: _.cloneDeep(formData.natures),
    naturesTitle: _.cloneDeep(formData.naturesTitle)
  }
  if (dialogForm.value.trees?.length) {
    if (dialogForm.value.trees?.length === checkAllAnysisList.value.length) {
      checkedAllTree.value = true
      indeterminateTree.value = false
    } else {
      checkedAllTree.value = false
      indeterminateTree.value = true
    }
  }
  if (dialogForm.value.natures?.length) {
    if (dialogForm.value.natures.length === checkAllNatureList.value.length) {
      checkedAllNature.value = true
      indeterminateNature.value = false
    } else {
      checkedAllNature.value = false
      indeterminateNature.value = true
    }
  }
  dialogVisible.value = true
  nextTick(() => {
    modalTree.value?.expandAll(true)
    modalNature.value?.expandAll(true)
  })
}
const handleCallTotalFilter = () => {
  commonFilter('callTotal', callTotalForm.value)
}
const handleSeatsTotalFilter = () => {
  commonFilter('seatsTotal', seatsTotalForm.value)
}
const handleConnectFilter = () => {
  commonFilter('connect', connectForm.value)
}
const handlePerCallFilter = () => {
  commonFilter('perCall', perCallForm.value)
}
const handlePerDurationFilter = () => {
  commonFilter('perDuration', perDurationForm.value)
}
const handleValidDurationFilter = () => {
  commonFilter('validDuration', validDurationForm.value)
}
const handleTendencyFilter = () => {
  commonFilter('tendency', tendencyForm.value)
}

const handleDialogConfirm = async () => {
  switch (dialogForm.value.key) {
    case 'callTotal':
      callTotalForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      callTotalForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      callTotalForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      callTotalForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getCallTotalData()
      break
    case 'seatsTotal':
      seatsTotalForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      seatsTotalForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      seatsTotalForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      seatsTotalForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getSeatsTotalData()
      break
    case 'connect':
      connectForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      connectForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      connectForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      connectForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getConnectData()
      break
    case 'perCall':
      perCallForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      perCallForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      perCallForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      perCallForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getPerCallData()
      break
    case 'perDuration':
      perDurationForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      perDurationForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      perDurationForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      perDurationForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getPerDurationData()
      break
    case 'validDuration':
      validDurationForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      validDurationForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      validDurationForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      validDurationForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getValidDurationData()
      break
    case 'tendency':
      tendencyForm.value.trees = _.cloneDeep(dialogForm.value.trees)
      tendencyForm.value.treesNode = _.cloneDeep(dialogForm.value.treesNode)
      tendencyForm.value.natures = _.cloneDeep(dialogForm.value.natures)
      tendencyForm.value.naturesTitle = _.cloneDeep(dialogForm.value.naturesTitle)
      getTendencyData()
      break
    default:
      break
  }
  handleDialogCancel()
}
const handleDialogCancel = () => {
  dialogForm.value = { key: '', trees: [], treesNode: [], natures: [], naturesTitle: [] }
  modalTree.value?.selectAll(false)
  modalNature.value?.selectAll(false)
  checkedAllTree.value = false
  indeterminateTree.value = false
  checkedAllNature.value = false
  indeterminateNature.value = false
  dialogVisible.value = false
}
const handleChangeAllTree: CheckboxInstance['onChange'] = (value) => {
  indeterminateTree.value = false
  if (value) {
    checkedAllTree.value = true
    dialogForm.value.trees = _.cloneDeep(checkAllAnysisList.value)
    dialogForm.value.treesNode = _.cloneDeep(checkAllAnysisNodeList.value)
  } else {
    checkedAllTree.value = false
    dialogForm.value.trees = []
    dialogForm.value.treesNode = []
  }
}
const handleCheckTrees = (values: Array<string | number>, node: any) => {
  dialogForm.value.treesNode = []
  if (node.checkedNodes?.length) {
    node.checkedNodes.map((item: any) => {
      dialogForm.value.treesNode?.push({ key: item.key, type: item.type })
    })
  }
  if (values.length === checkAllAnysisList.value.length) {
    checkedAllTree.value = true
    indeterminateTree.value = false
  } else if (values.length === 0) {
    checkedAllTree.value = false
    indeterminateTree.value = false
  } else {
    checkedAllTree.value = false
    indeterminateTree.value = true
  }
}
const handleChangeAllNature: CheckboxInstance['onChange'] = (value) => {
  indeterminateNature.value = false
  if (value) {
    checkedAllNature.value = true
    dialogForm.value.natures = _.cloneDeep(checkAllNatureList.value)
    dialogForm.value.naturesTitle = _.cloneDeep(checkAllNatureTitleList.value)
  } else {
    checkedAllNature.value = false
    dialogForm.value.natures = []
    dialogForm.value.naturesTitle = []
  }
}
const handleCheckNature = (values: Array<string | number>, node: any) => {
  dialogForm.value.naturesTitle = []
  if (node.checkedNodes?.length) {
    node.checkedNodes.map((item: any) => {
      dialogForm.value.naturesTitle?.push(item.title)
    })
  }
  if (values.length === checkAllNatureList.value.length) {
    checkedAllNature.value = true
    indeterminateNature.value = false
  } else if (values.length === 0) {
    checkedAllNature.value = false
    indeterminateNature.value = false
  } else {
    checkedAllNature.value = false
    indeterminateNature.value = true
  }
}
</script>

<style lang="scss" scoped>
.call-report {
  background-color: inherit !important;

  :deep(.call-report-block) {
    @for $i from 0 through 10 {
      .width-#{10 * $i} {
        width: 10% * $i;
      }

      .height-#{10 * $i} {
        height: 10% * $i;
      }
    }

    @for $i from 0 through 40 {
      .width#{80+(10 * $i)} {
        width: 80px + 10px * $i;
      }

      .height#{100+(10 * $i)} {
        height: 100px + 10px * $i;
      }
    }

    .call-item {
      flex: 1;

      &-title {
        margin-top: 12px;
        margin-bottom: 20px;
        font-weight: bold;
      }

      &-block {
        display: flex;
        align-items: center;

        img {
          width: 30px;
          height: 30px;
          padding: 6px;
          border-radius: 4px;
        }

        span {
          margin-left: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #000;
        }
      }
    }
  }
}

.modal {
  &-block {
    display: flex;
  }

  &-row {
    width: 50%;
  }

  &-title {
    margin-bottom: 12px;
  }

  &-col {
    height: 400px;
    border: 1px solid #dedede;
    padding: 6px;
    border-radius: 4px;

    .arco-checkbox {
      display: block;
      line-height: 32px;
    }
  }
}
</style>
