<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="mediatorId" label="记录归属">
                  <Mdtcascader v-model:value="searchForm.mediatorId"></Mdtcascader>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="litigantName" label="联系人">
                  <a-input v-model="searchForm.litigantName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="phoneCheckStatus" label="检测结果">
                  <a-select
                    v-model="searchForm.phoneCheckStatus"
                    :options="phoneCheckStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="phoneStatus" label="号码状态">
                  <a-select
                    v-model="searchForm.phoneStatus"
                    :options="phoneStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="startTime" label="检测时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="recordIds"
        row-key="recordId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #phoneStatus="{ record }">
          <span> {{ phoneStatusObj[record.phoneStatus] || record.phoneStatus }} </span>
        </template>
        <template #phoneCheckStatus="{ record }">
          <span> {{ phoneCheckStatusObj[record.phoneCheckStatus] || record.phoneCheckStatus }} </span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import { queryNumberDetectRecord } from '@/api/arcoApi/mediateManage/jobLog/numberDetectRecord'
import { exportPhoneCheckPageLog } from '@/api/arcoApi/mediateManage/jobLog/numberDetectRecord'
import Mdtcascader from '@/components/cascader/index.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'

import { dictEnumValToObject } from '@/utils'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import dict from '@/dict/mediateManage'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    operatorCompanyId: '',
    phoneCheckStatus: '',
    phoneCheckResult: '',
    checkDateStart: '',
    litigantPhone: '',
    entrustsName: '',
    identityType: '',
    litigantName: '',
    litigantType: '',
    operatorName: '',
    httpResponse: '',
    checkDateEnd: '',
    phoneStatus: '',
    mediatorId: '',
    litigantId: '',
    entrustsId: '',
    operatorId: '',
    httpStatus: '',
    checkDate: '',
    recordId: '',
    allTime: [],
    caseId: '',
    caseNo: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('号码检测记录')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<REQUEST_POST_NUMBERDETECT_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_NUMBERDETECT_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const phoneCheckStatusOptions = ref<SelectOptionData[]>(dict.phoneCheckStatusOptions)
const phoneStatusOptions = ref<SelectOptionData[]>(dict.phoneStatusOptions)
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  {
    width: 240,
    tooltip: true,
    ellipsis: true,
    fixed: 'left',
    title: '案件编号',
    dataIndex: 'caseNo',
    slotName: 'caseNo'
  },
  { width: 220, tooltip: true, ellipsis: true, title: '案件所属案源方', dataIndex: 'entrustsName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人', dataIndex: 'operatorName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人所属组织', dataIndex: 'operatorCompanyName' },
  { width: 120, tooltip: true, ellipsis: true, title: '检测联系人', dataIndex: 'litigantName' },
  { width: 140, tooltip: true, ellipsis: true, title: '检测号码', dataIndex: 'litigantPhone' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码状态', dataIndex: 'phoneStatus', slotName: 'phoneStatus' },
  { width: 160, title: '检测结果', dataIndex: 'phoneCheckStatus', slotName: 'phoneCheckStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '操作时间', dataIndex: 'checkDate', align: 'center' }
])

const phoneStatusObj = computed(() => {
  return dictEnumValToObject(phoneStatusOptions.value)
})

const phoneCheckStatusObj = computed(() => {
  return dictEnumValToObject(phoneCheckStatusOptions.value)
})

const handleExcelDownload = async () => {
  let excelData = await exportPhoneCheckPageLog(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  recordIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.checkDateEnd = timeArr[1] || ''
    searchForm.value.checkDateStart = timeArr[0] || ''
  } else {
    searchForm.value.checkDateEnd = ''
    searchForm.value.checkDateStart = ''
  }
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await queryNumberDetectRecord({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
