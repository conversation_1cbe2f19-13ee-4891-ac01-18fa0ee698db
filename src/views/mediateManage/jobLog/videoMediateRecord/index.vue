<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="mediatorId" label="记录归属">
                  <Mdtcascader v-model:value="searchForm.mediatorId"></Mdtcascader>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="mediatorName" label="参会人">
                  <a-input v-model="searchForm.participant" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="meetingStatus" label="会议状态">
                  <a-select
                    v-model="searchForm.meetingStatus"
                    :options="meetingStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="会议时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="recordIds"
        row-key="recordId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #meetingStatus="{ record }">
          {{ mediateStatusObj[record.meetingStatus] || record.meetingStatus }}
        </template>
        <template #meetingTime="{ record }">
          <span v-if="record.meetingTime"> {{ record.meetingTime || '--' }}分 </span>
        </template>
        <template #operations="{ record }">
          <a-button type="text" @click="handleDownload(record.filePath)">下载</a-button>
          <a-button type="text" @click="handleVideoPlay(record.filePath)">播放</a-button>
        </template>
      </a-table>
    </a-card>
    <a-modal
      v-model:visible="videoVisible"
      :footer="false"
      :width="500"
      :body-style="{ textAlign: 'center' }"
      :on-before-cancel="handleStopPlay"
      title="录屏播放"
    >
      <video ref="videoRef" :src="videoUrl" width="100%" height="320px" autoPlay :muted="true" :controls="true">
        您的浏览器不支持video
      </video>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { reportMeetingRecord, getMeetingVideoPath } from '@/api/arcoApi/mediateManage/jobLog/videoMediateRecord'
import { getVideoMediateRecordList } from '@/api/arcoApi/mediateManage/jobLog/videoMediateRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import Mdtcascader from '@/components/cascader/index.vue'

import { computed, ref, reactive, watch, nextTick } from 'vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { downloadVideo } from '@/api/commonApi/file'
import { Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    caseNo: '',
    mediationEndTime: '',
    mediationStartTime: '',
    mediatorId: '',
    mediatorName: '',
    meetingStatus: '',
    participant: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('视频调解记录')
// const pageEnName = ref('VideoMediate')

const searchForm = ref<REQUEST_POST_VIDEOMEDIATE_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_VIDEOMEDIATE_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const videoRef = ref<HTMLVideoElement>()
const videoVisible = ref(false)
const videoUrl = ref('')

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const meetingStatusOptions = ref<SelectOptionData[]>(dict.meetingStatusOptions)
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', dataIndex: 'caseNo', slotName: 'caseNo' },
  { width: 180, tooltip: true, ellipsis: true, title: '案件所属案源方', dataIndex: 'entrustsName' },
  { width: 180, tooltip: true, ellipsis: true, title: '会议名称', dataIndex: 'meetingName' },
  { width: 140, align: 'center', title: '会议发起人', dataIndex: 'mediatorName' },
  { width: 200, tooltip: true, ellipsis: true, title: '所属组织', dataIndex: 'companyName', align: 'center' },
  { width: 170, align: 'center', title: '参会人身份', dataIndex: 'participantType' },
  { width: 170, align: 'center', title: '会议状态', dataIndex: 'meetingStatus', slotName: 'meetingStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '会议时间', dataIndex: 'mediationTime', align: 'center' },
  { width: 140, title: '会议时长', dataIndex: 'meetingTime', align: 'center', slotName: 'meetingTime' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const mediateStatusObj = computed(() => {
  return dictEnumValToObject(meetingStatusOptions.value)
})

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const handleExcelDownload = async () => {
  let excelData = await reportMeetingRecord(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

const handleDownload = async (filePath: string) => {
  if (filePath) {
    location.assign(downloadVideo({ filePath }))
  } else {
    Message.warning('这条视频调解记录没有录屏')
  }
}

// 播放录屏
const handleVideoPlay = (filePath: string) => {
  if (filePath) {
    getMeetingVideoPath({ filePath }).then((res) => {
      if (res) {
        videoVisible.value = true
        const newPath = res.replace('http', 'https')
        nextTick(() => {
          videoUrl.value = newPath
          videoRef.value?.play()
        })
      } else {
        Message.warning('这条记录没有录屏')
      }
    })
  } else {
    Message.warning('这条记录没有录屏')
  }
}

const handleStopPlay = () => {
  if (videoRef.value) {
    videoRef.value.pause()
    videoRef.value.currentTime = 0
  }
  return true
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.mediationEndTime = timeArr[1] || ''
    searchForm.value.mediationStartTime = timeArr[0] || ''
  } else {
    searchForm.value.mediationEndTime = ''
    searchForm.value.mediationStartTime = ''
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  recordIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getVideoMediateRecordList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
