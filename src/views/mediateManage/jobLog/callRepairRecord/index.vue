<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="litigantPhone" label="修复号码">
                  <a-input v-model="searchForm.litigantPhone" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="litigantName" label="修复人">
                  <a-input v-model="searchForm.litigantName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="company" label="记录归属">
                  <Mdtcascader v-model:value="searchForm.company"></Mdtcascader>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="matchRepairResult" label="修复结果">
                  <a-select
                    v-model="searchForm.matchRepairResult"
                    :options="repairResultOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="startTime" label="修复时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="repairLitigantId"
        :pageize="size"
        :bordered="true"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :expandable="expandable"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #acquireStatus="{ record }">
          <span> {{ phoneStatusObj[record.acquireStatus] || record.acquireStatus }} </span>
        </template>
        <template #repairStatus="{ record }">
          <span> {{ repairStatusObj[record.repairStatus] || record.repairStatus }} </span>
        </template>
        <template #repairSuccessNet="{ record }">
          <span> {{ repairNetObj[record.repairSuccessNet] || record.repairSuccessNet }} </span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { queryCallRepairRecordNew, exportRepairLog } from '@/api/arcoApi/mediateManage/jobLog/callRepairRecord'
import Mdtcascader from '@/components/cascader/index.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch, h } from 'vue'
import { dictEnumValToObject } from '@/utils'
import { Table } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'

import dict from '@/dict/mediateManage'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    caseNo: '',
    company: '',
    litigantName: '',
    litigantPhone: '',
    matchRepairResult: '',
    repairTimeEnd: '',
    repairTimeStart: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('失联修复记录')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<REQUEST_POST_CALLREPAIR_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CALLREPAIR_LIST_NEW_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const repairResultOptions = ref<SelectOptionData[]>(dict.repairResultOptions)
const repairStatusOptions = ref<SelectOptionData[]>(dict.repairStateOptions)
const phoneStatusOptions = ref<SelectOptionData[]>(dict.phoneStatusOptions)

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', slotName: 'caseNo' },
  { width: 220, tooltip: true, ellipsis: true, title: '系统修复批次号', dataIndex: 'repairLitigantId' },
  { width: 120, tooltip: true, ellipsis: true, title: '修复姓名', dataIndex: 'litigantName' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复身份证号', dataIndex: 'idCard' },
  { width: 150, title: '修复状态', dataIndex: 'repairStatus', slotName: 'repairStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '批次修复结果', dataIndex: 'repairResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复成功所属渠道', slotName: 'repairSuccessNet' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次有效截止时间', dataIndex: 'expireTime' },
  { width: 140, title: '号码获取状态', dataIndex: 'acquireStatus', slotName: 'acquireStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取结果', dataIndex: 'acquireResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取失败原因', dataIndex: 'failMsg' },
  { width: 140, align: 'center', tooltip: true, ellipsis: true, title: '修复号码', dataIndex: 'virtualNo' },
  { width: 200, tooltip: true, ellipsis: true, title: '发起人所属组织', dataIndex: 'companyName' },
  { width: 200, tooltip: true, ellipsis: true, title: '发起人', dataIndex: 'accountName' },
  { width: 180, tooltip: true, ellipsis: true, title: '修复时间', dataIndex: 'repairSuccessTime', align: 'center' }
])

const childColumns = computed<TableColumnData[]>(() => [
  { width: 220, tooltip: true, ellipsis: true, title: '系统修复批次号', dataIndex: 'repairLitigantId' },
  { width: 120, tooltip: true, ellipsis: true, title: '渠道修复批次号', dataIndex: 'repairRecordId' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复渠道', slotName: 'repairNet' },
  { width: 150, title: '修复状态', dataIndex: 'repairStatus', slotName: 'repairStatus' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次修复结果', dataIndex: 'repairResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次修复失败原因', dataIndex: 'repairFailMsg' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次有效截止时间', dataIndex: 'expireTime' },
  { width: 140, title: '号码获取状态', dataIndex: 'acquireStatus', slotName: 'acquireStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取结果', dataIndex: 'acquireResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取失败原因', dataIndex: 'failMsg' },
  { width: 140, align: 'center', tooltip: true, ellipsis: true, title: '修复号码', dataIndex: 'virtualNo' },
  { width: 150, tooltip: true, ellipsis: true, title: '修复时间', dataIndex: 'repairCompleteTime' }
])

const expandable = reactive({
  width: 50,
  expandedRowRender: (record) => {
    if (record.mdtNetRepairRecordDTOList && record.mdtNetRepairRecordDTOList.length) {
      return h('div', { style: { padding: '20px', background: '#fff', border: '1px solid #ccc' } }, [
        h(
          Table,
          {
            bordered: true,
            rowKey: 'repairRecordId',
            columns: childColumns.value,
            data: record.mdtNetRepairRecordDTOList,
            pagination: false,
            scrollbar: { type: 'track' }
          },
          {
            acquireStatus: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', phoneStatusObj.value[record.acquireStatus] || record.acquireStatus)
            },
            batchStatus: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', repairStatusObj.value[record.repairStatus] || record.repairStatus)
            },
            repairNet: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', repairNetObj.value[record.repairNet] || record.repairNet)
            }
          }
        )
      ])
    }
  }
})

const repairStatusObj = computed(() => {
  return dictEnumValToObject(repairStatusOptions.value)
})

const phoneStatusObj = computed(() => {
  return dictEnumValToObject(phoneStatusOptions.value)
})

const repairNetObj = computed(() => {
  return dictEnumValToObject(dict.repairNetOptions)
})

const handleExcelDownload = async () => {
  let excelData = await exportRepairLog(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.repairTimeStart = timeArr[0] || ''
    searchForm.value.repairTimeEnd = timeArr[1] || ''
  } else {
    searchForm.value.repairTimeStart = ''
    searchForm.value.repairTimeEnd = ''
  }
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await queryCallRepairRecordNew({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
