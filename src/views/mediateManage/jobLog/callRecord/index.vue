<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="callerPhone" label="主叫号码">
                  <a-input v-model="searchForm.callerPhone" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="calleePhone" label="被叫号码">
                  <a-input v-model="searchForm.calleePhone" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="mediatorId" label="记录归属">
                  <Mdtcascader v-model:value="searchForm.mediatorId"></Mdtcascader>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="calleeName" label="联系人姓名">
                  <a-input v-model="searchForm.calleeName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="usageMode" label="呼叫类型">
                  <a-select
                    v-model="searchForm.usageMode"
                    :options="usageModeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="创建时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="recordIds"
        row-key="recordId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #timeLength="{ record }">
          <span> {{ record.timeLength || '--' }}秒 </span>
        </template>
        <template #ringLength="{ record }">
          <span> {{ record.ringLength || '--' }}秒 </span>
        </template>
        <template #operations="{ record }">
          <a-button type="text" @click="handleDownload(record.filePath)">下载</a-button>
          <a-button type="text" @click="handleAudioPlay(record.filePath)">播放</a-button>
        </template>
      </a-table>
    </a-card>
    <a-modal
      v-model:visible="audioVisible"
      :footer="false"
      :width="400"
      :body-style="{ textAlign: 'center' }"
      :on-before-cancel="handleStopPlay"
      title="录音播放"
    >
      <audio ref="audioRef" :src="audioUrl" autoplay controls controlslist="nodownload">您的浏览器不支持audio</audio>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { getCallRecordStream, exportCallRecord } from '@/api/arcoApi/mediateManage/jobLog/callRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import { getCallRecordList } from '@/api/arcoApi/mediateManage/jobLog/callRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { computed, ref, reactive, watch, nextTick } from 'vue'
import Mdtcascader from '@/components/cascader/index.vue'
import useLoading from '@/layouts/appArco/hooks/loading'

import { download } from '@/api/commonApi/file'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/mediateManage'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    calleeName: '',
    calleePhone: '',
    callerPhone: '',
    companyId: '',
    companyIds: [],
    entrustsId: '',
    mediatorId: '',
    mediatorIds: [],
    mediatorName: '',
    optDateEnd: '',
    optDateStart: '',
    usageMode: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('呼叫记录')
// const pageEnName = ref('Call')

const searchForm = ref<REQUEST_POST_CALL_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CALL_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const audioRef = ref<HTMLAudioElement>()
const audioVisible = ref(false)
const audioUrl = ref('')

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const usageModeOptions = ref<SelectOptionData[]>(dict.usageModeOptions)
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  {
    width: 240,
    tooltip: true,
    ellipsis: true,
    fixed: 'left',
    title: '案件编号',
    dataIndex: 'caseNo',
    slotName: 'caseNo'
  },
  { width: 120, align: 'center', title: '呼叫类型', dataIndex: 'usageMode' },
  { width: 160, tooltip: true, ellipsis: true, align: 'center', title: '呼叫结果', dataIndex: 'callResult' },
  { width: 100, align: 'center', title: '通话时长', dataIndex: 'timeLength', slotName: 'timeLength' },
  { width: 100, align: 'center', title: '响铃时长', dataIndex: 'ringLength', slotName: 'ringLength' },
  { width: 180, align: 'center', title: '主叫号码', dataIndex: 'callerPhone' },
  { width: 180, align: 'center', title: '被叫号码', dataIndex: 'calleePhone' },
  { width: 140, tooltip: true, ellipsis: true, align: 'center', title: '当事人', dataIndex: 'calleeName' },
  { width: 210, tooltip: true, ellipsis: true, title: '所属组织', dataIndex: 'orgName', align: 'center' },
  { width: 120, tooltip: true, ellipsis: true, title: '员工姓名', dataIndex: 'mediatorName', align: 'center' },
  { width: 180, tooltip: true, ellipsis: true, title: '呼叫时间', dataIndex: 'startTime', align: 'center' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const handleExcelDownload = async () => {
  let excelData = await exportCallRecord(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

const handleDownload = (filePath: string) => {
  if (filePath) {
    location.assign(download({ filePath }))
  } else {
    Message.warning('这条呼叫记录没有录音')
  }
}

// 播放录音
const handleAudioPlay = (filePath: string) => {
  if (filePath) {
    getCallRecordStream(filePath).then((res) => {
      if (res && res.data) {
        audioVisible.value = true
        nextTick(() => {
          let blob = new Blob([res.data])
          if (window.URL && audioRef.value) {
            audioUrl.value = window.URL.createObjectURL(blob)
          }
          audioRef.value?.play()
        })
      } else {
        Message.warning('这条记录没有录音')
      }
    })
  } else {
    Message.warning('这条记录没有录音')
  }
}

const handleStopPlay = () => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }
  return true
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.optDateStart = timeArr[0] || ''
    searchForm.value.optDateEnd = timeArr[1] || ''
  } else {
    searchForm.value.optDateStart = ''
    searchForm.value.optDateEnd = ''
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  recordIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getCallRecordList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
