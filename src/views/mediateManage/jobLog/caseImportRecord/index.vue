<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="importStatus" label="导入状态">
                  <a-select
                    v-model="searchForm.importStatus"
                    :options="importStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="impFlag" label="导入类型">
                  <a-select
                    v-model="searchForm.impFlag"
                    :options="impFlagOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                  <a-form-item field="mediateStatuses" label="调解状态">
                    <a-select
                      v-model="searchForm.mediateStatuses"
                      :options="mediateStatusOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-clear
                      multiple
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="entrustsIds" label="案源方">
                    <a-select
                      v-model="searchForm.entrustsIds"
                      :options="entrustsOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-clear
                      multiple
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="mediateResult" label="调解结果">
                    <a-select
                      v-model="searchForm.mediateResult"
                      :options="mediateResultOptions"
                      placeholder="请选择"
                      allow-clear
                    />
                  </a-form-item>
                </a-col> -->
              <!-- <a-col :span="8">
                  <a-form-item field="createTime" label="创建时间">
                    <a-range-picker
                      v-model="searchForm.createTime"
                      show-time
                      :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
                      class="percent-100"
                    ></a-range-picker>
                  </a-form-item>
                </a-col> -->
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon> <icon-search /> </template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon> <icon-refresh /> </template> 重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    
      <a-table
        v-model:selected-keys="recordIds"
        row-key="recordId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #importStatus="{ record }">
          <template v-for="item in importStatusOptions" :key="item.value">
            <span v-if="item.value==record.importStatus" :style="{color:item.color}"> {{ item.label }}</span>
          </template>
        </template>

        <template #caseStatusNumber="{ record }">
          <span> {{ record.totalCases }} /
            {{ record.importStatus ==4 ? 0 :record.successCases }}
            / {{ record.importStatus ==4 ? record.totalCases :record.failedCases }} </span>
        </template>
        <template #timeLength="{ record }">
          <span> {{ record.timeLength || '--' }}秒 </span>
        </template>
        <template #ringLength="{ record }">
          <span> {{ record.ringLength || '--' }}秒 </span>
        </template>
        <template #operations="{ record }">
          <a-button type="text" @click="handleDownload(record.caseImportId,0)">导入文件</a-button>
          <a-button type="text" :disabled="record.importStatus<=2" @click="handleDownload(record.caseImportId,1)">校验结果（全部）</a-button>
          <a-button type="text" :disabled="record.importStatus<=2" @click="handleDownload(record.caseImportId,2)">校验结果（仅失败）</a-button>
        </template>
      </a-table>
    </a-card>
    <a-modal
      v-model:visible="audioVisible"
      :footer="false"
      :width="400"
      :body-style="{ textAlign: 'center' }"
      :on-before-cancel="handleStopPlay"
      title="录音播放"
    >
      <audio ref="audioRef" :src="audioUrl" autoplay controls>您的浏览器不支持audio</audio>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { getCallRecordStream, exportCallRecord } from '@/api/arcoApi/mediateManage/jobLog/callRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import { pageListOfRecord,downloadCaseImportRecord } from '@/api/eleApi/case/importRecord'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { computed, ref, reactive, watch,onMounted,onUnmounted, nextTick } from 'vue'
import useLoading from '@/layouts/appArco/hooks/loading'

import { download } from '@/api/commonApi/file'
import { Modal,Message } from '@arco-design/web-vue'
import dict from '@/dict/mediateManage'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'
import { useIntervalFn } from '@vueuse/core/index'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    importStatus: null,
		impFlag:null
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('案件导入记录')
// const pageEnName = ref('Call')

const searchForm = ref<REQUEST_POST_CALL_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CALL_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const audioRef = ref<HTMLAudioElement>()
const audioVisible = ref(false)
const audioUrl = ref('')

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const impFlagOptions = [
	{label: "导入", value:1},
	{label: "更新", value:2}
]

const importStatusOptions=[
	{label: "正在导入", value:1,color:'black'},
	{label: "成功", value:2,color:'green'},
	{label: "部分成功", value:3,color:'#CFAF0F'},
	{label: "失败", value:4,color:'red'},
		{label: "系统异常", value:-1,color:'red'}

]

const findOptionLabelByValue= function(options,value){
	return options.find(item => item.value ==value)?.label;
}

const usageModeOptions = ref<SelectOptionData[]>(dict.usageModeOptions)
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 200, fixed: 'left', title: '导入时间', dataIndex: 'createTime' },
  { width: 200, align: 'center', title: '导入文件名', dataIndex: 'importFileName' },
  { width: 160, align: 'center', title: '导入类型', dataIndex: 'impFlag',render:({record})=>findOptionLabelByValue(impFlagOptions,record.impFlag) },
  { width: 100, align: 'center', title: '导入状态', dataIndex: 'importStatus',slotName: 'importStatus'},
  { width: 200, align: 'center', title: '总数/成功/失败', dataIndex: 'caseStatusNumber',slotName: 'caseStatusNumber' },
  { width: 180, align: 'center', title: '导入模板', dataIndex: 'tmplTitle' },
  { width: 180, align: 'center', title: '案源方', dataIndex: 'entrustsName' },
	{ width: 210, align: 'center', title: '部门', dataIndex: 'deptName' },
  { width: 140, align: 'center', title: '导入人', dataIndex: 'employeeName' },
  { width: 160, align: 'center', title: '下载', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const handleExcelDownload = async () => {
  let excelData = await exportCallRecord(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

const handleDownload = (caseImportId: number,exportType:number) => {

Modal.info({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `是否确认下载？`,
    onOk: () => {
			downloadCaseImportRecord(caseImportId,exportType).then((res) => {
				Message.success('下载成功')
				sysUtil.blobExport(res)
			})
    },
    onCancel: () => {}
  })

}

// 播放录音
const handleAudioPlay = (filePath: string) => {
  if (filePath) {
    getCallRecordStream(filePath).then((res) => {
      if (res && res.data) {
        audioVisible.value = true
        nextTick(() => {
          let blob = new Blob([res.data])
          if (window.URL && audioRef.value) {
            audioUrl.value = window.URL.createObjectURL(blob)
          }
          audioRef.value?.play()
        })
      } else {
        Message.warning('这条记录没有录音')
      }
    })
  } else {
    Message.warning('这条记录没有录音')
  }
}

const handleStopPlay = () => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }
  return true
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.optDateStart = timeArr[0] || ''
    searchForm.value.optDateEnd = timeArr[1] || ''
  } else {
    searchForm.value.optDateStart = ''
    searchForm.value.optDateEnd = ''
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  recordIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 },isLoad=true) => {
	if(isLoad){
		setLoading(true)
	}

  try {
    const data = await pageListOfRecord({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
		if(isLoad){

			setLoading(false)
		}
  }
}

getTableData()


useIntervalFn(()=>getTableData(pagination), 5000)




watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
