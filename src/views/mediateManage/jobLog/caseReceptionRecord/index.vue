<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="receiveType" label="接收方式">
                  <a-select
                    v-model="searchForm.receiveType"
                    :options="receiveTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="creatorName" label="创建人">
                  <a-input v-model="searchForm.creatorName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsId" label="案源方">
                  <a-select
                    v-model="searchForm.entrustsId"
                    :options="entrustsOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="createResult" label="创建结果">
                  <a-select
                    v-model="searchForm.createResult"
                    :options="createResultOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="acceptTime" label="接收时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="recordIds"
        row-key="collectionId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #filePath="{ record }">
          <span
            v-if="record.filePath"
            class="mdt-common-text mdt-common-text-active"
            @click="handleDownload(record.filePath)"
          >
            {{ record.filePath.split('__').pop() }}
          </span>
          <span v-else class="mdt-common-text mdt-common-text-visited">(暂无附件)</span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getCaseReceptionRecordList } from '@/api/arcoApi/mediateManage/jobLog/caseReceptionRecord'
import { exportCollectionReceiveLog } from '@/api/arcoApi/mediateManage/jobLog/caseReceptionRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'
import { download } from '@/api/commonApi/file'

import dict from '@/dict/mediateManage'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    acceptTimeStart: '',
    acceptTimeEnd: '',
    entrustsName: '',
    createResult: '',
    receiveType: '',
    creatorName: '',
    acceptTime: '',
    entrustsId: '',
    allTime: [],
    caseNo: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('案件接收记录')
// const pageEnName = ref('CaseReception')

const searchForm = ref<REQUEST_POST_CASERECEPTION_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CASERECEPTION_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const createResultOptions = ref<SelectOptionData[]>(dict.createResultOptions)
const receiveTypeOptions = ref<SelectOptionData[]>(dict.receiveTypeOptions)
const entrustsOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', dataIndex: 'caseNo', slotName: 'caseNo' },
  { width: 120, tooltip: true, ellipsis: true, title: '接收方式', dataIndex: 'collectionType' },
  { width: 120, tooltip: true, ellipsis: true, title: '案件创建结果', dataIndex: 'collectionResult' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建人', dataIndex: 'creatorName' },
  { width: 180, tooltip: true, ellipsis: true, title: '创建人所属组织', dataIndex: 'companyName' },
  { width: 180, tooltip: true, ellipsis: true, title: '所属案源方', dataIndex: 'entrustsName' },
  { width: 180, tooltip: true, ellipsis: true, title: '案件基本信息模板', dataIndex: 'tmplName' },
  { width: 180, tooltip: true, ellipsis: true, title: '相关附件', dataIndex: 'filePath', slotName: 'filePath' },
  { width: 180, tooltip: true, ellipsis: true, title: '接收时间', dataIndex: 'acceptTime', align: 'center' }
])

const handleExcelDownload = async () => {
  let excelData = await exportCollectionReceiveLog(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

const handleDownload = (filePath: string) => {
  location.assign(download({ filePath }))
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  recordIds.value = []
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.acceptTimeStart = timeArr[0] || ''
    searchForm.value.acceptTimeEnd = timeArr[1] || ''
  } else {
    searchForm.value.acceptTimeStart = ''
    searchForm.value.acceptTimeEnd = ''
  }
}


const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getCaseReceptionRecordList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

getAllEntrusts()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
