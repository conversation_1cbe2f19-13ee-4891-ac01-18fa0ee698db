<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="phoneNumber" label="接收号码">
                  <a-input v-model="searchForm.phoneNumber" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="contactName" label="收信人">
                  <a-input v-model="searchForm.contactName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="mediatorId" label="记录归属">
                  <Mdtcascader v-model:value="searchForm.mediatorId"></Mdtcascader>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="sendMessage" label="发送结果">
                  <a-select
                    v-model="searchForm.sendMessage"
                    :options="sendMessageOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="sendTimeBegin" label="发送时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="smsIds"
        row-key="smsId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #sendType="{ record }">
          <span> {{ sendTypeObj[record.sendType] || record.sendType }} </span>
        </template>
        <template #sendMessage="{ record }">
          <span> {{ sendMessageObj[record.sendMessage] || record.sendMessage }} </span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import { exportSmsLog, querySmsSendRecord } from '@/api/arcoApi/mediateManage/jobLog/smsSendRecord'
import Mdtcascader from '@/components/cascader/index.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'

import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    caseNo: '',
    companyId: '',
    mediatorId: '',
    contactName: '',
    phoneNumber: '',
    sendMessage: '',
    sendTimeBegin: '',
    sendTimeEnd: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('短信记录')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<REQUEST_POST_SMS_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_SMS_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const smsIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const sendMessageOptions = ref<SelectOptionData[]>(dict.sendMessageOptions)
const sendTypeOptions = ref<SelectOptionData[]>(dict.sendTypeOptions)
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  {
    width: 240,
    tooltip: true,
    ellipsis: true,
    fixed: 'left',
    title: '案件编号',
    dataIndex: 'caseNo',
    slotName: 'caseNo'
  },
  { width: 120, tooltip: true, ellipsis: true, title: '收信人', dataIndex: 'contactName' },
  { width: 140, tooltip: true, ellipsis: true, title: '接听电话', dataIndex: 'phoneNumber' },
  { width: 240, tooltip: true, ellipsis: true, title: '调解组织', dataIndex: 'companyName' },
  { width: 140, tooltip: true, ellipsis: true, title: '发送人', dataIndex: 'senderName' },
  { width: 120, title: '发送方式', dataIndex: 'sendType', slotName: 'sendType' },
  { width: 200, tooltip: true, ellipsis: true, align: 'center', title: '发送内容', dataIndex: 'sendContext' },
  { width: 140, title: '回执', dataIndex: 'receiveStatus', slotName: 'receiveStatus' },
  { width: 120, title: '发送状态', dataIndex: 'sendStatus', slotName: 'sendStatus' },
  { width: 120, tooltip: true, ellipsis: true, title: '发送结果', dataIndex: 'sendMessage', slotName: 'sendMessage' },
  { width: 180, title: '发送时间', dataIndex: 'sendTime', align: 'center' }
])

const sendTypeObj = computed(() => {
  return dictEnumValToObject(sendTypeOptions.value)
})

const sendMessageObj = computed(() => {
  return dictEnumValToObject(sendMessageOptions.value)
})

const handleExcelDownload = async () => {
  let excelData = await exportSmsLog(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  smsIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  smsIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.sendTimeEnd = timeArr[1] || ''
    searchForm.value.sendTimeBegin = timeArr[0] || ''
  } else {
    searchForm.value.sendTimeEnd = ''
    searchForm.value.sendTimeBegin = ''
  }
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await querySmsSendRecord({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
