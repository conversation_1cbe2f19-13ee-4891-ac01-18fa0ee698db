<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="caseNo" label="案件编号">
                  <a-input v-model="searchForm.caseNo" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="operatorName" label="操作人">
                  <a-input v-model="searchForm.operatorName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="distributeTargetName" label="分派对象">
                  <a-input v-model="searchForm.distributeTargetName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="entrustsId" label="案源方">
                  <a-select
                    v-model="searchForm.entrustsId"
                    :options="entrustsOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="startTime" label="操作时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24" class="mdt-col-flexend">
          <a-space>
            <a-button @click="handleExcelDownload">
              <template #icon><icon-download /> </template> 下载
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-model:selected-keys="recordIds"
        row-key="recordId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #caseNo="{ record }">
          <a-tag v-if="record.caseNo" class="mdt-common-tag" @click="handleToCaseDetails(record.caseId)">
            {{ record.caseNo }}
          </a-tag>
        </template>
        <template #toStatus="{ record }">
          <span>
            调解状态由【{{ $useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,record.fromStatus) || record.fromStatus }}】变为 【
            {{ $useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,record.toStatus) || record.toStatus }}】

          </span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getCaseAllocationRecordList } from '@/api/arcoApi/mediateManage/jobLog/caseAllocationRecord'
import { exportDistributeLog } from '@/api/arcoApi/mediateManage/jobLog/caseAllocationRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, ref, reactive, watch } from 'vue'
import { DictTypeEnum } from '@/dict/systemManage.ts'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    caseId: '',
    caseNo: '',
    distributeTargetName: '',
    entrustsId: '',
    entrustsName: '',
    fromStatus: '',
    operateTime: '',
    operateTimeEnd: '',
    operateTimeStart: '',
    operatorName: '',
    orgName: '',
    recordId: '',
    roleName: '',
    toStatus: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('案件分派记录')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<REQUEST_POST_CASEALLOCATION_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CASEALLOCATION_LIST_DATA_TYPE[]>([])
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const recordIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const entrustsOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 240, tooltip: true, ellipsis: true, fixed: 'left', title: '案件编号', dataIndex: 'caseNo', slotName: 'caseNo' },
  { width: 220, tooltip: true, ellipsis: true, title: '案件所属案源方', dataIndex: 'entrustsName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人', dataIndex: 'operatorName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人角色', dataIndex: 'roleName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人所属组织', dataIndex: 'orgName' },
  { width: 120, tooltip: true, ellipsis: true, title: '分派对象', dataIndex: 'distributeTargetName' },
  { width: 200, tooltip: true, ellipsis: true, title: '状态变更记录', dataIndex: 'toStatus', slotName: 'toStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '操作时间', dataIndex: 'operateTime', align: 'center' }
])


const handleExcelDownload = async () => {
  let excelData = await exportDistributeLog(searchForm.value)
  if (excelData) sysUtil.blobExport(excelData)
}

// 详情导航
const handleToCaseDetails = (caseId: string) => {
  if (caseId) {
    router.push({ name: `caseDetails`, params: { caseId } })
  }
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  recordIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  recordIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.operateTimeStart = timeArr[0] || ''
    searchForm.value.operateTimeEnd = timeArr[1] || ''
  } else {
    searchForm.value.operateTimeStart = ''
    searchForm.value.operateTimeEnd = ''
  }
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getCaseAllocationRecordList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

getAllEntrusts()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
