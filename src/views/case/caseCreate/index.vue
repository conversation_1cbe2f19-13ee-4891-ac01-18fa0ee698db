<template>
  <div class="case-create-page">
    <a-card :bordered="false" class="case-create-card">
      <template #title>
        <span class="bold">创建案件</span>
      </template>
      <div class="wrapper">
        <a-steps v-model:current="step" style="width: 750px" line-less class="steps">
          <a-step description="输入案件基本信息"> 选择基础信息 </a-step>
          <a-step description="输入详细的案件内容"> 选择详细信息 </a-step>
          <a-step description="创建成功"> 完成创建 </a-step>
        </a-steps>
        <keep-alive>
          <BaseInfo v-if="step === 1" ref="baseInfoRef" @comfirm="baseInfoComfirm" @cancel="baseInfoCancel" />
          <DetailsInfo
            v-else-if="step === 2"
            ref="detailsInfoRef"
            :loading="loading"
            @comfirm="detailsComfirm"
            @cancel="detailsCancel"
          />
          <Status v-else-if="step === 3" :status="status" @opreation="handleOpreation" />
        </keep-alive>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { singleImportCase } from '@/api/arcoApi/caseManage/caseCreate'
import DetailsInfo from './components/details-info.vue'
import BaseInfo from './components/base-info.vue'
import { Message } from '@arco-design/web-vue'
import Status from './components/status.vue'
import { useRouter } from 'vue-router'
import { nextTick, ref } from 'vue'

const router = useRouter()

const generateFormModel = () => {
  return {
    mdtCaseLitigantSaveBOList: [],
    caseSubjectMatter: null,
    caseNatureContent: '',
    mediateBeginTime: '',
    mediateEndTime: '',
    deputyTmplName: '',
    entrustsDeptId: '',
    completeReview: 1,
    deputyTmplId: '',
    entrustsName: '',
    entrustsId: '',
    tmplName: '',
    caseName: '',
    allTime: [],
    caseNo: ''
  }
}

const saveFormData = ref<REQUEST_CREATE_SAVE_PARAM_TYPE>(generateFormModel())

const status = ref<'info' | 'success' | 'warning' | 'error' | '403' | '404' | '500' | null>('info')
const detailsInfoRef = ref<InstanceType<typeof DetailsInfo>>()
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const caseId = ref<string | number>('')
const loading = ref(false)

const step = ref(1)
const baseInfoComfirm = (modal: REQUEST_CREATE_SAVE_BASEPARAM_TYPE) => {
  saveFormData.value = {
    ...saveFormData.value,
    ...modal
  }
  step.value = 2
}

const baseInfoCancel = () => {
  router.push({ name: `caseManage` })
}

const detailsComfirm = (list: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE[]) => {
  saveFormData.value.mdtCaseLitigantSaveBOList = list
  loading.value = true
  singleImportCase(saveFormData.value)
    .then((res) => {
      if (res.caseId) {
        Message.success('操作成功')
        status.value = 'success'
        caseId.value = res.caseId
        nextTick(() => detailsInfoRef.value?.initRenderData())
      }
    })
    .catch(() => {
      status.value = 'error'
    })
    .finally(() => {
      loading.value = false
      step.value = 3
    })
}

const detailsCancel = () => {
  step.value = 1
}

const handleOpreation = (val: string) => {
  switch (val) {
    case 'viewCase':
      router.push({ path: `caseDetails/${caseId.value}` })
      break
    case 'createCase':
      saveFormData.value = generateFormModel()
      caseId.value = ''
      step.value = 1
      nextTick(() => baseInfoRef.value?.resetModel())
      break
    case 'toCaseList':
      router.push({ name: `caseManage` })
      break
    case 'backUpdate':
      step.value = 2
      break
  }
}
</script>

<style scoped lang="scss">
.case-create-page {
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    // padding: 34px 0;
    background-color: var(--color-bg-2);
    :deep(.arco-form) {
      .arco-form-item {
        &:last-child {
          margin-top: 20px;
        }
      }
    }
  }

  .steps {
    margin-bottom: 45px;
  }
}
</style>
