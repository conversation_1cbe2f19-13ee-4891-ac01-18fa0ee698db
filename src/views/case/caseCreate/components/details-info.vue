<template>
  <div class="mdt-card-common">
    <div class="base-form-box mdt-form-content">
      <div class="mdt-form-header">
        <div class="mdt-form-split"></div>
        <span class="mdt-form-title">当事人信息</span>
      </div>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-button size="mini" style="margin-bottom: 10px" type="primary" @click="handleInfoAdd">
            <template #icon><icon-plus /> </template> 新增
          </a-button>
        </a-col>
        <a-col :span="24">
          <a-table
            :columns="cloneColumns"
            :data="renderData"
            :pagination="false"
            :bordered="false"
            :scroll="{ maxHeight: '500px'}"
          >
            <template #identityType="{ record }">
							{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.litigant_identity_type,record.identityType)}}
						</template>
            <template #idType="{ record }">
              {{ idTypeObj[record.idType] || record.idType }}
            </template>
            <template #litigantPhone="{ record }">
              <div v-if="record.litigantPhone" class="row-record-content">
                <span class="row-record-text">{{ dzPhone(record.litigantPhone) }}</span>
              </div>
            </template>
            <template #operations="{ rowIndex }">
              <a-button type="text" @click="handleDelete(rowIndex)"> 删除 </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
  </div>
  <a-space :size="20" style="margin-top: 10px">
    <a-button :loading="loading" @click="onCancelClick">返回</a-button>
    <a-button :loading="loading" type="primary" @click="onComfirmClick">下一步</a-button>
  </a-space>
  <PartyInfoModal
    ref="formInfoRef"
    v-model:visible="modalVisible"
    :title="modalTitle"
    @comfirm="handleComfirm"
    @cancel="handleCancel"
  />
</template>

<script lang="ts" setup>
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { Message, Modal } from '@arco-design/web-vue'
import { computed, nextTick, ref, watch } from 'vue'
import PartyInfoModal from './party-info-modal.vue'
import { dictEnumValToObject } from '@/utils'
import { dzPhone } from '@/utils/proto'
import dict from '@/dict/caseManage'
import _ from 'lodash'
import { DictTypeEnum } from '../../../../dict/systemManage.ts'

interface Props {
  loading?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false
})

type Column = TableColumnData & { checked?: true }

const emits = defineEmits(['comfirm', 'cancel'])

const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('当事人')

const renderData = ref<REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof PartyInfoModal>>()
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const modalVisible = ref(false)

const columns = computed<TableColumnData[]>(() => [
  {
    width: 100,
    tooltip: true,
    ellipsis: true,
    title: '身份类别',
    dataIndex: 'identityType',
    slotName: 'identityType'
  },
  { width: 100, tooltip: true, ellipsis: true, title: '名称', dataIndex: 'litigantName' },
  { width: 110, title: '当事人类型', dataIndex: 'litigantType', slotName: 'litigantType' },
  { width: 100, tooltip: true, ellipsis: true, title: '证件类型', dataIndex: 'idType', slotName: 'idType' },
  { width: 100, tooltip: true, ellipsis: true, title: '证件号码', dataIndex: 'idNo', align: 'center' },
  {
    width: 100,
    tooltip: true,
    ellipsis: true,
    title: '联系方式',
    align: 'center',
    dataIndex: 'litigantPhone',
    slotName: 'litigantPhone'
  },
  { width: 80, fixed: 'right', title: '操作', dataIndex: 'operations', align: 'center', slotName: 'operations' }
])

const modalTitle = computed(() => {
  return `${pageZhName.value}${modalType.value === 'add' ? '新增' : '信息'}`
})



const litigantTypeObj = computed(() => {
  return dictEnumValToObject(dict.litigantTypeOptions)
})

const idTypeObj = computed(() => {
  return dictEnumValToObject(dict.idCardTypeOptions)
})

const handleInfoAdd = () => {
  modalType.value = 'add'
  modalVisible.value = true
  nextTick(() => {
    formInfoRef.value?.initFormModel()
  })
}

const handleDelete = (index: number) => {
  Modal.warning({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `是否确认删除该${pageZhName.value}`,
    onOk: () => {
      renderData.value.splice(index, 1)
    },
    onCancel: () => {}
  })
}

const handleComfirm = (modalData: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  modalVisible.value = false
  const repairName = renderData.value.find((row) => row.litigantName === modalData.litigantName)
  if (repairName) {
    Message.warning('当事人名称已重复')
  } else {
    renderData.value.push(_.cloneDeep(modalData))
  }
}
const handleCancel = () => {
  modalVisible.value = false
}

const initRenderData = () => {
  renderData.value = []
}

const onComfirmClick = async () => {
  if (renderData.value.length === 0) {
    Message.warning('请添加当事人信息')
    return
  }
  emits('comfirm', renderData.value)
}

const onCancelClick = async () => {
  emits('cancel')
}

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)

defineExpose({ initRenderData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.mdt-card-common {
  width: 740px;
  padding: 20px;
  border: 1px solid #ccc;
}

.row-record-icon {
  cursor: pointer;
  color: rgb(var(--primary-6));
}
</style>
