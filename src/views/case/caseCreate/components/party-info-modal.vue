<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        :rules="baseInfoRules"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">当事人类型</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }" field="litigantType">
                <a-radio-group v-model="baseInfoForm.litigantType" @change="handleSelectLitigantType">
                  <a-radio v-for="item in litigantTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">当事人信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="identityType" label="身份类别">
								<DictDataSelect v-model="baseInfoForm.identityType" :dict-type="DictTypeEnum.litigant_identity_type" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="litigantName" label="姓名">
                <a-input v-model="baseInfoForm.litigantName" :max-length="50" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="idType" label="证件类型">
                <a-select v-model="baseInfoForm.idType" :options="idTypeOptions" placeholder="请选择" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="idNo" label="证件号码">
                <a-input v-model="baseInfoForm.idNo" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="litigantPhone" label="联系方式">
<!--                <a-input v-model="baseInfoForm.litigantPhone" :max-length="100" placeholder="请输入内容1" allow-clear />-->
                <a-input-tag v-model="litigantPhoneList" :unique-value="true" placeholder="按回车键输入" :allow-clear="true"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="sex" label="性别">
                <a-radio-group v-model="baseInfoForm.sex">
                  <a-radio v-for="item in sexOptions" :key="item.value" :value="item.value">{{ item.label }}</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '1'" :span="12">
              <a-form-item field="nation" label="民族">
                <a-input v-model="baseInfoForm.nation" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '1'" :span="12">
              <a-form-item field="birth" label="出生日期">
                <a-date-picker v-model="baseInfoForm.birth" style="width: 100%">
                  <template #suffix-icon><icon-schedule /></template>
                </a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="post" label="职业或职务">
                <a-input v-model="baseInfoForm.post" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>

            <a-col v-if="baseInfoForm.litigantType === '2'" :span="12"></a-col>
            <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
              <a-form-item field="companyName" label="企业名称">
                <a-input v-model="baseInfoForm.companyName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
              <a-form-item field="isPrivateEnterprise" label="是否民营企业">
                <a-radio-group v-model="baseInfoForm.isPrivateEnterprise">
                  <a-radio v-for="item in isPrivateEnterpriseOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '1'" :span="12">
              <a-form-item field="contactAddress" label="联系地址">
                <a-input v-model="baseInfoForm.contactAddress" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '1'" :span="12">
              <a-form-item field="idAddress" label="户籍地址">
                <a-input v-model="baseInfoForm.idAddress" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
              <a-form-item field="registerAddress" label="注册地址">
                <a-input v-model="baseInfoForm.registerAddress" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>

            <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
              <a-form-item field="businessAddress" label="经营地址">
                <a-input v-model="baseInfoForm.businessAddress" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item
                :label-col-props="{ span: 4 }"
                :wrapper-col-props="{ span: 20 }"
                label-align="right"
                field="litigantDesc"
                label="备注"
              >
                <a-textarea
                  v-model="baseInfoForm.litigantDesc"
                  :auto-size="{ minRows: 4 }"
                  :max-length="250"
                  style="margin-left: 8px"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { RadioOption } from '@arco-design/web-vue/es/radio/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { phoneAndTelReg } from '@/assets/ts/regexp'
import dict from '@/dict/caseManage'
import { computed, ref } from 'vue'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'

interface Props {
  width?: string | number
  title: string
}

withDefaults(defineProps<Props>(), {
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm', modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    isPrivateEnterprise: '1',
    businessAddress: '',
    contactAddress: '',
    registerAddress: '',
    litigantName: '',
    litigantPhone: '',
    litigantType: '1',
    litigantDesc: '',
    identityType: '',
    phoneStatus: '',
    companyName: '',
    litigantId: '',
    idAddress: '',
    caseId: '',
    idType: '',
    nation: '',
    birth: '',
    post: '',
    idNo: '',
    sex: '1'
  }
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE>(generateFormModel())
const baseInfoRules = ref({
  litigantPhone: [{validator: (value, cb) => {
      return new Promise(resolve => {
        if(value){
          let split = value.split(',')
          if(split){
            let msg = "";
            for(let i of split){
              let b = phoneAndTelReg.test(i);
              if(!b){
                if(msg === ""){
                  msg += i;
                }else{
                  msg += ","+i;
                }
              }
            }
            if(msg != ""){
              cb(`${msg} 手机号格式不正确`);
            }
          }
        }
        resolve();
      })
    }}],
  identityType: [{ required: true, message: '请选择身份类别' }],
  companyName: [{ required: true, message: '请输入企业名称' }],
  litigantName: [{ required: true, message: '请输入姓名' }],
  litigantType: [{ required: true, message: '请选择' }]
})

const isPrivateEnterpriseOptions = ref<RadioOption[]>(dict.isPrivateEnterpriseOptions)
const litigantTypeOptions = ref<RadioOption[]>(dict.litigantTypeOptions)
const idTypeOptions = ref<SelectOptionData[]>(dict.idCardTypeOptions)
const sexOptions = ref<RadioOption[]>(dict.sexOptions)
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    emits('comfirm', baseInfoForm.value)
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

const handleSelectLitigantType = (value: string | number | boolean) => {
  if (value === '1') {
    baseFormRef.value?.resetFields(['companyName', 'isPrivateEnterprise', 'registerAddress', 'businessAddress'])
  } else if (value === '2') {
    baseFormRef.value?.resetFields(['nation', 'birth', 'contactAddress', 'idAddress'])
  }
}
let litigantPhoneList = computed({
  // 读取
  get(){
    if(baseInfoForm.value.litigantPhone){
      return baseInfoForm.value.litigantPhone.replace(/\s/g, '').split(',');
    }
    return []
  },
  // 修改
  set(val){
    let phoneList = "";
    for(let i=0;i<val.length;i++){
      if(i<val.length-1){
        phoneList = phoneList.concat(val[i]).concat(",");
      }else{
        phoneList = phoneList.concat(val[i]);
      }
    }
    baseInfoForm.value.litigantPhone = phoneList;
  }
})
defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
