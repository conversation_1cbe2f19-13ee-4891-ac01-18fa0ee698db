<template>
  <a-spin>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      class="form"
    >
      <a-form-item field="caseNo" label="案件编号" :max-length="50">
        <a-input v-model="formData.caseNo" placeholder="请输入" allow-clear />
      </a-form-item>
      <a-form-item field="entrustsId" label="案源方">
        <a-select
          v-model="formData.entrustsId"
          :options="entrustsOptions"
          placeholder="请选择"
          allow-clear
          @change="handleEntrustsChange"
        />
      </a-form-item>
      <a-form-item v-if="formData.entrustsId" field="entrustsDeptId" label="所属部门">
        <a-select v-model="formData.entrustsDeptId" :options="entrustsDeptOptions" placeholder="请选择" allow-clear />
      </a-form-item>
      <a-form-item field="allTime" label="调解时限">
        <a-range-picker v-model="formData.allTime" style="width: 100%" @change="handlePickerSelectForUpdate">
          <template #suffix-icon><icon-schedule /></template>
        </a-range-picker>
      </a-form-item>
      <a-form-item field="completeReview" label="办结审核">
        <a-switch
          v-model="formData.completeReview"
          :checked-value="1"
          :unchecked-value="0"
          :default-checked="formData.completeReview === 1 ? true : false"
        ></a-switch>
      </a-form-item>
      <a-form-item field="deputyTmplId" label="案件模板">
        <a-select v-model="formData.deputyTmplId" :options="tmplOptions" placeholder="请选择" allow-clear />
      </a-form-item>
      <a-form-item field="caseNatureContent" label="案由">
        <dict-data-select v-model="formData.caseNatureContent" :dict-type="DictTypeEnum.NATURE"  :model-value-witch="'id'" />
      </a-form-item>
      <a-form-item field="caseSubjectMatter" label="案件标的">
        <a-input-number v-model="formData.caseSubjectMatter" :max-length="50" placeholder="请输入" allow-clear />
      </a-form-item>
      <a-form-item field="businessType" label="业务类型">
        <a-select
          v-model="formData.businessType"
          :options="businessTypeOptions"
          allow-create
          allow-clear
        />
      </a-form-item>
      <a-form-item class="base-form-justify">
        <a-space :size="20">
          <a-button @click="onCancelClick">取消</a-button>
          <a-button type="primary" @click="onComfirmClick">下一步</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { getDeptInfoById } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import { deputyTmplList } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { getCaseNatures, getNaturesDictId } from '@/api/eleApi/common'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { SelectInstance } from '@arco-design/web-vue/es/select'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { addVal } from '@/api/arcoApi/systemManage/dictManage'
import { Modal, Message } from '@arco-design/web-vue'
import { ref } from 'vue'
import dict from '@/dict/caseManage.ts'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
import { DictTypeEnum } from '@/dict/systemManage.ts'

const emits = defineEmits(['comfirm', 'cancel'])

const formData = ref<REQUEST_CREATE_SAVE_BASEPARAM_TYPE>({
  caseSubjectMatter: null,
  caseNatureContent: '',
  mediateBeginTime: '',
  mediateEndTime: '',
  deputyTmplName: '',
  entrustsDeptId: '',
  completeReview: 1,
  deputyTmplId: '',
  entrustsName: '',
  entrustsId: '',
  tmplName: '',
  caseName: '',
  allTime: [],
  caseNo: '',
	businessType:''
})

const formRules = ref({
  caseNo: [{ required: true, message: '请输入案件编号' }],
  entrustsDeptId: [{ required: true, message: '请选择所属部门' }],
  caseSubjectMatter: [{ required: true, message: '请输入标的' }],
  caseNatureContent: [{ required: true, message: '请选择案由' }],
  entrustsId: [{ required: true, message: '请选择案源方' }],
  deputyTmplId: [{ required: true, message: '请选择案件模板' }],
  caseName: [{ required: true, message: '请输入' }],
	businessType: [{ required: true, message: '请选择' }]
})
const businessTypeOptions = ref<SelectOptionData[]>(dict.businessType)

const entrustsDeptOptions = ref<SelectOptionData[]>([])
const natureTitleOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const tmplOptions = ref<SelectOptionData[]>([])
const formRef = ref<FormInstance>()
const naturesDictId = ref('')

const resetModel = () => {
  formRef.value?.resetFields()
}

const setModel = (model: REQUEST_CREATE_SAVE_BASEPARAM_TYPE) => {
  if (model) {
    formData.value = model
    getDeputyTmplList()
  }
}

const handleEntrustsChange = () => {
  formRef.value?.resetFields(['deputyTmplId', 'entrustsDeptId'])
  tmplOptions.value = []
  if (formData.value.entrustsId) {
    getDeputyTmplList()
    getEntrustsDeptList(formData.value.entrustsId)
  }
}

// const handleNatureChange: SelectInstance['onChange'] = (val) => {
//   const hasNature = natureTitleOptions.value.find((option) => option.value === val)
//   if (!hasNature) {
//     Modal.info({
//       title: '提示',
//       hideCancel: false,
//       bodyStyle: { padding: '0 30px' },
//       content: '该案由未存在下拉项，是否进行新增操作？',
//       onOk: async () => {
//         let param = { dictId: naturesDictId.value, enableFlag: true, dictTag: val.toString().trim() }
//         await addVal(param)
//         Message.success('案由新增成功')
//       },
//       onCancel: () => {
//         formData.value.caseNatureContent = ''
//       }
//     })
//   }
// }

const handlePickerSelectForUpdate = (times: any) => {
  if (times && times.length) {
    formData.value.mediateBeginTime = times[0] || ''
    formData.value.mediateEndTime = times[1] || ''
  } else {
    formData.value.mediateBeginTime = ''
    formData.value.mediateEndTime = ''
  }
}

const onComfirmClick = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    emits('comfirm', formData.value)
  }
}

const onCancelClick = async () => {
  const res = await formRef.value?.resetFields()
  if (!res) {
    emits('cancel')
  }
}

const getDeputyTmplList = () => {
  let params = { tmplStatus: 1, tmplType: '2', entrustsId: formData.value.entrustsId }
  deputyTmplList(params).then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.deputyTmplId }))
    }
  })
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

const getEntrustsDeptList = (id: string | number) => {
  getDeptInfoById(id).then((res) => {
    if (res && res.length) {
      entrustsDeptOptions.value = res.map((item) => ({ label: item.deptName, value: item.deptId }))
    }
  })
}

const getCaseNaturesData = () => {
  getCaseNatures().then((res) => {
    if (res && res.length) {
      natureTitleOptions.value = res.map((item) => ({ label: item.dictTag, value: item.dictTag }))
    }
  })
}

const findNaturesDictId = async () => {
  naturesDictId.value = await getNaturesDictId()
}

getCaseNaturesData()
findNaturesDictId()
getAllEntrusts()

defineExpose({ resetModel, setModel })
</script>

<style scoped lang="scss">
.form {
  width: 500px;
}

.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}
</style>
