import { Ref,ref,ComputedRef,computed } from 'vue'
import { ApprovalStatusEnum } from '@/dict/caseAuditManage.ts'

// 案件上下文类型定义
export interface CaseContext {
  // 案件ID
  caseId: string;

  // 案件基本信息
  caseBasic: Ref<REQUEST_CASEBASIS_DATA_TYPE>;

  // 当事人列表
	litigantList:Ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>;

  // 可以根据需要添加更多的上下文数据
  // 例如：调解记录、任务列表等

	//是否可编辑，定义一个计算属性
	isEditable: ComputedRef<boolean>
}


export const createDefaultCaseContext = (): CaseContext => {
	// 创建默认的空上下文
	let caseBasic = ref<REQUEST_CASEBASIS_DATA_TYPE>({
	caseId: 0,
	caseNo: '',
	entrustsName: '',
	entrustsDeptId: 0,
	entrustsDeptName: '',
	creatorName: '',
	mdtCaseStatus: '',
	mediateStatus: '',
	mediateResult: '',
	orgName: '',
	deptName: '',
	currentMediatorName: '',
	remainingDays: 0,
	createTime: '',
	LocalDateTime: '',
	closeTime: '',
	caseName: '',
	mediateBeginTime: '',
	mediateDoneTime: '',
	mediateCloseTime: '',
	caseApplyTime: '',
	caseNatureContent: '',
	expirationTime: '',
	defendAntLitigantNames: '',
	plainTiffLitigantNames: '',
	otherDemand: '',
	successReason: '',
	closeReason: '',
	suspendReason: ''
})

	return {
  caseId: '',
  caseBasic: caseBasic,
  litigantList:ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>([]),
	isEditable: computed(() => {
		if (caseBasic.value.mdtCaseStatus != '40'){
			console.log('1')
			return true
		}
		//只要有案管办结就永远可以编辑
		if (ApprovalStatusEnum.CASE_MANAGE_CLOSE.isIn(caseBasic.value.businessTypeObj?.processTypeList)){
			console.log('2')
			return true
		}
			console.log('3')
		return false;
	}
	)
}}

// 创建上下文的 Symbol key，避免命名冲突
export const CaseContextKey = Symbol('CaseContext')
