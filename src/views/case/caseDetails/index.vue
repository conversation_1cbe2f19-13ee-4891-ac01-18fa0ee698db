<template>
  <a-layout class="case-details-page">
    <a-layout-content style="display: flex; flex-direction: column; row-gap: 10px; margin: 10px 0 0 0">
      <a-row :gutter="10">
        <a-col :span="18" class="case-detail-item">
          <a-space direction="vertical" fill>
            <div style="height: 210px">
              <BasicInfo ref="basicInfoRef" :case-id="caseId" />
            </div>
            <a-row :gutter="10">
              <a-col :xs="16" :xxl="17" class="case-detail-item">
                <CustomModuleInfo ref="moduleInfoRef" :case-id="caseId" :list-height="caseInfoTabHeightSafe - 230" />
              </a-col>
              <a-col :xs="8" :xxl="7" class="case-detail-item">
                <LitigantInfo
                  ref="litigantInfoRef"
                  :case-id="caseId"
                  :list-height="caseInfoTabHeightSafe - 277"
                  @add-callrepaire-record="handleAddCallrepaire"
                />
              </a-col>
            </a-row>
          </a-space>
        </a-col>
        <a-col :span="6">
          <div style="height: 300px">
            <TaskListTable :case-id="caseId" :list-height="280" />
          </div>
          <MediateRecordTable
            ref="mediateRecordTableRef"
            :case-id="caseId"
            :list-height="caseInfoTabHeightSafe - 360"
            @mediate-record-save="handleMediateRecordSave"
          />
        </a-col>
      </a-row>
    </a-layout-content>

    <a-layout-sider ref="CaseInfoTabRef" style="width: 80px; margin-left: 5px">
      <CaseInfoTab :case-id="caseId" @call-repair="handleLitigantVirtualNo" />
    </a-layout-sider>
  </a-layout>
</template>

<script lang="ts" setup>
import LitigantInfo from './components/litigant-info.vue'
import CaseInfoTab from './components/case-info-tab.vue'
import BasicInfo from './components/basic-info.vue'
import { useRoute } from 'vue-router'
import { computed, provide, ref, watch } from 'vue'
import TaskListTable from '@/views/case/caseDetails/components/task-list-table.vue'
import MediateRecordTable from '@/views/case/caseDetails/components/mediate-record-table.vue'
import CustomModuleInfo from '@/views/case/caseDetails/components/custom-module-info.vue'
import { useElementSize } from '@vueuse/core'
import { useAppStore } from '@/layouts/appArco/store/index.ts'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'

// 进入详情页自动收缩菜单
const appStore = useAppStore()
appStore.updateSettings({ menuCollapse: true })

// 计算拥有独立下拉条容器的高度
const CaseInfoTabRef = ref(null)
const caseInfoTabSize = useElementSize(CaseInfoTabRef)
//防止容器太高
const caseInfoTabHeightSafe = computed(() => {
  return Math.min(1500, caseInfoTabSize.height.value)
})

const route = useRoute()

const litigantInfoRef = ref<InstanceType<typeof LitigantInfo>>()
const moduleInfoRef = ref<InstanceType<typeof CustomModuleInfo>>()
const mediateRecordTableRef = ref<InstanceType<typeof MediateRecordTable>>()
const basicInfoRef = ref<InstanceType<typeof BasicInfo>>()
const caseId = ref<string>(route.params.caseId as string)

//案件基本信息被很多深层组件使用，因此提供给子组件,该信息会在BasicInfo组件中获取

provide('caseId', caseId.value)

// 创建统一的案件上下文，案件所有组件都共享案件的一些信息
// 创建统一的案件上下文
const caseContext: CaseContext = {
	//默认的空属性
	...createDefaultCaseContext(),
	caseId: caseId.value
}

// 使用一个 provide 提供所有上下文
provide(CaseContextKey, caseContext)

watch(()=>caseContext.isEditable,()=>{
	console.log(caseContext.caseBasic.value)
	console.log(caseContext.isEditable.value)
},{immediate: true})

watch(()=>caseContext.caseBasic,()=>{
	console.log(caseContext.caseBasic.value)
},{deep:true})

const handleMediateRecordSave = () => {
  basicInfoRef.value?.queryCaseCurrentData()
}

// 当事人虚拟号渲染处理
const handleLitigantVirtualNo = (litigantId: string, timeStamp: number) => {
  litigantInfoRef.value?.setLitigantCall(litigantId, timeStamp)
  litigantInfoRef.value?.getTableData()
}

const handleAddCallrepaire = (item: REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE, canClose = true) => {
  mediateRecordTableRef.value?.handleEdit(item, canClose)
}
</script>

<style lang="scss">
:deep(.arco-card-header) {
  border-bottom: none;
}
.case-detail-item {
  height: 100%;
}

.case-details-page {
  background-color: var(--color-fill-2) !important;
  height: 100% !important;
  margin-top: 0px !important;
  padding-bottom: 5px !important;
  margin-right: -20px;
}

.description-content {
  width: 100%;
  max-height: 250px;
  overflow-y: scroll;
  padding: 10px;
  background-color: rgb(var(--gray-1));

  .description-text-red {
    color: #bd3124;
  }
  .description-text-gray {
    color: rgb(184, 179, 179);
  }
  .description-text-label {
    max-width: 200px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: right;
    vertical-align: text-top;
  }

  .description-text-value {
    text-align: left;
    vertical-align: text-top;
  }
}
</style>
