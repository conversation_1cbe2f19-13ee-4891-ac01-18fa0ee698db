<template>
  <a-card title="调解记录" class="cardContent case-detail-item">
    <template #extra>
      <a-button v-if="!caseComplated" v-auth="['caseRecordSave']" type="primary" @click="handleShowModal">添加</a-button>
    </template>
    <div :style="{ height: listHeight + 'px' }">
      <a-list :split="false" :bordered="false" :scrollbar="true" :max-height="listHeight">
        <a-list-item v-for="item in renderData" :key="item.recordId" style="padding-top: 5px; padding-right: 15px">
          <a-card>
            <template #title>
							<a-row align="center">
              <span style="font-size: 14px; margin-right: 10px">
								{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,item.mediateStatus)}}
							</span>

              <ellipsis-text style="font-weight: bold" :text="item.contactorUser" :max-length="8" />

							<a-tag style="margin-left: 5px" v-if="item.identityType"  color="gray">
								{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.litigant_identity_type,item.identityType)}}
							</a-tag>
							</a-row>
            </template>
            <template #extra>
              <a-space :size="0">
                <a-tooltip content="编辑">
                  <a-button type="text" @click="handleEdit(item)">
                    <template #icon>
                      <icon-edit />
                    </template>
                  </a-button>
                </a-tooltip>
                <a-button v-if="userName === item.mediatorName" type="text" @click="handleDelete(item)">
                  <template #icon>
                    <icon-delete />
                  </template>
                </a-button>
              </a-space>
            </template>
            <a-descriptions :column="1">
              <a-descriptions-item label="原/被告其他诉求:">
                <ellipsis-text :text="item.otherDemand" :max-length="25" />
              </a-descriptions-item>
              <a-descriptions-item label="调解时间:">
                {{ timeToDateStr(item.mediateTime) }}
              </a-descriptions-item>
              <a-descriptions-item label="下次跟进时间:">
                {{ timeToDateStr(item.nextDoTime) }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-list-item>
      </a-list>
    </div>
  </a-card>
  <MediateRecordModal
    ref="formInfoRef"
    v-model:visible="modalVisible"
    :case-complated="caseComplated"
    :case-id="caseId"
    :can-close="modalCanClose"
    title="调解记录登记"
    @confirm="handleConfirm"
  />
</template>

<script lang="ts" setup>
import { byCaseIdGetMediateRecord, caseDetailsDeleteRecord } from '@/api/arcoApi/caseManage/caseDetails.ts'
import MediateRecordModal from './modal/mediate-record-modal.vue'
import EllipsisText from '@/components/ellipsis-text/index.vue'
import { Modal, Message } from '@arco-design/web-vue'
import { timeToDateStr } from '@/utils/dateUtil'
import useLoading from '@arco/hooks/loading.ts'
import { computed, nextTick, ref } from 'vue'
import { dictEnumValToObject } from '@/utils'
import { useUserStore } from '@arco/store'
import dict from '@/dict/caseManage.ts'
import bus from '@/utils/eventBus'
import _ from 'lodash'
import { DictTypeEnum } from '../../../../dict/systemManage.ts'

const { caseId, listHeight } = defineProps({ caseId: { type: String, default: '' }, listHeight: { type: Number } })
const emits = defineEmits<{ (e: 'mediateRecordSave'): void }>()

const { setLoading } = useLoading(true)

const modalType = ref<'add' | 'edit'>('add')
const userStore = useUserStore()
const pageZhName = ref('调解记录')

const renderData = ref<REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof MediateRecordModal>>()
const userName = ref(userStore.userInfo.loginName)
const modalCanClose = ref(true)
const modalVisible = ref(false)

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))


const handleDelete = (row: REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE) => {
  if (row.recordId) {
    Modal.warning({
      title: '提示',
      hideCancel: false,
      alignCenter: true,
      content: `是否确认删除该条${pageZhName.value}`,
      onOk: () => {
        row.recordId &&
          caseDetailsDeleteRecord(row.recordId)
            .then(() => {
              Message.success('操作成功')
              getTableData()
            })
            .catch(() => {})
      },
      onCancel: () => {}
    })
  }
}

const handleShowModal = () => {
  modalCanClose.value = true
  modalVisible.value = true
}

const handleEdit = async (row: REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE, canClose = true) => {
  if (row.recordId) {
    modalType.value = 'edit'
  }
  modalVisible.value = true
  nextTick(() => {
    // 如果没有调解状态就取最后一次调解
    if (!row.mediateStatus && renderData.value.length > 0) {
      row.mediateStatus = renderData.value[renderData.value.length - 1].mediateStatus
    }

    formInfoRef.value?.setFormModel(row as REQUEST_CASEDETAILS_MEDIATE_SAVE_PARAM_TYPE)
  })
  modalCanClose.value = canClose
}

const handleConfirm = () => {
  modalVisible.value = false
  getTableData()

  emits('mediateRecordSave')
}

const getTableData = async () => {
  setLoading(true)
  try {
    renderData.value = await byCaseIdGetMediateRecord(caseId)
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

getTableData()

defineExpose({ getTableData, handleShowModal, handleEdit })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

:deep(.arco-card-header) {
  border-bottom: none;
}

:deep(.arco-card-body) {
  padding-top: 0;
}

.cardContent > :deep(.arco-card-body) {
  padding: 0;
}

:deep(.arco-list-item) {
  padding-left: 10px !important;
  padding-right: 20px !important;
}
</style>
