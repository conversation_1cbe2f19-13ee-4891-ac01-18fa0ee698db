<template>
  <a-card :bordered="false" :title="lisPageTitle" class="case-detail-item cardContent">
    <template #extra>
      <a-space v-if="!caseComplated" :size="5">
        <a-button type="primary" @click="getTableData"> 刷新</a-button>
        <a-button v-auth="['caseLitiganteSave']" type="primary" @click="handleInfoAdd"> 添加</a-button>
      </a-space>
    </template>

    <div :style="{ height: listHeight + 'px' }">
      <a-list :split="false" :bordered="false" :scrollbar="true" :max-height="listHeight">
        <a-list-item v-for="item in renderData" :key="item.litigantId" style="padding-top: 5px; padding-right: 15px">
          <litigant-info-item
            :item="item" :case-id="caseId" :litigant-call="litigantCall" @get-table-data="getTableData"
            @add-callrepaire-record="emits('addCallrepaireRecord',$event)"
            @handle-view="handleView(item)"
          />
        </a-list-item>
      </a-list>
    </div>
    <PartyInfoModal
      ref="formInfoRef"
      v-model:visible="modalVisible"
      :case-id="caseId"
      :title="modalTitle"
      @comfirm="handleComfirm"
    />
  </a-card>
</template>


<script lang="ts" setup>
import PartyInfoModal from './modal/litigant-info-modal.vue'

import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import bus from '@/utils/eventBus'

import LitigantInfoItem from './litigant-info-item.vue'
import { caseDetailsQueryLit } from '@/api/arcoApi/caseManage/caseDetails.ts'

import { inject } from 'vue'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
// 使用统一的上下文
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())

const { caseId, listHeight } = defineProps({ caseId: { type: String, default: '' }, listHeight: { type: Number } })

const emits = defineEmits<{
	addCallrepaireRecord: [item: REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE, canClose?: boolean]
}>()

const { setLoading } = useLoading(true)

const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('当事人')

const renderData = ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof PartyInfoModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}信息`)
const loadingTask = ref<NodeJS.Timeout>()
const litigantCall = ref(new Object())
const modalVisible = ref(false)

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))

const modalTitle = computed(() => {
	return `${pageZhName.value}${modalType.value === 'add' ? '新增' : '信息'}`
})


const handleInfoAdd = () => {
	modalType.value = 'add'
	modalVisible.value = true
}


const handleView = async (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE) => {
	if (row.litigantId) {
		modalType.value = 'edit'
		modalVisible.value = true
		nextTick(() => {
			formInfoRef.value?.setFormModel(row)
		})
	}
}


const handleComfirm = () => {
	modalVisible.value = false
	getTableData()
}

const getTableData = async () => {
	setLoading(true)
	try {
		const res = await caseDetailsQueryLit(caseId)
		renderData.value = res
		// 更新共享的当事人列表
		caseContext.litigantList.value = res
	} catch (err) {
	} finally {
		setLoading(false)
	}
}

const setLitigantCall = (field: string, timeStamp: number) => {
	if (field && timeStamp) {
		litigantCall.value[field] = timeStamp
	} else if (litigantCall.value[field]) {
		delete litigantCall.value[field]
	}
}


onMounted(() => {
	caseId && getTableData()
})

onUnmounted(() => {
	clearTimeout(loadingTask.value)
})


defineExpose({ handleInfoAdd, getTableData, setLitigantCall })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
	&:last-child {
		.arco-table-th-item-title {
			margin-left: 16px;
		}
	}
}

:deep(.callrepair-task-description) {
	.arco-skeleton-line-row {
		border-radius: 4px;
	}
}

:deep(.arco-card-header) {
	border-bottom: none;
}

:deep(.arco-card-body) {
	padding-top: 0 !important;
}

.cardContent > :deep(.arco-card-body) {
	padding: 0;
}

:deep(.arco-list-item) {
	padding-left: 10px !important;
	padding-right: 20px !important;
}
</style>
