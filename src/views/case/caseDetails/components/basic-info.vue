<template>
  <a-card class="case-detail-item" :header-style="{ marginTop: '10px' }">
    <template #title>
      <span style="font-weight: bold; font-size: 20px">{{ basicForm.caseNo }}</span>

      <a-button type="text" @click="caseReviewVisible = true">
        <template #icon>
          <IconFont type="icon-ai" :size="14" />
        </template>
        <template #default>案情梳理</template>
      </a-button>
      <a-button type="text" @click="mediateOutlineVisible = true">
        <template #icon>
          <IconFont type="icon-ai" :size="14" />
        </template>
        <template #default>调查提纲</template>
      </a-button>
    </template>
    <template #extra>
      <a-space direction="vertical" style="color: black; text-align: right">
        <span>案源方：{{ basicForm.entrustsName }}</span>
        <span>案由：{{ basicForm.caseNatureContent }}</span>
      </a-space>
    </template>
    <a-space style="font-weight: bold">
      <template #split>|</template>
      <template v-for="item in basicData" :key="item.label">
        <a-tooltip v-if="item" :disabled="item.value && item.value.length < 5" :content="item.value">
          <a-button type="text" long>
            <span class="tooltip-text">{{ item.label }}：{{ item.value }}</span>
          </a-button>
        </a-tooltip>
      </template>

      <a-tooltip :content="'调解截止时间：' + timeToDateStr(basicForm.expirationTime)">
        <span>
          调解截止：<span style="color: rgb(var(--danger-5)); margin-top: 5px">
            剩余{{ basicForm.remainingDays ? basicForm.remainingDays + '天' : '' }}
          </span>
        </span>
      </a-tooltip>
      <a-tooltip :content="'标的额：' + (basicForm.caseSubjectMatter==null?'未填写':basicForm.caseSubjectMatter)">
        <span>
          标的额 :
          <a-input-number v-model="basicForm.caseSubjectMatter" :precision="2" @blur="handleBlur" @focus="handleFocus" :style="{width:'320px'}" placeholder="填写" style="width: 80px;background-color: white;padding: 3px;" :hide-button="true"/>
        </span>
      </a-tooltip>
    </a-space>
    <a-row style="padding-left: 10px" justify="space-between" :wrap="false">
      <a-col :span="5" :xxl="{ span: 15 }" style="margin-top: 20px;overflow: auto;">
				<a-scrollbar ref="scrollbarRef" style="overflow: auto;padding-bottom: 15px">
        <a-steps :current="currentStepNumber" label-placement="vertical">
          <a-step :description="timeToDateStr(basicForm.caseApplyTime)">
            <template #icon>&nbsp;</template>
            案件申请
          </a-step>
          <a-step v-if="hasThisProcess(ApprovalStatusEnum.MEDIATE_CLOSE.code)" :description="timeToDateStr(basicForm.mediateBeginTime)">
            <template #icon>&nbsp;</template>
            调解开始
          </a-step>
          <a-step v-if="hasThisProcess(ApprovalStatusEnum.MEDIATE_CLOSE.code)" :description="timeToDateStr(basicForm.mediateDoneTime)">
            <template #icon>&nbsp;</template>
            调解完成
          </a-step>
          <a-step v-if="basicForm.closeTime" :description="timeToDateStr(basicForm.closeTime)">
            <template #icon>&nbsp;</template>
						调解办结
          </a-step>
					<a-step v-if="basicForm.caseManageCloseTime" :description="timeToDateStr(basicForm.caseManageCloseTime)">
						<template #icon>&nbsp;</template>
						案管办结
					</a-step>
          <a-step v-if="caseComplated" :description="timeToDateStr(basicForm.mediateCloseTime)">
            <template #icon>&nbsp;</template>
            案件关闭
          </a-step>
          <a-step v-if="!caseComplated && !basicForm.closeTime && hasThisProcess(ApprovalStatusEnum.MEDIATE_CLOSE.code)" v-auth="['caseApprovalSave']">
            <template #icon>&nbsp;</template>
            <a-button type="primary" style="margin-top: 10px" @click="approvalModalVisible = true">调解办结</a-button>
          </a-step>
					<a-step v-if="!caseComplated && !basicForm.caseManageCloseTime && hasThisProcess(ApprovalStatusEnum.CASE_MANAGE_CLOSE.code)
					&& ( basicForm.closeTime
					|| !hasThisProcess(ApprovalStatusEnum.MEDIATE_CLOSE.code)
					)" v-auth="['caseApprovalSave']">
            <template #icon>&nbsp;</template>
            <a-button type="primary" style="margin-top: 10px" @click="caseManageapprovalModalVisible = true">案管办结</a-button>
          </a-step>
        </a-steps>
				</a-scrollbar>
      </a-col>

      <a-col :span="5">
        <a-card style="max-width: 200px">
          <a-space direction="vertical" style="padding-top: 10px">
            <span>{{ basicForm.currentMediatorName || '暂未分配调解员' }}</span>
            <span>{{ basicForm.deptName || '暂未分配调解团队' }}</span>
            <span>{{ basicForm.orgName || '暂未分配调解组织' }}</span>
          </a-space>
        </a-card>
      </a-col>
    </a-row>
  </a-card>
  <StartApprovalModal
    v-model:visible="approvalModalVisible"
    :width="600"
    :case-id="caseId"
    title="申请办理"
		@confirm="queryCaseCurrentData"
		:filter-approval-types="[ApprovalStatusEnum.MEDIATE_CLOSE.code, ApprovalStatusEnum.DELAY.code]"
  />
	<StartApprovalModal
    v-model:visible="caseManageapprovalModalVisible"
    :width="600"
    :case-id="caseId"
    title="申请办理"
		@confirm="queryCaseCurrentData"
		:filter-approval-types="[ApprovalStatusEnum.CASE_MANAGE_CLOSE.code]"
  />

  <AIContentModal
    v-model:visible="mediateOutlineVisible"
    title="调查提纲"
    :content-type="AIContentType.MEDIATE_OUTLINE"
    :business-id="props.caseId"
  >
    <template #info> "原告诉请"变动后，将重新分析 </template>
  </AIContentModal>
  <AIContentModal
    v-model:visible="caseReviewVisible"
    title="案情梳理"
    :content-type="AIContentType.CASE_REVEIW"
    :business-id="props.caseId"
  >
    <template #info> "呼叫记录"变动后，将重新分析 </template>
  </AIContentModal>
</template>

<script lang="ts" setup>
import StartApprovalModal from '@/views/case/caseManage/components/start-approval-modal.vue'
import { caseDetailsQueryCaseInfo,updateCaseSubjectMatter } from '@/api/arcoApi/caseManage/caseDetails'
import type { DescData } from '@arco-design/web-vue/es/descriptions/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { timeToDateStr } from '@/utils/dateUtil'
import { dictEnumValToObject } from '@/utils'
import { useDictStore, useTabBarStore } from '@arco/store'
import { useRoute } from 'vue-router'
import dict from '@/dict/caseManage'
import { computed, inject, nextTick, ref } from 'vue'
import bus from '@/utils/eventBus'
import { AIContentType } from '@/api/arcoApi/ai/ai.ts'
import AIContentModal from '@/views/case/caseDetails/components/aiContent/AIContentModal.vue'
import { DictTypeEnum } from '@/dict/systemManage.ts'
import { ApprovalStatusEnum } from '@/dict/caseAuditManage.ts'
import { useScroll } from '@vueuse/core'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
import { Message, Modal } from '@arco-design/web-vue'
const caseContext = inject<CaseContext>(CaseContextKey)


const scrollbarRef = ref()
const { x,measure } = useScroll(scrollbarRef)



let dictStore = useDictStore()
const props = defineProps({ caseId: { type: String, default: null } })

const { setLoading } = useLoading(true)
const tabBarStore = useTabBarStore()
const route = useRoute()

const basicForm = ref<REQUEST_CASEBASIS_DATA_TYPE>({})
const basicData = ref<DescData[]>([])
const caseNo = ref('')

const mdtCaseStatusObj = computed(() => {
  return dictEnumValToObject(dict.mdtCaseStatusOptions)
})

const mediateResultObj = computed(() => {
  return dictEnumValToObject(dict.mediateResultOptions)
})



const currentStepNumber = computed(()=>{
	let step = 1;
	//有调解办结
	if(hasThisProcess(ApprovalStatusEnum.MEDIATE_CLOSE.code)){
		//调解开始时间 +1
		if(basicForm.value.mediateBeginTime){
			step++;
		}
		//调解完成时间 +1
		if(basicForm.value.mediateDoneTime){
			step++;
		}
	}

	//调解办结完成+1
	if(basicForm.value.closeTime){
		step++;
	}
	//案管办结完成+1
	if(basicForm.value.caseManageCloseTime){
		step++;
	}
	//案件关闭+1
	if(basicForm.value.mediateCloseTime){
		step++;
	}

	return step;
})


const handleBlur = () => {
  if(basicForm.value.caseSubjectMatter == old){
    return;
  }
  Modal.open({
    title: '标的金额修改提示',
    content: '确定要将标的金额修改为 '+basicForm.value.caseSubjectMatter+' 吗？',
    okText: '确定', // 确认按钮文本
    cancelText: '取消', // 取消按钮文本
    onOk: () => {
      updateCaseSubjectMatter(basicForm.value.caseId,basicForm.value.caseSubjectMatter).then(()=>{
        Message.success("保存成功")
      },()=>{
        basicForm.value.caseSubjectMatter = old;
      })
    },
    onCancel: () => {
      basicForm.value.caseSubjectMatter = old;
    }
  });
};
let old:number = 0;
const handleFocus = ()=>{
  old = basicForm.value.caseSubjectMatter
}
const caseComplated = computed(() => basicForm.value.mdtCaseStatus == '40')

const setRenderData = (modal: any) => {
  caseNo.value = modal.caseNo
  // 设置标签页-案件编号展示
  const currentTagRoute = tabBarStore.tagList.find((tagRoute) => tagRoute.fullPath === route.fullPath)
  if (currentTagRoute && currentTagRoute.fullPath && !currentTagRoute.multipageId) {
    tabBarStore.updateTagFieldData(currentTagRoute.fullPath, 'multipageId', caseNo.value)
  }
  basicData.value = [
    { label: '案件状态', value: modal.mdtCaseStatus ? mdtCaseStatusObj.value[modal.mdtCaseStatus] : '暂无数据' },
    { label: '调解状态', value: modal.mediateStatus ? dictStore.getDictTagByTypeAndKey(DictTypeEnum.mediate_status, modal.mediateStatus) : '暂无数据' },
    { label: '调解结果', value: modal.mediateResult ? mediateResultObj.value[modal.mediateResult] : '暂无数据' }
  ]
  if (modal.closeReason) {
    basicData.value.push({ label: '终止原因', value: dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_close_reason, modal.closeReason) })
  }
  if (modal.successReason) {
    basicData.value.push({ label: '结案方式', value: dictStore.getDictTagByTypeAndKey(DictTypeEnum.case_success_reason, modal.successReason)})
  }
}


//办结流程，是否包含
const hasThisProcess = (val)=>{
	let list = basicForm.value.businessTypeObj?.processTypeList ?? []
	return list.includes(val);
}

const queryCaseCurrentData = async () => {
  try {
    setRenderData({})
    const res = await caseDetailsQueryCaseInfo(props.caseId)
    if (res) {
      basicForm.value = res

      //  更新案件基本信息至上下文
			caseContext.caseBasic.value = res

      setRenderData(res)
      bus.emit('transfer', caseComplated.value)
    } else {
      basicData.value = []
    }
  } catch (err) {
  } finally {
    setLoading(false)
  }

	//让滚动条具最右侧
	nextTick(()=>{
		measure();
		x.value+=10000
	})
}

props.caseId && queryCaseCurrentData()

const approvalModalVisible = ref(false)
const caseManageapprovalModalVisible = ref(false)

const mediateOutlineVisible = ref(false)
const caseReviewVisible = ref(false)

defineExpose({ queryCaseCurrentData })
</script>

<style scoped lang="scss">
.arco-steps-item-finish :deep(.arco-steps-icon) {
  color: var(--color-white);
  background-color: rgb(var(--primary-6));
  border: 1px solid transparent;
}
:deep(.arco-steps-item-title) {
  color: black !important;
  font-weight: bold !important;
}
:deep(.arco-card-header) {
  border-bottom: none;
}
:deep(.arco-card-body) {
  padding-top: 0;
}
.tooltip-text {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgb(var(--gray-7));
}
</style>
