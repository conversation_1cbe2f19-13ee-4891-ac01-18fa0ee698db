<template>
  <free-modal v-model:visible="modalVisible" :title="props.title" @before-open="getContent" @cancel="cancel">
    <a-button v-if="aiContent.status==1" type="primary" @click="handleCreateAIContent">
      开始分析
    </a-button>
    <slot v-else :ai-content="aiContent.content">
      <template v-if="aiContent.status==2 && !aiContent.content">
        分析中...
      </template>
      <markdown-comb v-else :markdown-content="aiContent.content" style="height: 50vh" />
    </slot>

    <template #footer>
      <a-row style="width: 100%" align="center">
        <a-col :span="6">
          <template v-for="item in aiContentStatus">
            <a-alert :key="item.value" v-if="item.value==aiContent.status" :type="item.type">
              {{ item.label }}
            </a-alert>
          </template>
        </a-col>
        <a-col :span="12">
          <icon-exclamation-circle-fill />
          <slot name="info">
          </slot>
        </a-col>
        <a-col :span="6" justify="end">
          <a-button v-if="aiContent.status==4 && props.showReanalysisButton" type="primary" style="margin-right: 30px" @click="handleCreateAIContent">
            重新分析
          </a-button>
          <a-button type="primary" style="margin-right: 30px" @click="refresh">
            刷新
          </a-button>
          <a-button type="primary" @click="cancel()">
            关闭
          </a-button>
        </a-col>
      </a-row>
    </template>
  </free-modal>
</template>


<script setup lang="ts">

import FreeModal from '@/components/FreeModal/freeModal.vue'
import MarkdownComb from '@/components/MarkdownComb/MarkdownComb.vue'
import { ref } from 'vue'
import { getAIContent, createAIContent, AIContentType } from '@/api/arcoApi/ai/ai.ts'
import { aiContentStatus } from '@/dict/ai.ts'
import { AIContentDTO } from '@/api/arcoApi/ai/types/AIContentBo.ts'
import { Message } from '@arco-design/web-vue'


let modalVisible = defineModel('visible')


const props = withDefaults(defineProps<{
  title: string
  contentType: AIContentType
  businessId: string | number
  showReanalysisButton?: boolean
}>(), {
  showReanalysisButton: true
})

let emits = defineEmits(['cancel'])

const aiContent = ref<AIContentDTO>({content:'',status:3})


const getContent = async () => {
	let res = await getAIContent(props.contentType, props.businessId)
	aiContent.value = res;
}

const refresh = () => {
	getContent()
	//提示刷新成功
	Message.success('刷新成功')
}

function cancel() {
	modalVisible.value = false
	emits('cancel')
}

function handleCreateAIContent(){
	createAIContent(props.contentType, props.businessId)
		.then(() => {
			Message.success('操作成功')
			getContent()
		})
}


</script>

<style scoped lang="scss">

</style>
