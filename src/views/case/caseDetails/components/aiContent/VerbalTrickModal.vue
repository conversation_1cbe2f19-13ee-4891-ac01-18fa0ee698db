<template>
  <AIContentModal title="沟通策略" :content-type="AIContentType.VERBAL_TRICK" :business-id="item.litigantId">
    <!--		原告不需要固定策略-->
    <template v-if="item.identityType !='1'" #default="{aiContent}">
      <a-split
        ref="verbal-trick-split"
        style="height: 60vh"
        disabled
      >
        <template #first>
          <markdown-comb :markdown-content="aiContent" style="height: 100%" />
        </template>
        <template #second>
          <markdown-comb :markdown-content="verbalTrickMd" style="height: 100%" />
          <!--          <iframe-->
          <!--						v-if="attrs.visible"-->
          <!--            src="https://astrolegal.feishu.cn/docx/KKTqdB4fpoGaQCxHbEocEM4nnQc?from=web&hide_nav=true&display=minimal&embed=true&shareToken=public&auth_page=no"-->
          <!--            :style="iframeStyle"-->
          <!--						style="border:none;"-->
          <!--            allowfullscreen></iframe>-->
        </template>
      </a-split>
    </template>
    <template #info>
      "答辩预测"变动后，将重新分析
    </template>
  </AIContentModal>
</template>
<script setup>
import AIContentModal from '@/views/case/caseDetails/components/aiContent/AIContentModal.vue'
import MarkdownComb from '@/components/MarkdownComb/MarkdownComb.vue'
import { useElementSize } from '@vueuse/core'
import { computed, useTemplateRef } from 'vue'
import { AIContentType } from '@/api/arcoApi/ai/ai.ts'
import { useAttrs } from 'vue'
//引入本地的md文件
import verbalTrickMd from './verbalTrick.md?raw'

const attrs = useAttrs()


const props = defineProps({
  item: {
    type: Object,
    default: () => {
      return {}
    }
  }
})


const el = useTemplateRef('verbal-trick-split')
const { width, height } = useElementSize(el)
//iframe 高度计算
const iframeStyle = computed(() => {
  return {
    width: `100%`,
    height: `${height.value+70}px`,
		marginTop: `-80px`
  }
})


</script>
