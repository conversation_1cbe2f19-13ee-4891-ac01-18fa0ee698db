<template>
  <div v-if="tabVisible">
    <a-space direction="vertical" align="center" class="tab-item" @click="handleClick">
      <div class="tab-icon">
        <slot name="icon">
          <component :is="props.icon" :size="23" />
        </slot>
      </div>
      <span>{{ props.title }}</span>
    </a-space>
  </div>
  <free-modal v-if="props.needModal" v-model:visible="modalVisible" :title="props.title" @ok="activeTab=null" @cancel="handleClose" :unmount-on-close="props.unmountOnClose">
    <slot />
  </free-modal>
  <template v-else>
    <slot :modal-visible="modalVisible" :handle-close="handleClose" />
  </template>
</template>
<script setup lang="ts">
import { inject } from 'vue'
import { ref, watch } from 'vue'
import FreeModal from '@/components/FreeModal/freeModal.vue'

const props = defineProps({
	tabVisible: {
		type: Boolean,
		default: true
	},
	title: {
		type: String
	},
	icon: {
		type: String
	},
	value: {
		type: String
	},
	needModal: {
		type: Boolean,
		default: true
	},
	unmountOnClose: {
		type:Boolean,
		default:true
	}
})

const emits = defineEmits(['click'])


const { activeTab } = inject('tabs')

const modalVisible = ref(false)


watch(activeTab, () => {
	if (activeTab.value == props.value) {
			modalVisible.value = true
	}
})


const handleClick = () => {
	activeTab.value = props.value
	emits('click')
}

const handleClose = () => {
	activeTab.value=null
	modalVisible.value = false
}
</script>

<style scoped lang="scss">
.tab-item:hover {
	cursor: pointer;
}

.tab-item {
	font-size: 11px;
	text-align: center;
}

.tab-icon {
	padding: 5px;
	border-radius: 5px;
	background-color: rgb(var(--primary-1));
	color: rgb(var(--primary-6));
}

.tab-icon:hover {
	outline: 3px solid rgb(var(--primary-6));
}
</style>
