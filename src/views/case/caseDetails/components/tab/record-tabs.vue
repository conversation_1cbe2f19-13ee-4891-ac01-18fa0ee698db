<template>
  <a-space :size="10" fill direction="vertical">
    <slot />
  </a-space>
</template>
<script setup lang="ts">

import { provide, ref, watch } from 'vue'

const activeTab = defineModel<string>('activeTab')

const emits = defineEmits(['change'])

const tabs = ref([])

watch(activeTab,()=>{
	emits('change',activeTab.value)
})

const selectTab = (value)=>{
	activeTab.value = value
}


provide('tabs',{tabs,activeTab,selectTab})

</script>

<style scoped lang="scss">
</style>
