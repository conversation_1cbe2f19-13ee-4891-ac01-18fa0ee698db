<template>
  <div :style="{ height: listHeight + 'px', overflow: 'hidden' }">
    <a-list :split="false" :bordered="false" :scrollbar="false" :max-height="props.listHeight" class="case-detail-item">
      <a-list-item v-for="tabItem in moduleTabs" :key="tabItem.dataId">
        <a-card :title="tabItem.label" class="cardContent">
          <template #extra>
            <a-button v-auth="['caseSave']" size="mini" type="primary" @click="handleRecordModal(tabItem)">
              {{ tabItem.allowMultipleRecord ? '新增' : '编辑' }}
            </a-button>
          </template>
          <a-table
            v-if="tabItem.allowMultipleRecord"
            row-key="dataId"
            :pagination="false"
            :columns="tabItem.columns"
            :style="{ width: '100%' }"
            :scroll="{ maxHeight: '465px' }"
            :data="tabItem.tableData"
            :bordered="false"
            row-class="custom-row-cell"
          >
            <template
              v-for="columnItem in tabItem.columns"
              :key="columnItem.slotName"
              #[columnItem.slotName]="{ record, rowIndex }"
            >
              <template v-if="columnItem.slotName === 'operat'">
                <a-space v-if="record.view" :size="5">
                  <a-tooltip content="编辑">
                    <a-button type="text" size="mini" @click="record.view = false">
                      <template #icon> <icon-edit /></template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="删除">
                    <a-button type="text" size="mini" @click="handleDeleteRecord(record, tabItem.tableData, rowIndex)">
                      <template #icon> <icon-delete /></template>
                    </a-button>
                  </a-tooltip>
                </a-space>
                <a-space v-else :size="5">
                  <a-tooltip content="保存">
                    <a-button type="text" size="mini" @click="handleRecordSave(record)">
                      <template #icon> <icon-save /></template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="取消">
                    <a-button type="text" size="mini" @click="record.view = true">
                      <template #icon> <icon-close /></template>
                    </a-button>
                  </a-tooltip>
                </a-space>
              </template>
              <template v-else>
                <span v-if="record.view">{{ record[columnItem.dataIndex] }}</span>
                <component
                  :is="handleUseComponent(columnItem.fieldAttr?.componentRelation)"
                  v-else
                  v-model="record.data[`${columnItem.dataIndex}`]"
                  v-bind="handleComponentAttr(columnItem.fieldAttr)"
                >
                  <template v-if="columnItem.fieldAttr?.componentRelation === 'ASelect'" #option="{ data }">
                    <a-tooltip v-if="data.value && data.value.length > 8" :content="data.value">
                      <span class="mdt-form-option-value"> {{ data.value }}</span>
                    </a-tooltip>
                  </template>
                </component>
                <!-- <CollapseInput></CollapseInput> -->
              </template>
            </template>
          </a-table>
          <a-descriptions v-else class="customDataTable" :data="tabItem.renderData" layout="vertical">
            <template #label="{ label, data }">
              <span v-if="data.isApprovalValidated == 1" style="color: red"> * </span>
              <ellipsis-text :text="label" :max-length="8" />
            </template>
            <template #value="{ value, data }">
              <a-skeleton v-if="loading" :animation="true">
                <a-skeleton-line :widths="['200px']" :rows="1" />
              </a-skeleton>
              <span v-else :class="{ 'description-text-gray': !value }">
                {{ data.label ? value || '(暂无数据)' : '' }}
              </span>
            </template>
          </a-descriptions>
        </a-card>
      </a-list-item>
    </a-list>
  </div>
  <CaseEditModal
    ref="approvalModalRef"
    v-model:visible="caseEditModalVisible"
    :width="700"
    :data-id="selectMoudleTabItem.dataId"
    :module-id="selectMoudleTabItem.tmplModuleId"
    :title="selectMoudleTabItem.label"
    :render-fields="selectMoudleTabItem.fieldData"
    :render-data="selectMoudleTabItem.renderData"
    @comfirm="handleCaseEditComfirm"
  />
</template>
<script lang="ts" setup>
import { Message, Modal, type TableColumnData, type TableData } from '@arco-design/web-vue'
import CaseEditModal from '@/views/case/caseDetails/components/modal/case-edit-modal.vue'
import { caseDetailsQueryModuleInfo } from '@/api/arcoApi/caseManage/caseDetails.ts'
import type { DescData } from '@arco-design/web-vue/es/descriptions/interface'

import { componentMapObject, componentMapModals } from '@/dict/businessConf'
import { caseDetailsCaseDelete } from '@/api/arcoApi/caseManage/caseDetails'
import { caseDetailsCaseEdit } from '@/api/arcoApi/caseManage/caseDetails'
// import CollapseInput from '@/components/CollapseInput/index.vue'
import EllipsisText from '@/components/ellipsis-text/index.vue'
import useLoading from '@arco/hooks/loading.ts'
import bus from '@/utils/eventBus'
import { h, ref } from 'vue'
import dayjs from 'dayjs'
import { hasRule } from '@/directives/rule.ts'

interface ReSetTableColumnData extends TableColumnData {
  fieldAttr?: REQUEST_GET_FIELD_LIST_TYPE
  dataIndex: string
  slotName: string
}

type MODULE_DATA_TYPE = {
  fieldData: REQUEST_GET_FIELD_LIST_TYPE[]
  columns: ReSetTableColumnData[]
  tmplModuleId: string | number
  allowMultipleRecord: 0 | 1
  tableData: TableData[]
  renderData: DescData[]
  dataId: string
  label: string
}

const props = defineProps({ caseId: { type: String, default: '' }, listHeight: { type: Number } })

const { loading, setLoading } = useLoading(true)

const moduleTabs = ref<MODULE_DATA_TYPE[]>([])
const caseEditModalVisible = ref(false)

const selectMoudleTabItem = ref<MODULE_DATA_TYPE>({
  allowMultipleRecord: 0,
  tmplModuleId: '',
  renderData: [],
  fieldData: [],
  tableData: [],
  columns: [],
  dataId: '',
  label: ''
})

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))

const handleUseComponent = (name?: string) => {
  if (name) {
    return name === 'ATextarea' ? 'CollapseInput' : componentMapObject[name] || 'AInput'
  }
  return 'AInput'
}

const handleRecordAdd = (formData: MODULE_DATA_TYPE) => {
  if (formData.tableData.length < 100) {
    let columData = new Object()
    formData.fieldData.forEach((item) => {
      columData[item.fieldTitle] = componentMapModals[item.componentRelation]
    })
    formData.tableData.push({
      tmplModuleId: formData.tmplModuleId,
      dataId: formData.dataId,
      data: columData,
      view: false,
      ...columData
    })
  } else {
    Message.warning('记录已达到上限')
  }
}

const handleRecordSave = (columData: TableData) => {
  let paramsData = {}
  let isNullData = true
  for (const key in columData.data) {
    if (columData.data[key].length > 0 || columData.data[key]) {
      isNullData = false
    }
    let isArr = Array.isArray(columData.data[key])
    paramsData[key] = isArr ? columData.data[key].join(',') : columData.data[key]
  }
  if (isNullData) {
    Message.warning('该记录未登记数据，请核对后再保存！')
    return
  }

  let params = {
    tmplModuleId: columData.tmplModuleId,
    dataId: columData.dataId,
    caseId: props.caseId,
    data: paramsData
  }
  columData.loading = true
  caseDetailsCaseEdit(params)
    .then((res) => {
      for (const key in paramsData) {
        if (Object.prototype.hasOwnProperty.call(columData, key)) {
          columData[key] = paramsData[key]
        }
      }
      columData.dataId = res
      columData.view = true
      Message.success('操作成功')
    })
    .finally(() => {
      columData.loading = false
    })
}

const handleComponentAttr = (field?: REQUEST_GET_FIELD_LIST_TYPE) => {
  let attrs = new Object()
  attrs['allowClear'] = true
  switch (field && field.componentRelation) {
    case 'ASelectMultiple':
      attrs['multiple'] = true
      attrs['maxTagCount'] = 1
      break
    case 'ADatePicker':
      break
    case 'ADateTimePicker':
      attrs['showTime'] = true
      break
    case 'AInputNumber':
      break
    case 'ATextarea':
      attrs['autoSize'] = { minRows: 5 }
      break
    default:
      break
  }
  // json转换
  if (field && field.fieldValues) {
    let _fieldValues = JSON.parse(field.fieldValues)
    if (_fieldValues && Object.prototype.toString.call(_fieldValues) === '[object Array]') {
      attrs['options'] = _fieldValues
    }
  }

	//查看是否是只读角色
	if(hasRule(field?.readOnlyRoles)){
		attrs['disabled'] = true
	}

  return attrs
}

// const handleComponentEvents = (component: string) => {
//   return {
//     mousedown: (e) => {
//       e.target.className = ''
//     }
//   }
// }

const handleRecordModal = (formData: MODULE_DATA_TYPE) => {
  if (formData.allowMultipleRecord) {
    handleRecordAdd(formData)
  } else {
    for (const key in selectMoudleTabItem.value) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        selectMoudleTabItem.value[key] = formData[key]
      } else {
        selectMoudleTabItem.value[key] = null
      }
    }
    caseEditModalVisible.value = true
  }
}

const handleDeleteRecord = (columData: TableData, data: TableData[], rowIndex: number) => {
  if (columData.dataId) {
    Modal.warning({
      title: '提示',
      hideCancel: false,
      content: `删除后将不可恢复，是否确认删除该行数据？`,
      onOk: () => {
        caseDetailsCaseDelete(columData.dataId).then(() => {
          Message.success('操作成功')
          queryCaseCurrentData()
        })
      },
      onCancel: () => {}
    })
  } else {
    // 根据下标删除
    data.splice(rowIndex, 1)
  }
}

const setCustomModuleData = (customData: REQUEST_CASEDETAILS_CUSTOM_LIST_DATA_TYPE[]) => {
  if (customData && customData.length) {
    moduleTabs.value = customData.map((item) => {
      let columns: ReSetTableColumnData[] = []
      let tableData: TableData[] = []
      let renderData: DescData[] = []

      if (item.allowMultipleRecord === 1) {
        // 表格
        item.values.forEach((valueItem) => {
          let tableItem = new Object()
          tableItem['data'] = new Object()
          tableItem['view'] = true
          // tableItem['renderData'] = []
          // tableItem['fieldData'] = item.fields
          // tableItem['label'] = item.moduleTitle
          tableItem['dataId'] = valueItem.dataId
          tableItem['tmplModuleId'] = item.tmplModuleId

          for (let _i = 0; _i < valueItem.data.length; _i++) {
            const { key, value } = valueItem.data[_i]
            tableItem[key] = value
            // tableItem['renderData'].push({ label: key, value: value || '' })
            let fieldItem = item.fields.find((attr) => attr.fieldTitle === key)
            switch (fieldItem && fieldItem.componentRelation) {
              case 'ASelectMultiple':
                tableItem['data'][key] = value ? value.split(',') : []
                break
              case 'ADatePicker':
                // 过滤非时间字符串
                tableItem['data'][key] = dayjs(value).isValid() ? value : ''
                break
              case 'ADateTimePicker':
                tableItem['data'][key] = dayjs(value).isValid() ? value : ''
                break
              case 'AInputNumber':
                tableItem['data'][key] = value && !isNaN(Number(value)) ? Number(value) : 0
                break
              case 'ATextarea':
                tableItem['data'][key] = value ? value : ''
                break
              default:
                tableItem['data'][key] = value || ''
                break
            }
          }
          tableData.push(tableItem)
        })

        let titleRender = (item) => {
          if (item.isApprovalValidated == 1) {
            return h('span', [h('span', { style: 'color:red' }, '*'), item.fieldTitle])
          }
          return h('span', item.fieldTitle)
        }

        columns = item.fields.map((fieldItem, index) => ({
          dataIndex: fieldItem.fieldTitle,
          title: titleRender(fieldItem),
          slotName: `field${index}`,
          fieldAttr: fieldItem,
          ellipsis: true,
          tooltip: true,
          width: 200
        }))
        columns.push({
          dataIndex: 'operat',
          slotName: 'operat',
          align: 'center',
          fixed: 'right',
          title: '操作',
          width: 160
        })
      } else {
        // 表单
        for (let i in item.valueList) {
          let listItem = item.valueList[i]
          const { key, value } = listItem
          const { isApprovalValidated } = item.fields[i]
          renderData.push({ label: `${key}`, value: value || '', isApprovalValidated: isApprovalValidated})
        }
      }

      return {
        allowMultipleRecord: item.allowMultipleRecord,
        tmplModuleId: item.tmplModuleId,
        label: item.moduleTitle,
        fieldData: item.fields,
        dataId: item.dataId,
        renderData,
        tableData,
        columns
      }
    })
  }
}

const handleCaseEditComfirm = () => {
  caseEditModalVisible.value = false
  queryCaseCurrentData()
}

const queryCaseCurrentData = async () => {
  try {
    const res = await caseDetailsQueryModuleInfo(props.caseId)
    if (res) {
      setCustomModuleData(res)
    }
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

if (props.caseId) {
  queryCaseCurrentData()
}
</script>
<style scoped lang="scss">
:deep(.arco-card-header) {
  border-bottom: none;
  padding-bottom: 0;
}

:deep(.arco-card-body) {
  padding-top: 0;
}

:deep(.arco-list-item-main) {
  width: 100%;
}

.cardContent > :deep(.arco-card-body) {
  padding-top: 0 !important;
  width: 100%;
}

:deep(.arco-descriptions-item-label) {
  vertical-align: top;
  width: 30%;
}

:deep(.arco-descriptions-item-value) {
  vertical-align: top;
  width: 30%;
}

.customDataTable :deep(.arco-descriptions-table) {
  table-layout: fixed !important;
  width: 100%;
}

.custom-row-cell :deep(.arco-table-td) {
  position: relative;
}

.custom-row-cell .arco-table-tex {
  position: relative;
}

.case-detail-item {
  background-color: var(--color-bg-2);
}

.case-detail-item :deep(.arco-list) {
  // overflow: hidden !important;
  // &:hover {
  //   overflow: auto !important;
  // }
  &::-webkit-scrollbar {
    width: 8px;
    height: 5px;
    overflow: auto;
  }

  &::-webkit-scrollbar-track {
    border-radius: 10px !important;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(144, 147, 153, 0.5);
  }
}
</style>
