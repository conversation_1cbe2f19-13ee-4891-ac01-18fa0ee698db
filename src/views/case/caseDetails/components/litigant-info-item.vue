<template>
  <a-card>
    <template #title>
      <span style="font-size: 14px">
				{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.litigant_identity_type,item.identityType)}}
			</span>
    </template>
    <template #extra>
      <a-space v-if="!caseComplated" :size="0">
        <a-tooltip content="单个修复">
          <a-button
            v-auth="['caseContactRestoration']"
            :loading="item.repairloading"
            type="text"
            @click="handleOtherLossRepair(item, 'repairloading')"
          >
            <template #icon>
              <icon-tool />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip content="调解记录登记">
          <a-button type="text" @click="handleAddCallrepaire(item)">
            <template #icon>
              <icon-message />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip content="编辑">
          <a-button type="text" @click="emits('handleView')">
            <template #icon>
              <icon-edit />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip content="删除">
          <a-button v-auth="['caseLitiganteDelete']" type="text" @click="handleDelete(item)">
            <template #icon>
              <icon-delete />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </template>
    <ellipsis-text
      :ellipsis="{ rows: 1, showTooltip: true }"
      style="font-size: 16px; font-weight: bold; margin-bottom: 5px"
      :text="item.litigantName"
      :max-length="10"
    />
    <div v-if="item.litigantPhone" style="margin-top: 5px;">
      <div v-for="p in item.phoneList" :key="p.phone" style="padding: 2px 0px;">
        <a-row align="center" :gutter="1">
          <a-col :flex="1">
            <a-space :size="5">
              <a-tooltip :disabled="item.litigantPhone.length < 12" :content="litigantPhoneView(p)">
                <span class="row-record-text">{{ litigantPhoneView(p) }}</span>
              </a-tooltip>
              <!-- 添加小眼睛图标 -->
              <a-tooltip v-if="isPhoneSign" :content="p.show? '隐藏完整号码' : '显示完整号码'">
                <icon-eye-invisible v-if="p.show" class="row-record-icon" @click="p.show=false" />
                <icon-eye v-else class="row-record-icon" @click="p.show=true" />
              </a-tooltip>
              <a-tag v-show="p.checkTag !=null && p.checkTag!= undefined" color="green">
                <ellipsis-text :text="phoneStatusObj[p.checkTag] || p.checkTag || '正常'" :max-length="3" />
              </a-tag>
            </a-space>
          </a-col>
          <a-col :span="7" class="mdt-col-flexend" justify="end">
            <a-space v-if="!caseComplated" :size="10">
              <a-tooltip content="上次联系">
                <icon-clock-circle v-show="p.preTag !=null && p.preTag!= undefined" style="color: sandybrown" class="row-record-icon"></icon-clock-circle>
              </a-tooltip>
              <a-tooltip content="拨打">
                <icon-phone class="row-record-icon" @click="callPhone(item, 'litigantPhone',p.phone)"></icon-phone>
              </a-tooltip>
              <a-tooltip content="检测">
                <icon-sync
                  class="row-record-icon"
                  :spin="p.checksloading"
                  @click="handleDetectionRepair(item,p)"
                ></icon-sync>
              </a-tooltip>
            </a-space>
          </a-col>
        </a-row>
      </div>
    </div>
    <div class="lit-desc" v-if="item.litigantDesc != null && item.litigantDesc != ''">
      <strong>备注：</strong>
      <a-tooltip :content="item.litigantDesc">
        <span class="lit-desc-span">
         {{item.litigantDesc}}
        </span>
      </a-tooltip>
    </div>
    <a-row v-if="handleVirtulNoRender(item)" align="center" :gutter="5">
      <a-col :flex="1">
        <div class="row-record-content">
          <a-tooltip :disabled="item.virtualNo.length < 12" :content="item.virtualNo">
            <span class="row-record-text">{{ item.virtualNo }}</span>
          </a-tooltip>
        </div>
      </a-col>
      <a-col :span="7" class="mdt-col-flexend" justify="end">
        <a-tooltip content="拨打">
          <icon-phone class="row-record-icon" @click="callPhone(item, 'virtualNo',item.virtualNo)"></icon-phone>
        </a-tooltip>
      </a-col>
    </a-row>
    <a-row  align="center" :gutter="5" style="margin-top: 10px">
      <a-col :flex="2.6">
        <a-space>
          <IconFont type="icon-ai" :size="14" />
          性格测试
        </a-space>
      </a-col>
      <template v-if="item.mbtiStatus==1">
        <a-col :flex="1.2" class="mdt-col-flexend" justify="end">
          <a-link @click="handleCreateMbti(item)"><u>开始分析</u></a-link>
        </a-col>
      </template>
      <template v-else-if="!item.mbti">
        <a-col :flex="1.2" class="mdt-col-flexend" justify="end">
          <a-link><u>分析中...</u></a-link>
        </a-col>
      </template>
      <template v-else>
        <a-col :flex="1.2" class="mdt-col-flexend" justify="end">
          <a-link @click="mbtiReportVisible=true"><u>{{ item.mbti }}</u></a-link>
        </a-col>
        <a-col :flex="2" class="mdt-col-flexend" justify="end">
          <a-link v-if="item.mbti && item.identityType !='1'" @click="guessReplyVisible=true"><u>答辩预测</u></a-link>
        </a-col>
        <a-col :flex="2" class="mdt-col-flexend" justify="end">
          <a-link v-if="item.mbti" @click="verbalTrickVisible=true"><u>沟通策略</u></a-link>
        </a-col>
      </template>
    </a-row>
  </a-card>



  <AIContentModal
    v-model:visible="mbtiReportVisible" title="MBTI性格测试报告" :content-type="AIContentType.MBTI_REPORT"
    :business-id="item.litigantId"
  >
    <template #info>
      "当事人信息"、"调查提纲"变动后，将重新分析
    </template>
  </AIContentModal>
  <AIContentModal
    v-model:visible="guessReplyVisible" title="答辩预测" :content-type="AIContentType.GUESS_REPLY"
    :business-id="item.litigantId"
  >
    <template #info>
      "性格测试报告"、"调查提纲"变动后，将重新分析
    </template>
  </AIContentModal>
  <VerbalTrickModal v-model:visible="verbalTrickVisible" :item="item" />
</template>
<script lang="ts" setup>
import EllipsisText from '@/components/ellipsis-text/index.vue'
import { caseDetailsDeleteLit } from '@/api/arcoApi/caseManage/caseDetails'
import { singleNumberDetect } from '@/api/arcoApi/mediateManage/jobLog/numberDetectRecord'
import { singleCallRepairNew } from '@/api/arcoApi/mediateManage/jobLog/callRepairRecord'
import { Message, Modal } from '@arco-design/web-vue'

import { computed, ref } from 'vue'
import { phoneAndTelReg } from '@/assets/ts/regexp'
import { dictEnumValToObject } from '@/utils'
import { useAppStore, useUserStore } from '@arco/store'
import useTccc from '@arco/hooks/tccc'
import dict from '@/dict/caseManage'
import bus from '@/utils/eventBus'
import { AIContentType,createAIContent } from '@/api/arcoApi/ai/ai.ts'
import AIContentModal from '@/views/case/caseDetails/components/aiContent/AIContentModal.vue'
import VerbalTrickModal from '@/views/case/caseDetails/components/aiContent/VerbalTrickModal.vue'
import { dzPhone } from '@/utils/proto.ts'
import { DictTypeEnum } from '../../../../dict/systemManage.ts'

let {item,caseId,litigantCall} = defineProps<{item: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE,caseId:string,litigantCall}>()

const emits = defineEmits<{
	addCallrepaireRecord: [item: REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE, canClose?: boolean]
	getTableData:[],
	handleView:[]
}>()

type RowLoadingFieldType = 'repairloading' | 'refreshloading'

const userStore = useUserStore()
const { mapFormBox } = useTccc()

const pageZhName = ref('当事人')


const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))
const phoneStatusObj = computed(() => {
	return dictEnumValToObject(dict.litigantPhoneStatusOptions)
})



const handleVirtulNoRender = (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE) => {
	return row.virtualNo && row.batchExpireDate && litigantCall[row.litigantId]
}

const handleDelete = (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE) => {
	Modal.warning({
		title: '提示',
		hideCancel: false,
		alignCenter: true,
		content: `是否确认删除该${pageZhName.value}`,
		onOk: () => {
			row.litigantId &&
			caseDetailsDeleteLit(row.litigantId)
				.then(() => {
					Message.success('操作成功')
					emits('getTableData')
				})
				.catch(() => {
				})
		},
		onCancel: () => {
		}
	})
}


const handleOtherLossRepair = (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE, prop: RowLoadingFieldType) => {
	Modal.warning({
		title: '提示',
		hideCancel: false,
		alignCenter: true,
		content: '是否对该当事人进行失联修复',
		onOk: () => {
			if (row.litigantId) {
				row[prop] = true
				singleCallRepairNew({ caseId: caseId, litigantId: row.litigantId })
					.then((res) => {
						if (res) {
							Message.success('操作成功')
							emits('getTableData')
						}
					})
					.finally(() => {
						row[prop] = false
					})
			} else {
				Message.warning('请选择当事人')
			}
		},
		onCancel: () => {
		}
	})
}

const handleDetectionRepair = async (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE,p:any) => {
	if (!p.phone) {
		Message.info('暂无联系方式')
		return
	}
	if (!phoneAndTelReg.test(p.phone)) {
		Message.info('当事人联系方式格式不正确')
		return
	}
	Modal.warning({
		title: '提示',
		hideCancel: false,
		alignCenter: true,
		content: `是否对该号码发起检测？`,
		onOk: () => {
			if (row.litigantId) {
				p.checksloading = true
				singleNumberDetect({ caseId: caseId, litigantId: row.litigantId ,litigantPhone: p.phone})
					.then((res) => {
						if (res) {
							Message.success('号码检测成功')
							emits('getTableData')
						}
					})
					.finally(() => {
						p.checksloading = false
					})
			} else {
				Message.warning('请选择当事人')
			}
		},
		onCancel: () => {
		}
	})
}





const handleAddCallrepaire = (item: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE) => {
  let contactPhoneList:any=[];
  if(item.litigantPhone){
    contactPhoneList = item.litigantPhone.split(",");
  }
	emits('addCallrepaireRecord', {
		litigantId: item.litigantId,
		identityType: item.identityType,
		contactorUser: item.litigantName,
		contactPhone: contactPhoneList.length>0?contactPhoneList[0]:"",
    contactPhoneList: contactPhoneList
	} as REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE)
}

async function callPhone(item: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE, field: string, phoneNumber:string) {
	try {
		await mapFormBox(item, caseId, field,phoneNumber)
		// 只有调解员需要拨打
		if (!userStore.getUserRoleList.includes('调解员')) return
		setTimeout(() => {
			emits(
				'addCallrepaireRecord',
				{
					litigantId: item.litigantId,
					identityType: item.identityType,
					contactorUser: item.litigantName,
					contactPhone: phoneNumber,
					mediateType: '1'
				} as REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE,
				false
			)
		}, 1000)
	} catch (error) {
		console.log(error)
	}
}



const mbtiReportVisible = ref(false)
const guessReplyVisible = ref(false)
const verbalTrickVisible = ref(false)


const handleCreateMbti = async (item)=>{
	const res = await createAIContent(AIContentType.MBTI_REPORT,item.litigantId).then((res) => {
		Message.success('操作成功')
		emits('getTableData')
	})
}




const appStore = useAppStore()
//是否脱敏
const isPhoneSign = appStore.systemParam.isPhoneSign
const litigantPhoneView = (item: LITIGANT_PHONE_TYPE)=>{
	if(!isPhoneSign){
		return item.phone;
	}
	if(item.show){
		return item.phone;
	}
	return dzPhone(item.phone);
}



</script>
<style scoped lang="scss">

.row-record-text {
	display: inline-block;
	max-width: 120px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.row-record-icon {
	cursor: pointer;
	color: rgb(var(--primary-6));
}
.lit-desc{
  margin-top: 5px;
  display: -webkit-box;          /* 关键：转换为弹性盒子 */
  -webkit-box-orient: vertical;  /* 垂直方向排列 */
  -webkit-line-clamp: 3;        /* 限制显示 3 行 */
  overflow: hidden;             /* 隐藏超出部分 */
  text-overflow: ellipsis;      /* 超出时显示省略号 */
  word-break: break-word;       /* 允许单词换行（可选） */
}
.lit-desc-span{
  cursor: pointer;
}

</style>
