<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    :hide-cancel="!props.canClose"
    :mask-closable="props.canClose"
		@before-open="initFormModel"
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="left"
        :disabled="props.caseComplated"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">调解状态</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                field="mediateStatus" label="调解状态"
                :rules="[{ required: true, message: '请输入调解状态' }]"
              >
								<DictDataSelect v-model="baseInfoForm.mediateStatus" :dict-type="DictTypeEnum.mediate_status" :ignoreKeys="[0,1,2,3]" checkStrictly />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">调解信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="litigantId" :rules="[{ required: true, message: '请选择联系对象' }]" label="联系对象">
                <a-select
                  v-model="baseInfoForm.litigantId"
                  :options="litigantOptions"
                  placeholder="请选择"
                  allow-clear
                  @change="handleLitigantSelect"
                />
                <!-- <a-input v-model="baseInfoForm.contactorUser" :max-length="50" placeholder="请输入内容" allow-clear /> -->
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="identityType" label="当事人身份" :rules="[{ required: true, message: '请选择当事人身份' }]">
								<DictDataSelect v-model="baseInfoForm.identityType" :dict-type="DictTypeEnum.litigant_identity_type" />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item
                field="contactPhone"
                label="联系电话"
              >
                <a-select v-model="baseInfoForm.contactPhone" placeholder="请选择内容" allow-clear allow-create>
                  <a-option v-for="contact in baseInfoForm.contactPhoneList" :key="contact">{{ contact }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item field="mediateType" label="调解类型" :rules="[{ required: true, message: '请选择调解类型' }]">
                <a-select
                  v-model="baseInfoForm.mediateType"
                  :options="mediateTypeOptions"
                  placeholder="请选择"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item field="mediateTime" label="调解时间" :rules="[{ required: true, message: '请选择调解时间' }]">
                <a-date-picker v-model="baseInfoForm.mediateTime" style="width: 100%">
                  <template #suffix-icon><icon-schedule /></template>
                </a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="nextDoTime" label="下次跟进时间">
                <a-date-picker v-model="baseInfoForm.nextDoTime" style="width: 100%">
                  <template #suffix-icon><icon-schedule /></template>
                </a-date-picker>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item field="mediatorName" label="调解员">
                <a-input v-model="baseInfoForm.mediatorName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
<!--              <a-form-item field="isAnswer" label="是否接听">-->
<!--                <a-select v-model="baseInfoForm.isAnswer" :options="isAnswerOptions" placeholder="请选择" allow-clear />-->
<!--              </a-form-item>-->
              <a-form-item v-if="baseInfoForm.mediateStatus && continueMediateStatusKes.includes(Number(baseInfoForm.mediateStatus))" field="expectedSuccessDate" label="预计调成日期" :rules="[{ required: true, message: '请选择预计调成时间' }]">
								<a-date-picker v-model="baseInfoForm.expectedSuccessDate" style="width: 100%">
									<template #suffix-icon><icon-schedule /></template>
								</a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="intentionAmount" label="原/被告意向金额">
                <a-input-number v-model="baseInfoForm.intentionAmount" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                :label-col-props="{ span: 5 }"
                :wrapper-col-props="{ span: 19 }"
                field="otherDemand"
                :rules="[{ required: true, message: '请填写原/被告其他诉求' }]"
                label="原/被告其他诉求"
              >
                <a-textarea
                  v-model="baseInfoForm.otherDemand"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                  style="margin-left: -10px"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                :label-col-props="{ span: 5 }"
                :wrapper-col-props="{ span: 19 }"
                field="needHelpContent"
                label="需配合内容"
              >
                <a-textarea
                  v-model="baseInfoForm.needHelpContent"
                  :auto-size="{ minRows: 4 }"
                  :max-length="250"
                  style="margin-left: -10px"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { SelectInstance } from '@arco-design/web-vue/es/select'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { caseDetailsQueryLit, caseDetailsSaveRecord } from '@/api/arcoApi/caseManage/caseDetails'
import { Message } from '@arco-design/web-vue'
import { useDictStore, useUserStore } from '@arco/store'
import dict from '@/dict/caseManage'
import { inject, ref, watch } from 'vue'
import dayjs from 'dayjs'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'
const { caseBasic } = inject('caseBasic')
let dictStore = useDictStore();

let continueMediateStatusKes = dictStore.getOffspringKeys(DictTypeEnum.mediate_status,6)

interface Props {
  width?: string | number
  caseId: string | number
  canClose:boolean
  title: string
	caseComplated:boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: '700px',
  canClose:true,
  caseId: '',
  title: '',
	caseComplated:false
})

const emits = defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const userStore = useUserStore()

const generateFormModel = () => {
  return {
    mediatorName: userStore.userInfo.employeeName || '',
		mediateTime: dayjs().format('YYYY-MM-DD'),
    needHelpContent: '',
    mediateStatus: '4',
    intentionAmount: 0,
    contactorUser: '',
    contactPhone: '',
    contactPhoneList: '',
    identityType: '',
    otherDemand: '',
    mediateType: '',
    litigantId: '',
    nextDoTime: '',
    recordId: '',
    isAnswer: '',
    caseId: ''
  }
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_MEDIATE_SAVE_PARAM_TYPE>(generateFormModel())

const mediateTypeOptions = ref<SelectOptionData[]>(dict.mediateTypeOptions)
const isAnswerOptions = ref<SelectOptionData[]>(dict.isAnswerOptions)
const litigantOptions = ref<SelectOptionData[]>([])
watch(
  () => ({
    form: baseInfoForm.value.litigantId,
    options: litigantOptions.value
  }),
  () => {
    if(baseInfoForm.value.litigantId != null
      && baseInfoForm.value.litigantId != ""
      && baseInfoForm.value.litigantId != undefined
      && litigantOptions.value.length > 0
    ){
      baseInfoForm.value.contactPhoneList = getContactPhoneList(baseInfoForm.value,litigantOptions.value);
    }
  },
  { deep: true }
);
function getContactPhoneList(baseInfoForm: any,litigantList: any){
  for(let l of litigantList){
    if(l.value === baseInfoForm.litigantId){
      if(l.litigantPhone){
        let split = l.litigantPhone.split(",")
        if(baseInfoForm.contactPhone && !split.includes(baseInfoForm.contactPhone)){
          split.push(baseInfoForm.contactPhone);
        }
        return split;
      }else{
        return [];
      }
    }
  }
}
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const handleLitigantSelect: SelectInstance['onChange'] = (value) => {
  let targetLitigant = litigantOptions.value.find((item) => item.value === value)
  if (targetLitigant) {
    // 解析联系方式
    if(targetLitigant.litigantPhone){
      let phoneList = targetLitigant.litigantPhone.split(",");
      baseInfoForm.value.contactPhone = phoneList[0] || '';
    }
    baseInfoForm.value.identityType = targetLitigant.identityType || ''
    baseInfoForm.value.contactorUser = targetLitigant.label || ''
  }
}

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    baseInfoForm.value.caseId = props.caseId
    caseDetailsSaveRecord(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        emits('confirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const okClick = () => {}

const setFormModel = (modal: REQUEST_CASEDETAILS_MEDIATE_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = {...baseInfoForm.value,..._.cloneDeep(modal)}
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()

	//如果basic存在调解状态使用默认值
	if(caseBasic.value && caseBasic.value.mediateStatus){
		//只有当状态转数字大于3时，使用默认值
		if(Number(caseBasic.value.mediateStatus) > 3){
			baseInfoForm.value.mediateStatus = String(caseBasic.value.mediateStatus)
		}
	}
}

const getLitigantList = () => {
  if (!props.caseId) return
  caseDetailsQueryLit(props.caseId).then((res) => {
    if (res && res.length) {
      litigantOptions.value = res.map((item) => ({
        litigantPhone: item.litigantPhone,
        identityType: item.identityType,
        label: item.litigantName,
        value: item.litigantId
      }))
    }
  })
}

watch(
  () => visible.value,
  (val) => val && getLitigantList()
)

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
