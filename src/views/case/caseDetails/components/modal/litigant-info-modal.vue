<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <template #title>
      <a-row style="width: 100%">
        <a-col :span="16">{{ title }}</a-col>
        <a-col v-if="disabled" v-auth="['caseLitiganteSave']" :span="8" class="mdt-col-flexend">
          编辑状态:
          <a-switch v-model="switchStatus"></a-switch>
        </a-col>
      </a-row>
    </template>
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        :rules="baseInfoRules"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">当事人类型</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }" field="litigantType">
                <a-radio-group
                  v-model="baseInfoForm.litigantType"
                  :disabled="hasText"
                  @change="handleSelectLitigantType"
                >
                  <a-radio v-for="item in litigantTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">当事人信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="identityType" label="身份类别">
                <span v-if="hasText" class="mdt-form-text">
									{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.litigant_identity_type,baseInfoForm.identityType)}}
                </span>
								<DictDataSelect  v-else v-model="baseInfoForm.identityType" :dict-type="DictTypeEnum.litigant_identity_type" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="litigantName" :label="baseInfoForm.litigantType === '1'?'姓名':'企业名称'">
                <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.litigantName || '(暂无数据)' }}</span>
                <a-input
                  v-else
                  v-model="baseInfoForm.litigantName"
                  :max-length="50"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="idType" label="证件类型">
                <span v-if="hasText" class="mdt-form-text">
                  {{ idTypeObj[baseInfoForm.idType] || baseInfoForm.idType }}
                </span>
                <a-select
                  v-else
                  v-model="baseInfoForm.idType"
                  :options="idTypeOptions"
                  placeholder="请选择"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="idNo" label="证件号码">
                <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.idNo || '(暂无数据)' }}</span>
                <a-input v-else v-model="baseInfoForm.idNo" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <template v-if="baseInfoForm.litigantType === '1'">
              <a-col :span="12">
                <a-form-item field="litigantPhone" label="联系方式">
                  <span v-if="hasText" class="mdt-form-text">{{ litigantPhoneFormat || '(暂无数据)' }}</span>
                  <a-input-tag v-else v-model="litigantPhoneList" :unique-value="true" placeholder="按回车键输入" allow-clear :retain-input-value="true" @blur="blur"/>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="sex" label="性别">
                  <a-radio-group v-model="baseInfoForm.sex" :disabled="hasText">
                    <a-radio v-for="item in sexOptions" :key="item.value" :value="item.value">
                      {{ item.label || '(暂无数据)' }}
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="nation" label="民族">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.nation || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.nation" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="birth" label="出生日期">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.birth || '(暂无数据)' }}</span>
                  <a-date-picker v-else v-model="baseInfoForm.birth" style="width: 100%">
                    <template #suffix-icon>
                      <icon-schedule />
                    </template>
                  </a-date-picker>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="post" label="职业或职务">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.post || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.post" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item field="contactAddress" label="联系地址">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.contactAddress || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.contactAddress" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
							<a-col :span="12">
								<a-form-item field="idAddress" label="户籍地址">
									<span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.idAddress || '(暂无数据)' }}</span>
									<a-input v-else v-model="baseInfoForm.idAddress" placeholder="请输入内容" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item field="contactStatus" label="联系状态" >
									<span v-if="hasText" class="mdt-form-text">	{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.LITIGANT_CONTACT_STATUS,baseInfoForm.contactStatus)}}</span>
									<DictDataSelect v-else v-model="baseInfoForm.contactStatus" :dict-type="DictTypeEnum.LITIGANT_CONTACT_STATUS" />
								</a-form-item>
							</a-col>
            </template>
            <template v-if="baseInfoForm.litigantType === '2'">
              <a-col :span="12">
                <a-form-item field="legalAgentName" label="法定代表人姓名">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.legalAgentName || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.legalAgentName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="sex" label="性别">
                  <a-radio-group v-model="baseInfoForm.sex" :disabled="hasText">
                    <a-radio v-for="item in sexOptions" :key="item.value" :value="item.value">
                      {{ item.label || '(暂无数据)' }}
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="post" label="职业或职务">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.post || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.post" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="litigantPhone" label="联系方式">
                  <span v-if="hasText" class="mdt-form-text">{{ litigantPhoneFormat || '(暂无数据)' }}</span>
                  <a-input-tag v-else v-model="litigantPhoneList" :unique-value="true" placeholder="按回车键输入" allow-clear @blur="blur"/>
                </a-form-item>
              </a-col>
              <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
                <a-form-item field="isPrivateEnterprise" label="是否民营企业">
                  <a-radio-group v-model="baseInfoForm.isPrivateEnterprise">
                    <a-radio v-for="item in isPrivateEnterpriseOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
                <a-form-item field="registerAddress" label="注册地址">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.registerAddress || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.registerAddress" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>

              <a-col v-if="baseInfoForm.litigantType === '2'" :span="12">
                <a-form-item field="businessAddress" label="经营地址">
                  <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.businessAddress || '(暂无数据)' }}</span>
                  <a-input v-else v-model="baseInfoForm.businessAddress" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
							<a-col :span="12">
								<a-form-item field="contactStatus" label="联系状态" >
									<span v-if="hasText" class="mdt-form-text">	{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.LITIGANT_CONTACT_STATUS,baseInfoForm.contactStatus)}}</span>
									<DictDataSelect v-else v-model="baseInfoForm.contactStatus" :dict-type="DictTypeEnum.LITIGANT_CONTACT_STATUS" />
								</a-form-item>
							</a-col>
            </template>


            <a-col :span="24">
              <a-form-item
                :label-col-props="{ span: 4 }"
                :wrapper-col-props="{ span: 20 }"
                label-align="right"
                field="litigantDesc"
                label="备注"
              >
                <span v-if="hasText" class="mdt-form-text">{{ baseInfoForm.litigantDesc || '(暂无数据)' }}</span>
                <a-textarea
                  v-else
                  v-model="baseInfoForm.litigantDesc"
                  :auto-size="{ minRows: 4 }"
                  :max-length="250"
                  style="margin-left: 8px"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { RadioOption } from '@arco-design/web-vue/es/radio/interface'
import { caseDetailsSaveLit } from '@/api/arcoApi/caseManage/caseDetails'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { phoneAndTelReg } from '@/assets/ts/regexp'
import { Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/caseManage'
import { ref, computed } from 'vue'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'

interface Props {
	width?: string | number
	caseId: string | number
	title: string
}

const props = withDefaults(defineProps<Props>(), {
	width: '700px',
	caseId: '',
	title: ''
})

const emits = defineEmits<{
	(e: 'comfirm'): void
	(e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
	return {
		isPrivateEnterprise: '1',
		businessAddress: '',
		contactAddress: '',
		registerAddress: '',
		litigantName: '',
		litigantPhone: '',
		litigantType: '1',
		litigantDesc: '',
		identityType: '',
		phoneStatus: '',
		companyName: '',
		legalAgentName: '',
		litigantId: '',
		idAddress: '',
		caseId: '',
		idType: '',
		nation: '',
		birth: '',
		post: '',
		idNo: '',
		sex: '1'
	}
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE>(generateFormModel())
const baseInfoRules = ref({
	litigantPhone: [{validator: (value, cb) => {
      return new Promise(resolve => {
        if(value){
          let split = value.split(',')
          if(split){
            let msg = "";
            for(let i of split){
              let b = phoneAndTelReg.test(i);
              if(!b){
                if(msg === ""){
                  msg += i;
                }else{
                  msg += ","+i;
                }
              }
            }
            if(msg != ""){
              cb(`${msg} 手机号格式不正确`);
            }
          }
        }
        resolve();
      })
    }}],
	identityType: [{ required: true, message: '请选择身份类别' }],
	companyName: [{ required: true, message: '请输入企业名称' }],
	litigantName: [{ required: true, message: '请输入姓名' }],
	litigantType: [{ required: true, message: '请选择' }]
})

const isPrivateEnterpriseOptions = ref<RadioOption[]>(dict.isPrivateEnterpriseOptions)
const litigantTypeOptions = ref<RadioOption[]>(dict.litigantTypeOptions)
const idTypeOptions = ref<SelectOptionData[]>(dict.idCardTypeOptions)
const sexOptions = ref<RadioOption[]>(dict.sexOptions)
const baseFormRef = ref<FormInstance>()

const switchStatus = ref<boolean>(false)
const okLoading = ref<boolean>(false)
const disabled = ref<boolean>(false)

const hasText = computed(() => disabled.value && !switchStatus.value)

let litigantPhoneFormat = computed(()=>{
  if(baseInfoForm.value.litigantPhone){
    let list = baseInfoForm.value.litigantPhone.replace(/\s/g, '').split(',');
    let listMsg = "";
    let b = false;
    for(let p of list){
      if(b){
        listMsg += ", "+p;
      }else{
        listMsg += p;
        b=true;
      }
    }
    return listMsg;
  }
  return "";
})
let litigantPhoneList = computed({
  // 读取
  get(){
    if(baseInfoForm.value.litigantPhone){
      return baseInfoForm.value.litigantPhone.replace(/\s/g, '').split(',');
    }
    return []
  },
  // 修改
  set(val){
    let phoneList = "";
    for(let i=0;i<val.length;i++){
      if(i<val.length-1){
        phoneList = phoneList.concat(val[i]).concat(",");
      }else{
        phoneList = phoneList.concat(val[i]);
      }
    }
    baseInfoForm.value.litigantPhone = phoneList;
  }
})

const blur = (ev:any)=>{
  const inputElement = ev.target as HTMLInputElement;
  const currentValue = inputElement.value.trim();
  if (currentValue && !litigantPhoneList.value.includes(currentValue)) {
    litigantPhoneList.value = [...litigantPhoneList.value, currentValue];
    inputElement.value = "";
  }
}

const idTypeObj = computed(() => {
	return dictEnumValToObject(idTypeOptions.value)
})

const handleSaveModal = async () => {
	if (disabled.value && !switchStatus.value) return
	const res = await baseFormRef.value?.validate()
	if (!res) {
		okLoading.value = true
		baseInfoForm.value.caseId = props.caseId
		caseDetailsSaveLit(baseInfoForm.value)
			.then(() => {
				Message.success('操作成功')
				emits('comfirm')
				initFormModel()
			})
			.finally(() => {
				okLoading.value = false
			})
	}
	return false
}

const okClick = () => {
}

const handleCancelModal = () => {
	initFormModel()
	return true
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
	baseInfoForm.value = _.cloneDeep(modal)
	disabled.value = true
}

const initFormModel = () => {
	baseInfoForm.value = generateFormModel()
	baseFormRef.value?.resetFields()
	switchStatus.value = false
	disabled.value = false
}

const handleSelectLitigantType = (value: string | number | boolean) => {
	if (value === '1') {
		baseFormRef.value?.resetFields(['companyName', 'isPrivateEnterprise', 'registerAddress', 'businessAddress'])
	} else if (value === '2') {
		baseFormRef.value?.resetFields(['nation', 'birth', 'contactAddress', 'idAddress'])
	}
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
