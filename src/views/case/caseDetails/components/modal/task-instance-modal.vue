<template>
	<a-modal
		v-model:visible="visible"
		:closable="false"
		:width="800"
		:title="modalTitle"
		:ok-loading="okLoading"
		:on-before-ok="handleSaveModal"
		@before-open="handleBeforeOpen"
		:on-before-cancel="handleCancelModal"
		title-align="start"
		modal-class="mdt-modal"
		draggable
		@ok="okClick"
	>
		<div class="form-info">
			<a-form
				ref="baseFormRef"
        :disabled="readonly"
				:model="baseInfoForm"
				:rules="formRules"
				:label-col-props="{ span: 8 }"
				:wrapper-col-props="{ span: 16 }"
				label-align="right"
			>
				<!-- 批量模式下的案件信息显示 -->
				<div class="base-form-box mdt-form-content" v-if="isBatchMode">
					<div class="mdt-form-header">
						<div class="mdt-form-split"></div>
						<span class="mdt-form-title">案件信息</span>
					</div>
					<a-row :gutter="16">
						<a-col :span="24">
							<a-form-item :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }" field="caseIds">
								已选择案件数量：{{ ifChooseAll ? '全部案件' : `${caseIdList.length} 件` }}
							</a-form-item>
						</a-col>
					</a-row>
				</div>

				<!-- 任务创建表单 - 在创建模式或非只读处理模式下显示 -->
				<div class="base-form-box mdt-form-content">
					<div class="mdt-form-header">
						<div class="mdt-form-split"></div>
						<span class="mdt-form-title">任务信息</span>
					</div>
					<!-- 创建模式下的表单字段 -->
					<a-row :gutter="16">
						<a-col :span="12">
							<a-form-item field="assignAccountId" label="任务发起人">
                <a-input v-model="baseInfoForm.assignAccountName" disabled />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item field="assignTime" label="任务登记时间">
                <a-input v-model="baseInfoForm.assignTime" disabled />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="16">
						<a-col :span="12">

							<a-form-item  field="taskType" label="任务类型">
								<DictDataSelect
									:disabled="isProcessMode"
									v-model="baseInfoForm.taskType"
									:dict-type="DictTypeEnum.TASK_TYPE"
									placeholder="请选择任务类型"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item field="timeLimit" label="任务时限（时）">
								<a-input-number :disabled="isProcessMode" v-model="baseInfoForm.timeLimit" placeholder="请输入任务时限" :min="1" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="12">
							<a-form-item field="managerIdList" label="任务负责人">
								<DeptUserSelect :disabled="isProcessMode" v-model="baseInfoForm.managerIdList" multiple placeholder="请选择任务负责人" />
							</a-form-item>
						</a-col>
						<a-col :span="12" v-if="isProcessMode">
							<a-form-item field="deadline" label="截止时间">
								<a-input v-model="baseInfoForm.deadline" disabled />
							</a-form-item>
						</a-col>
					</a-row>
				</div>

				<!-- 处理模式下的表单字段 -->
				<div class="base-form-box mdt-form-content" v-if="isProcessMode">
					<div class="mdt-form-header">
						<div class="mdt-form-split"></div>
						<span class="mdt-form-title">任务办理 &nbsp;</span><a target="_blank" :href="dictStore.getDictRemarkByTypeAndKey(DictTypeEnum.TASK_TYPE,baseInfoForm.taskType)"><a-button size="mini" type="primary" v-if="dictStore.getDictRemarkByTypeAndKey(DictTypeEnum.TASK_TYPE,baseInfoForm.taskType)" >工作标准</a-button></a>
					</div>
					<a-row :gutter="16">
						<a-col :span="12">
							<a-form-item field="taskStatus" label="任务状态" required>
								<DictDataSelect
									v-model="baseInfoForm.taskStatus"
									:dict-type="DictTypeEnum.TASK_STATUS"
									placeholder="请选择任务状态"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="任务完成人">
                  <a-input v-model="baseInfoForm.finishAccountName" disabled />
<!--								<DeptUserSelect v-else v-model="baseInfoForm.finishAccountId" multiple placeholder="请选择任务完成人" />-->
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="16">
						<a-col :span="12">
							<a-form-item label="完成时间">
                <a-input v-model="baseInfoForm.finishTime" disabled />
<!--								<a-input :value="finishTime" placeholder="完成时间" />-->
							</a-form-item>
						</a-col>
					</a-row>
					<a-row>
						<a-col :span="24">
							<a-form-item field="remark" label="备注" :required="isRemarkRequired" 	:label-col-props="{ span: 4 }">
								<a-textarea
									v-model="baseInfoForm.remark"
									placeholder="请输入备注信息"
									:max-length="500"
									show-word-limit
									:rows="4"
								/>
							</a-form-item>
						</a-col>
					</a-row>
				</div>
			</a-form>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { createTask, processTask, getTaskInstanceById, batchCreateTask } from '@/api/arcoApi/taskWorkFlow/taskInstance'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import DeptUserSelect from '@/components/deptUserSelect/index.vue'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
import { DictTypeEnum } from '@/dict/systemManage'
import { Message } from '@arco-design/web-vue'
import { ref, watch, computed } from 'vue'
import { useDictStore, useUserStore } from '@arco/store'

import dayjs from 'dayjs'

let dictStore = useDictStore();
let userStore = useUserStore()


interface Props {
	caseId: string | number
	mode?: 'create' | 'process'
	instanceId?: string | number | null
	readonly?: boolean
	// 批量任务登记相关参数
	caseIdList?: (string | number)[]
	ifChooseAll?: boolean
	mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
}

const props = withDefaults(defineProps<Props>(), {
	caseId: '',
	mode: 'create',
	instanceId: null,
	readonly: false,
	caseIdList: () => [],
	ifChooseAll: false,
	mediateCasePageBO: () => ({})
})

const emits = defineEmits<{
	(e: 'success'): void
	(e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

// 计算属性，弹窗类型是否为处理任务
const isProcessMode = computed(() => props.mode === 'process')
// 计算属性，是否为批量模式
const isBatchMode = computed(() => {
	return (props.caseIdList && props.caseIdList.length > 0) || props.ifChooseAll
})
const modalTitle = computed(() => {
	if (isProcessMode.value) {
		return props.readonly ? '任务查看' : '任务办理'
	}
	return isBatchMode.value ? '批量任务登记' : '任务登记'
})

const isRemarkRequired = computed(() => {
	return baseInfoForm.value.taskStatus === 4
})

// 完成人姓名（当前用户）
const finishAccountName = computed(() => {
	if (baseInfoForm.value.taskStatus === 3 || baseInfoForm.value.taskStatus === 4) {
		return userStore.userInfo?.employeeName || '当前用户'
	}
	return ''
})

// 完成时间
const finishTime = computed(() => {
	if (baseInfoForm.value.taskStatus === 3 || baseInfoForm.value.taskStatus === 4) {
		return dayjs().format('YYYY-MM-DD HH:mm:ss')
	}
	return ''
})

const generateFormModel = (): REQUEST_GET_TASKINSTANCE_DATA_TYPE => {
	return {
		caseId: props.caseId,
		taskType: null,
		timeLimit: null,
		managerIdList: [],
		assignAccountId: userStore.userInfo.accountId,
		assignTime: null,
		remark: ''
	}
}

const baseInfoForm = ref<REQUEST_GET_TASKINSTANCE_DATA_TYPE>(generateFormModel())
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

// 表单验证规则
const formRules = computed(() => {
	const commonRules = {
		taskType: [{ required: true, message: '请选择任务类型' }],
		remark: [{ max: 500, message: '备注最大长度为500字符' }]
	}

	// 处理模式下的额外规则
	if (isProcessMode.value) {
		return {
			...commonRules,
			taskStatus: [{ required: true, message: '请选择任务状态' }],
			remark: isRemarkRequired.value
				? [
					{ required: true, message: '当任务状态为"已完成（异常）"时，备注为必填项' },
					{ max: 500, message: '备注最大长度为500字符' }
				]
				: [{ max: 500, message: '备注最大长度为500字符' }]
		}
	}



	return {
    ...commonRules,
    managerIdList: [
      { required: true, message: '请选择任务负责人' },
      {
        validator: (value: number[], callback: (error?: string) => void) => {
          if (!value || value.length === 0) {
            callback('至少需要指定一个任务负责人')
          } else {
            callback()
          }
        }
      }
    ]
  }
})


const handleBeforeOpen = async () => {
	//如果  instanceid存在就请求获取当前数据
	if (props.instanceId) {
		const res = await getTaskInstanceById(props.instanceId)
		baseInfoForm.value = res

		//查看时，不填充这些默认值
		if (!props.readonly){
			baseInfoForm.value.finishAccountId = userStore.userInfo.accountId
			baseInfoForm.value.finishAccountName = userStore.userInfo.employeeName || ''
			baseInfoForm.value.finishTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
		}

	} else {
		//设置一些默认数据
		baseInfoForm.value.assignAccountId = userStore.userInfo.accountId
		baseInfoForm.value.assignAccountName = userStore.userInfo.employeeName || ''
		baseInfoForm.value.assignTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
	}

}

const handleSaveModal = async () => {
	const state = await baseFormRef.value?.validate()
	if (!state) {
		okLoading.value = true
		try {
			if (isProcessMode.value) {
				// 处理任务
				const processParams: REQUEST_POST_TASKINSTANCE_PROCESS_PARAM_TYPE = {
					instanceId: baseInfoForm.value.instanceId!,
					taskStatus: baseInfoForm.value.taskStatus!,
					remark: baseInfoForm.value.remark
				}
				await processTask(processParams)
				Message.success('任务处理成功')
			} else {
				if (isBatchMode.value) {
					// 批量创建任务
					const batchCreateParams: REQUEST_POST_TASKINSTANCE_BATCHCREATE_PARAM_TYPE = {
						caseIdList: props.caseIdList!,
						taskType: baseInfoForm.value.taskType!,
						timeLimit: baseInfoForm.value.timeLimit,
						managerIdList: baseInfoForm.value.managerIdList!,
						remark: baseInfoForm.value.remark,
						assignAccountId: baseInfoForm.value.assignAccountId!,
						assignTime: baseInfoForm.value.assignTime!,
						ifChooseAll: props.ifChooseAll,
						mediateCasePageBO: props.mediateCasePageBO
					}
					await batchCreateTask(batchCreateParams)
					Message.success('批量任务登记成功')
				} else {
					// 创建任务
					const createParams: REQUEST_POST_TASKINSTANCE_CREATE_PARAM_TYPE = {
						caseId: baseInfoForm.value.caseId!,
						taskType: baseInfoForm.value.taskType,
						timeLimit: baseInfoForm.value.timeLimit,
						managerIdList: baseInfoForm.value.managerIdList!,
						assignAccountId: baseInfoForm.value.assignAccountId!,
						assignTime: baseInfoForm.value.assignTime
					}
					await createTask(createParams)
					Message.success('任务创建成功')
				}
			}
			emits('success')
			initFormModel()
			return true
		} catch (error) {
			console.error(isProcessMode.value ? '处理任务失败:' : '创建任务失败:', error)
			Message.error(isProcessMode.value ? '任务处理失败' : '任务创建失败')
		} finally {
			okLoading.value = false
		}
	}
	return false
}

const okClick = () => {
}

const handleCancelModal = () => {
	initFormModel()
	return true
}

watch(visible, () => {
	if (visible.value) {
		initFormModel()
	}
})

const initFormModel = () => {
	baseInfoForm.value = generateFormModel()
	baseFormRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.base-form-box {
	.arco-form-item {
		margin-bottom: 16px;
	}
}

.form-help-text {
	margin-left: 8px;
	color: #86909c;
	font-size: 12px;
}

.task-info-section {
	margin-bottom: 24px;
}

.section-title {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 16px;
	color: #1d2129;
}

.form-tooltip-icon {
	margin-left: 4px;
	color: #86909c;
}
</style>
