<template>
  <a-modal
    v-model:visible="visible"
    :width="width"
    :title="modalTitle"
    :footer="!showQrcode"
    :body-style="{ height: '30vh' }"
    :on-before-cancel="initFormModel"
    title-align="center"
    modal-class="mdt-modal"
    draggable
  >
    <div class="form-info">
      <a-row v-if="showQrcode">
        <a-col :span="8">
          <a-image :src="qrcodeImg" class="sys-logo" width="140" height="140"></a-image>
        </a-col>
        <a-col :span="16">
          <div class="sign-status-wrapper">
            签名情况：（
            <span style="color: #1dd779">
              {{ signManList.filter((e) => e.signStatus !== MAN_SIGN_STATE.WAIT).length }}
            </span>
            / {{ signManList.length }} ）
            <a-button type="text" style="margin-left: 20px" @click="getSignDetails(id)"> 刷新 </a-button>
            <div class="scroll-wrap">
              <div v-for="item in signManList" :key="item.litigantId">
                <icon-check-circle-fill v-if="item.signStatus === MAN_SIGN_STATE.ALREADY" style="color: #3a9189" />
                <icon-exclamation-circle-fill v-if="item.signStatus === MAN_SIGN_STATE.WAIT" style="color: #f3b448" />
                {{ item.litigantName }}<span class="tip-text">（{{ signStatusText(item) }}）</span>
              </div>
            </div>
            <a-button :loading="okLoading" type="primary" @click="handleSignTerminate">终止签名 </a-button>
          </div>
        </a-col>
      </a-row>
      <a-form
        v-else
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="formRules"
        :label-col-props="{ span: 8 }"
        :wrapper-col-props="{ span: 14 }"
        label-align="right"
      >
        <a-form-item field="identityTypeList" label="请选择当事人身份：">
					<DictDataSelect v-model="baseInfoForm.identityTypeList" :dict-type="DictTypeEnum.litigant_identity_type" multiple @change="handleLitigantOptions" />
        </a-form-item>
        <a-form-item field="litigantIdList" label="请选择当事人：">
          <a-select
            v-model="baseInfoForm.litigantIdList"
            :options="litigantOptions"
            placeholder="请选择"
            multiple
            allow-clear
          />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-space :size="10">
        <a-button :loading="okLoading" type="primary" @click="createSignQrCode">生成二维码开始签名 </a-button>
        <a-button :loading="smsLoging" @click="sendSignSms">发送签名短信 </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { sendSignSmsCode, getSignQrCode, flushSignRecord } from '@/api/arcoApi/caseManage/document'
import { newCreateSignQrCode, terminateSign } from '@/api/arcoApi/caseManage/document'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { caseDetailsQueryLit } from '@/api/arcoApi/caseManage/caseDetails'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { Message, Modal } from '@arco-design/web-vue'
import { computed, inject, ref, watch } from 'vue'
import { getImgFilePath } from '@/utils/proto'
import dict from '@/dict/caseManage'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'

const caseId = inject<string | number>('caseId')

interface Props {
  width?: string | number
  id: string | number
  filePath?: string
}

// 人员签名情况
enum MAN_SIGN_STATE {
  WAIT = 1, // 等待签名
  REJECT = 2, // 拒绝签名
  ALREADY = 3 // 已签名
}

const props = withDefaults(defineProps<Props>(), {
  width: '520px',
  id: ''
})

const emits = defineEmits<{
  (e: 'endSign'): void
  (e: 'qrcode'): void
  (e: 'sms'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    identityTypeList: [],
    caseId: caseId ?? '',
    litigantIdList: [],
    instrumentsId: '',
    filePath: ''
  }
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_SGIN_QECODE_TYPE>(generateFormModel())
const litigantData = ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>([])
const signManList = ref<REQUEST_CASEDETAILS_SGIN_MAN_DATA_TYPE[]>([])
const litigantOptions = ref<SelectOptionData[]>([])
const baseFormRef = ref<FormInstance>()
const showQrcode = ref(false)
const smsLoging = ref(false)
const okLoading = ref(false)
const isBase64 = ref(false)
const qrcodeUrl = ref('')

const formRules = ref({
  identityTypeList: [{ required: true, message: '请选择当事人身份', trigger: 'blur' }],
  litigantIdList: [{ required: true, message: '请选择当事人', trigger: 'blur' }]
})

const modalTitle = computed(() => {
  return '发起电子签名'
})

const qrcodeImg = computed(() => {
  let previewUrl = isBase64.value ? qrcodeUrl.value : getImgFilePath(qrcodeUrl.value)
  return previewUrl
})

const signStatusText = function (item: REQUEST_CASEDETAILS_SGIN_MAN_DATA_TYPE) {
  const map = {
    [MAN_SIGN_STATE.WAIT]: '待签名',
    [MAN_SIGN_STATE.REJECT]: '拒绝签名',
    [MAN_SIGN_STATE.ALREADY]: '已签名'
  }
  return map[item.signStatus]
}

const handleLitigantOptions = (types: (string | number)[]) => {
  let filterData = litigantData.value.filter((item) => types.includes(item.identityType))
  litigantOptions.value = filterData.map((item) => ({ label: item.litigantName, value: item.litigantId }))

  let selectIds: (string | number)[] = []
  if (baseInfoForm.value.litigantIdList.length > 0) {
    baseInfoForm.value.litigantIdList.forEach((id) => {
      if (litigantOptions.value.find((item) => item.value === id)) selectIds.push(id)
    })
  }
  baseInfoForm.value.litigantIdList = selectIds
}

const createSignQrCode = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    okLoading.value = true
    baseInfoForm.value.instrumentsId = props.id
    baseInfoForm.value.filePath = props.filePath || ''
    newCreateSignQrCode(baseInfoForm.value)
      .then((res) => {
        if (res) {
          Message.success('二维码创建成功')
          getSignDetails(props.id)
          emits('qrcode')
        }
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const handleSignTerminate = () => {
  Modal.info({
    title: '签名终止',
    titleAlign: 'start',
    content: '确定终止该文书的签名？',
    closable: false,
    hideCancel: false,
    onOk: async () => {
      try {
        let res = await terminateSign(props.id)
        if (res) {
          Message.success('操作成功')
          emits('endSign')
          initFormModel()
        }
      } catch (error) {
        Message.error(error as string)
      }
    }
  })
}

const sendSignSms = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    smsLoging.value = true
    sendSignSmsCode(baseInfoForm.value)
      .then(() => {
        Message.success('短信发送成功')
        emits('sms')
      })
      .finally(() => {
        smsLoging.value = false
      })
  }
  return false
}

const getModalSignQrCode = (filePath: string) => {
  getSignQrCode({ filePath }).then((res) => {
    if (res) {
      qrcodeUrl.value = res.base64QrCode
      showQrcode.value = true
      isBase64.value = true
      getSignDetails(props.id)
    }
  })
}

const getSignDetails = (id: string | number) => {
  flushSignRecord(id).then((res) => {
    if (res) {
      signManList.value = res.signerSignStatuses
      qrcodeUrl.value = res.qrCodeFilePath
      showQrcode.value = true
    }
  })
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseFormRef.value?.resetFields()

  baseInfoForm.value = generateFormModel()
  showQrcode.value = false
  visible.value = false
  qrcodeUrl.value = ''
}

const getLitigantList = () => {
  if (!caseId) return
  caseDetailsQueryLit(caseId).then((res) => {
    litigantData.value = res
  })
}

watch(
  () => visible.value,
  (val) => val && getLitigantList()
)

defineExpose({ setFormModel, initFormModel, getSignDetails })
</script>
<style lang="scss" scoped>
.sign-status-wrapper {
  margin-left: 30px;
  .scroll-wrap {
    margin-top: 5px;
    height: 115px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
