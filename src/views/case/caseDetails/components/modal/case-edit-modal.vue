<template>
  <a-modal
    v-model:visible="visible" :closable="false" :width="width" :title="title" :ok-loading="okLoading"
    :body-style="{ height: '52vh' }" modal-class="mdt-modal" title-align="start" draggable @ok="handleSaveModal"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef" :model="baseInfoForm" :label-col-props="{ span: 9 }" :wrapper-col-props="{ span: 15 }"
        label-align="left"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">基础信息</span>
          </div>
          <a-row :gutter="16">
            <a-col v-for="field in customerFields" :key="field.prop" :span="12">
              <a-form-item v-bind="field.aFormItemAttrs">
                <template #label>
                  <a-tooltip
                    v-if="field.aFormItemAttrs.label && field.aFormItemAttrs.label.length > 8"
                    :content="field.aFormItemAttrs.label"
                  >
                    <span class="mdt-form-item-label">
                      <span v-if="field.aFormItemAttrs.isApprovalValidated == 1" style="color: red">*</span>
                      {{ field.aFormItemAttrs.label }}:
                    </span>
                  </a-tooltip>
                  <span v-else-if="field.aFormItemAttrs.label" class="mdt-form-item-label">
                    <span v-if="field.aFormItemAttrs.isApprovalValidated == 1" style="color: red">*</span>
                    {{ field.aFormItemAttrs.label }}:
                  </span>
                </template>
                <component :is="field.component" v-model="baseInfoForm[field.prop]" v-bind="field.componentAttrs">
                  <template v-if="field.component === 'ASelect'" #option="{ data }">
                    <a-tooltip v-if="data.value && data.value.length > 12" :content="data.value">
                      <span class="mdt-form-option-value"> {{ data.value }}</span>
                    </a-tooltip>
                  </template>
                </component>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import type { DescData } from '@arco-design/web-vue/es/descriptions/interface'
  import type { ArcoFormGlobalComponentsType } from './arco-components-type'
  import type { FormItemInstance } from '@arco-design/web-vue/es/form'
  import type { FormInstance } from '@arco-design/web-vue/es/form'

  import { caseDetailsCaseEdit } from '@/api/arcoApi/caseManage/caseDetails'
  // import { ArcoFormGlobalComponents } from './arco-components'
  import { componentMapObject } from '@/dict/businessConf'
  import { nextTick, ref, watch, inject } from 'vue'
  import { Message } from '@arco-design/web-vue'

  import dayjs from 'dayjs'
  import _ from 'lodash'
	import { hasRule } from '@/directives/rule.ts'

  const caseId = inject < string | number > ('caseId') || ''

  interface Props {
    renderFields: REQUEST_GET_FIELD_LIST_TYPE[]
    moduleId?: string | number
    renderData?: DescData[]
    width?: string | number
    dataId?: string
    title: string
  }

  const props = withDefaults(defineProps < Props > (), {
    renderFields: () => [],
    renderData: () => [],
    width: '700px',
    moduleId: '',
    dataId: '',
    title: ''
  })

  const emits = defineEmits < {
  (e: 'comfirm', dataId: string | number): void
    (e: 'cancel'): void
}> ()

  const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

  interface CUTOM_FIELD_TYPE {
    componentAttrs: ArcoFormGlobalComponentsType[CUTOM_FIELD_TYPE['component']]['$props']
    component: keyof ArcoFormGlobalComponentsType
    aFormItemAttrs: FormItemInstance['$props']
    prop: string
  }

  const baseFormRef = ref < FormInstance > ()
  const okLoading = ref < boolean > (false)
  const customId = ref < string > ('')

  const customerFields = ref < CUTOM_FIELD_TYPE[] > ([])
  const baseInfoForm = ref < OBJ_KEY_STR_ANY_TYPE > ({})
  const customFieldsMapObj = ref < OBJ_KEY_STR_STR_TYPE > ({})

  const handleCustomerField = (fromData: OBJ_KEY_STR_ANY_TYPE) => {
    let saveParam: REQUEST_CASEDETAILS_CUSTOM_SAVE_PARAM_TYPE = {
      tmplModuleId: props.moduleId,
      dataId: props.dataId,
      data: {},
      caseId
    }
    if (customerFields.value && customerFields.value.length) {
      for (let index = 0; index < customerFields.value.length; index++) {
        const field = customerFields.value[index]
        if (Object.prototype.hasOwnProperty.call(field, 'prop')) {
          const _key = customFieldsMapObj.value[field.prop]
          const _value = fromData[field.prop]
          if (Array.isArray(_value)) {
            saveParam.data[_key] = _value.join(',')
          } else {
            saveParam.data[_key] = _value
          }
        }
      }
    }
    return saveParam
  }

  const handleSaveModal = async () => {
    const res = await baseFormRef.value?.validate()
    if (!res && baseInfoForm.value) {
      okLoading.value = true
      let param = handleCustomerField(baseInfoForm.value)
      caseDetailsCaseEdit(param)
        .then((res) => {
          console.log(res)
          Message.success('操作成功')
          emits('comfirm', props.moduleId)
        })
        .finally(() => {
          okLoading.value = false
        })
    }
  }

  const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
    baseInfoForm.value.data = _.cloneDeep(modal)
  }

  const initFormModel = () => {
    baseFormRef.value?.resetFields()
  }

  const initCustomFields = () => {
    if (props.dataId && customId.value === props.dataId) return
    baseInfoForm.value = {}
    customerFields.value = []
    customId.value = props.dataId
    nextTick(() => {
      props.renderFields.forEach((item, index) => {
        let { componentRelation, fieldValues, fieldTitle,readOnlyRoles } = item
        let renderItem = props.renderData.find((r) => r.label === fieldTitle)
        let fieldValue = (renderItem?.value as string) || ''
        // componentMapObject
        let formFieldItem: CUTOM_FIELD_TYPE = {
          component: componentRelation ? componentMapObject[componentRelation] || 'AInput' : 'AInput',
          aFormItemAttrs:
          {
            label: item.fieldTitle,
            field: `customField${index}`,
            isApprovalValidated: item.isApprovalValidated

          },
          componentAttrs: { allowClear: true },
          prop: `customField${index}`
        }
        switch (componentRelation) {
          case 'ASelectMultiple':
            formFieldItem.componentAttrs['modal'] = fieldValue ? fieldValue.split(',') : []
            formFieldItem.componentAttrs['multiple'] = true
            formFieldItem.componentAttrs['maxTagCount'] = 1
            break
          case 'ADatePicker':
            // 过滤非时间字符串
            formFieldItem.componentAttrs['modal'] = dayjs(fieldValue).isValid() ? fieldValue : ''
            break
          case 'ADateTimePicker':
            formFieldItem.componentAttrs['modal'] = dayjs(fieldValue).isValid() ? fieldValue : ''
            formFieldItem.componentAttrs['showTime'] = true
            break
          case 'AInputNumber':
            if (fieldValue && !isNaN(Number(fieldValue))) {
              formFieldItem.componentAttrs['modal'] = Number(fieldValue)
            } else {
              formFieldItem.componentAttrs['modal'] = 0
            }
            break
          case 'ATextarea':
            formFieldItem.componentAttrs['modal'] = fieldValue ? fieldValue : ''
            formFieldItem.componentAttrs['autoSize'] = { minRows: 5 }
            break
          default:
            formFieldItem.componentAttrs['modal'] = fieldValue || ''
            break
        }
        // json转换
        if (fieldValues) {
          let _fieldValues = JSON.parse(fieldValues)
          if (_fieldValues && Object.prototype.toString.call(_fieldValues) === '[object Array]') {
            formFieldItem.componentAttrs['options'] = _fieldValues
          }
        }
        baseInfoForm.value[`customField${index}`] = formFieldItem.componentAttrs.modal
        customFieldsMapObj.value[`customField${index}`] = item.fieldTitle as string


				//查看是否是只读角色
				if(hasRule(readOnlyRoles)){
					formFieldItem.componentAttrs['disabled'] = true
				}

        customerFields.value.push(formFieldItem)
      })
    })
  }

  watch(
    () => visible.value,
    (value) => {
      if (value && props.renderFields) {
        if (!props.dataId || customId.value !== props.dataId) initFormModel()
        initCustomFields()
      }
    }
  )

  defineExpose({ setFormModel })
</script>

<style lang="scss" scoped></style>
