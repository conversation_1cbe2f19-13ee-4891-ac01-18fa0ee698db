<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="formRules"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">制作方式：</span>
          </div>
          <a-form-item field="createMethod" label="生成方式">
            <a-radio-group v-model="createMethod">
              <a-radio v-for="item in createMethodsOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="createMethod === '1'" label="文书模板">
            <a-card class="mdt-card-common">
              <a-table
                v-model:selected-keys="tableSelectedKeys"
                row-key="docTmplId"
                :row-selection="rowSelection"
                :pagination="false"
                :columns="columns"
                :scroll="{ maxHeight: '30vh' }"
                :data="renderData"
                :bordered="false"
              >
              </a-table>
            </a-card>
          </a-form-item>
          <a-form-item v-if="createMethod==='2'" field="instrumentsFile" label="文书：">
            <a-space :size="20">
              <a-input v-model="baseInfoForm.documentFileName" style="width: 300px" readonly size="mini" />
              <!-- .doc,.docx -->
              <a-upload :auto-upload="false" :show-file-list="false" action="/" accept=".pdf" @change="onFileChange">
                <template #upload-button>
                  <a-link :hoverable="false"> 手动上传</a-link>
                </template>
              </a-upload>
            </a-space>
          </a-form-item>

					<a-form-item v-if="createMethod==='3'" field="selectedRecords" label="选择录音文件：">
						<a-space :size="20">
							<a-select
								v-model="selectedRecordsDisplay"
								:options="selectedRecordsOptions"
								placeholder="请选择录音文件"
                multiple
								readonly
								style="width: 300px"
							/>
							<a-link :hoverable="false" @click="openRecordSelectModal"> 选择</a-link>
						</a-space>
					</a-form-item>

        </div>
      </a-form>
    </div>

    <!-- 录音选择弹窗 -->
    <a-modal
      v-model:visible="recordSelectVisible"
      title="选择录音文件"
      :width="1200"
      :footer="false"
      :closable="true"
    >
      <CallRecordTable
        ref="callRecordTableRef"
        :multi-select="true"
        @multi-select-confirm="handleRecordSelectConfirm"
        @multi-select-cancel="handleRecordSelectCancel"
      />
    </a-modal>
  </a-modal>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'

import { batchGenerateSingleDoc, findSingleByCaseIds } from '@/api/arcoApi/businessConf/docTemplateManage'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { addDocument, generateDocumentByRecord } from '@/api/arcoApi/caseManage/document'
import { computed, inject, reactive, ref, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import _ from 'lodash'
import CallRecordTable from '../table/call-record-table.vue'

const caseId = inject<string | number>('caseId')

interface Props {
  width?: string | number
  title: string
}

withDefaults(defineProps<Props>(), {
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    instrumentsLitigantIdList: [],
    instrumentsIdentityType: '',
    instrumentsFile: null,
    instrumentsTmplId: '',
    documentFileName: '',
    caseId: '',
    selectedRecords: []
  }
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_DOC_SAVE_PARAM_TYPE>(generateFormModel())
const createMethodsOptions = ref<SelectOptionData[]>(dict.createMethodsOptions)
const renderData = ref<REQUEST_GET_CREATEDOC_TEMPLATE_LIST_TYPE[]>([])
const tableSelectedKeys = ref<(string | number)[]>([])

const baseFormRef = ref<FormInstance>()
const callRecordTableRef = ref()
const okLoading = ref<boolean>(false)
const createMethod = ref<string>('1')

// 录音选择相关状态
const recordSelectVisible = ref(false)
const selectedRecords = ref<REQUEST_GET_CALL_LIST_DATA_TYPE[]>([])
const selectedRecordsDisplay = ref<(string | number)[]>([])

// 录音选择选项
const selectedRecordsOptions = computed(() => {
  return selectedRecords.value.map(record => ({
    label: `${record.calleeName || '未知'} - ${record.startTime} - ${record.timeLength || '--'}秒`,
    value: record.recordId
  }))
})

const formRules = ref({
  instrumentsIdentityType: [{ required: true, message: '请选择当事人身份', trigger: 'blur' }],
  instrumentsLitigantIdList: [{ required: true, message: '请选择当事人', trigger: 'blur' }],
  instrumentsFile: [{ required: true, message: '请选择文书上传', trigger: 'blur' }],
  selectedRecords: [{ required: true, message: '请选择录音文件', trigger: 'blur' }]
})

const columns = computed<TableColumnData[]>(() => [
  { width: 100, tooltip: true, ellipsis: true, title: '文书模板名称', dataIndex: 'docTmplTitle' },
  { width: 100, tooltip: true, ellipsis: true, title: '关联模板', dataIndex: 'tmplTitle' }
])

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const handleSaveModal = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    okLoading.value = true
    // 生成方式-模板生成
    if (createMethod.value === '1') {
      let param = { caseIdList: caseId ? [caseId] : [], docTmplId: tableSelectedKeys.value }
      batchGenerateSingleDoc(param)
        .then(() => {
          Message.success('操作成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    }
    // 生成方式-本地上传
    if (createMethod.value === '2') {
      baseInfoForm.value.caseId = caseId || ''
      let param = new FormData()
      for (const key in baseInfoForm.value) {
        param.append(key, baseInfoForm.value[key])
      }
      addDocument(param)
        .then(() => {
          Message.success('操作成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    }
    // 生成方式-录音生成
    if (createMethod.value === '3') {
      const recordIdList = selectedRecords.value.map(record => record.recordId)
      const param: AI_DOCUMENT_GENERATE_BY_RECORD_PARAM_TYPE = {
        caseId: caseId || '',
        recordIdList: recordIdList
      }
      generateDocumentByRecord(param)
        .then((instrumentsId) => {
          Message.success(`调解笔录生成生成请求已提交，`)
          emits('comfirm')
          initFormModel()
        })
        .catch((error) => {
          Message.error('调解笔录生成生成请求失败: ' + error.message)
        })
        .finally(() => {
          okLoading.value = false
        })
    }
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const onFileChange = (fileList: FileItem[]) => {
  if (fileList.length > 1) fileList.splice(0, 1)
  baseInfoForm.value.documentFileName = fileList[0].name
  baseInfoForm.value.instrumentsFile = fileList[0].file ? fileList[0].file : null
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
  tableSelectedKeys.value = []
  createMethod.value = '1'
  selectedRecords.value = []
  selectedRecordsDisplay.value = []
}

const getCreateTemplateList = () => {
  if (!caseId) return
  findSingleByCaseIds({ caseIdList: [caseId] }).then((res) => {
    renderData.value = res
  })
}

// 录音选择相关方法
const openRecordSelectModal = () => {
  recordSelectVisible.value = true
  // 初始化录音表格数据
  if (callRecordTableRef.value && caseId) {
    callRecordTableRef.value.getTableData(caseId)
  }
}

const handleRecordSelectConfirm = (records: REQUEST_GET_CALL_LIST_DATA_TYPE[]) => {
  selectedRecords.value = records
  selectedRecordsDisplay.value = records.map(record => record.recordId)
  baseInfoForm.value.selectedRecords = records
  recordSelectVisible.value = false
  Message.success(`已选择 ${records.length} 条录音记录`)
}

const handleRecordSelectCancel = () => {
  recordSelectVisible.value = false
}

watch(
  () => visible.value,
  (val) => val && getCreateTemplateList()
)

defineExpose({ setFormModel, initFormModel })
</script>
<style lang="scss" scoped></style>
