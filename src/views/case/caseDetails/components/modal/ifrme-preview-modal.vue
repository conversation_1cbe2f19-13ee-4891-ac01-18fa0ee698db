<template>
  <a-modal
    ref="modalRef"
    v-model:visible="visible"
    :closable="closable"
    :draggable="draggable"
    :width="width"
    :title="title"
    :footer="showFooter"
    :ok-loading="okLoading"
    :mask-closable="maskClosable"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="preview-content">
      <iframe :src="viewerUrl" frameborder="0" allow="camera; microphonel" class="preview-iframe-window"></iframe>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  width?: string | number
  maskClosable?: boolean
  showFooter?: boolean
  draggable?: boolean
  closable?: boolean
  viewerUrl?: string
  title?: string
}

withDefaults(defineProps<Props>(), {
  maskClosable: true,
  showFooter: false,
  closable: false,
  draggable: false,
  viewerUrl: '',
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const okLoading = ref<boolean>(false)
</script>

<style lang="scss">
.preview-iframe-window {
  width: 100%;
  height: 65vh;
  overflow: scroll;
}
</style>
