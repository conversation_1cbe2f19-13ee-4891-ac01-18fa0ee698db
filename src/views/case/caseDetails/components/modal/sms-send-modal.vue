<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="formRules"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">联系人信息：</span>
          </div>
          <a-form-item field="litigantIdentityType" label="请选择当事人身份：">
						<DictDataSelect v-model="baseInfoForm.litigantIdentityType" :dict-type="DictTypeEnum.litigant_identity_type"
														multiple
														@change="handleLitigantOptions"
						/>
          </a-form-item>
          <a-form-item field="litigantIds" label="请选择当事人：">
            <a-select
              v-model="baseInfoForm.litigantIds"
              :options="litigantOptions"
              placeholder="请选择"
              allow-clear
              multiple
            />
          </a-form-item>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">短信信息：</span>
          </div>
          <a-form-item field="templateId" label="请选择短信模板：">
            <a-select
              v-model="baseInfoForm.templateId"
              :options="smsTemplateOptions"
              placeholder="请选择"
              allow-clear
            />
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { getSmsSendTemplate, sendBusinessSms } from '@/api/arcoApi/mediateManage/jobLog/smsSendRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import { caseDetailsQueryLit } from '@/api/arcoApi/caseManage/caseDetails'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/caseManage'
import { inject, ref } from 'vue'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'

const caseId = inject<string | number>('caseId')

interface Props {
  recordId: string | number
  width?: string | number
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '700px',
  recordId: '',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    litigantIdentityType: [],
    caseId: caseId ?? '',
    litigantIds: [],
    templateId: '',
    recordId: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_SMS_SEND_PARAM_TYPE>(generateFormModel())
const litigantData = ref<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]>([])

const smsTemplateOptions = ref<SelectOptionData[]>([])
const litigantOptions = ref<SelectOptionData[]>([])

const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const formRules = ref({
  litigantIdentityType: [{ required: true, message: '请选择当事人身份', trigger: 'blur' }],
  litigantIds: [{ required: true, message: '请选择当事人', trigger: 'blur' }],
  templateId: [{ required: true, message: '请选择短信模板', trigger: 'blur' }]
})

const handleLitigantOptions = (types: (string | number)[]) => {
	console.log('types', types)
  let filterData = litigantData.value.filter((item) => types.includes(Number(item.identityType)))
  litigantOptions.value = filterData.map((item) => ({ label: item.litigantName, value: item.litigantId }))

  let selectIds: (string | number)[] = []
  if (baseInfoForm.value.litigantIds.length > 0) {
    baseInfoForm.value.litigantIds.forEach((id) => {
      if (litigantOptions.value.find((item) => item.value === id)) selectIds.push(id)
    })
  }

  baseInfoForm.value.litigantIds = selectIds
}

const handleSaveModal = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    baseInfoForm.value.recordId = props.recordId
    okLoading.value = true
    sendBusinessSms(baseInfoForm.value)
      .then(() => {
        Message.success('短信发送成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

const getLitigantList = () => {
  if (!caseId) return
  caseDetailsQueryLit(caseId).then((res) => {
    litigantData.value = res
  })
}

const getSmsTemplteList = () => {
  getSmsSendTemplate('sendMeeting').then((res) => {
    smsTemplateOptions.value = res.map((item) => {
      return { label: item.templateName, value: item.smsTemplateId }
    })
  })
}

getSmsTemplteList()
getLitigantList()

defineExpose({ setFormModel, initFormModel })
</script>
<style lang="scss" scoped></style>
