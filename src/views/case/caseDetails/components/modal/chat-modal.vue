<template>
  <a-modal
    ref="modalRef"
    v-model:visible="visible"
    :width="width"
    :title="title"
    :mask-closable="maskClosable"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="chat-box mdt-common-scrollbar">
      <div class="chat-content">
        <div v-for="(itemData, index) in chatList" :key="index" class="chat-item">
          <a-space :size="4" direction="vertical" fill>
            <a-typography-paragraph>
              {{ itemData.userName }}
              <a-typography-text type="warning">[{{ itemData.role }}]</a-typography-text>
            </a-typography-paragraph>
            <a-typography-text bold>{{ itemData.message }}</a-typography-text>
            <div class="chat-item-footer">
              <div class="chat-item-time">
                <a-typography-text type="secondary">
                  {{ itemData.time }}
                </a-typography-text>
              </div>
              <div class="chat-item-actions">
                <div class="chat-item-actions-item">
                  <icon-command />
                </div>
                <div class="chat-item-actions-item chat-item-actions-collect">
                  <icon-star />
                </div>
              </div>
            </div>
          </a-space>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  width?: string | number
  maskClosable?: boolean
  title?: string
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  maskClosable: true,
  draggable: false,
  width: '600px',
  title: '',
  text: ''
})

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

interface ChatData {
  role: string
  message: string
  chatTime: {
    hour: number
    nano: number
    year: number
    month: string
    minute: number
    second: number
    dayOfWeek: string
    dayOfYear: number
    chronology: { id: string; calendarType: string }
    dayOfMonth: number
    monthValue: number
  }
  userName: string
}

const chatList = computed(() => {
  let chatArr = props.text ? (JSON.parse(props.text) as ChatData[]) : []
  if (Object.prototype.toString.call(chatArr) === '[object Array]') {
    return chatArr.map((item) => ({
      userName: item.userName,
      role: item.role,
      message: item.message,
      time: `${item.chatTime.year}/${item.chatTime.monthValue}/${item.chatTime.dayOfMonth} ${item.chatTime.hour}:${item.chatTime.minute}:${item.chatTime.second}`
    }))
  } else {
    return []
  }
})
</script>

<style lang="scss">
.chat-box {
  height: 40vh;
  padding: 10px;
  overflow-y: scroll;
}
.chat-item {
  padding: 8px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 2px;

  &-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-actions {
    display: flex;
    opacity: 0;

    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-right: 4px;
      color: var(--color-text-3);
      font-size: 14px;
      border-radius: 50%;
      cursor: pointer;

      &:hover {
        background-color: rgb(var(--gray-3));
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &-collected {
    .chat-item-actions-collect {
      color: rgb(var(--gold-6));
    }
  }

  &:hover {
    background-color: rgb(var(--gray-2));

    .chat-item-actions {
      opacity: 1;
    }
  }
}
</style>
