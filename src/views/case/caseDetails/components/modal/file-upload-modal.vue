<template>
  <a-modal
    ref="modalRef"
    v-model:visible="visible"
    :closable="true"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    @ok="okClick"
  >
    <div class="upload-form">
      <div class="upload-form-tip"><span>将所需文件上传至项目中</span></div>
      <a-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        :action="uploadURL"
        :headers="uploadHeader"
        :auto-upload="false"
        :data="{ caseId: caseId ?? '' }"
        accept=".png,.jpg,.jpeg,.gif,.svg,.dsg,.doc,.docx,.pdf"
        draggable
        multiple
        @error="handleUploadError"
        @change="handleUploadChange"
        @success="handleUploadSuccess"
        @progress="handleUploadProgress"
      >
        <template #upload-button>
          <div class="upload-form-box">
            <div class="upload-form-icon animate-wave">
              <div class="upload-icon-do"><icon-cloud-download :size="24" /></div>
              <div class="do-box do-w1"></div>
              <div class="do-box do-w2"></div>
              <div class="do-box do-w3"></div>
              <div class="do-box do-w4"></div>
            </div>
            <div class="upload-form-text">
              <span>
                <span style="color: #3a9189">点击</span>或将文件<span style="color: #3a9189">拖拽</span>到这里上传
              </span>
            </div>
            <div class="upload-form-text">
              <span style="color: var(--color-text-4)">支持.png、.jpg、.jpeg、.gif、.svg、.dsg...</span>
            </div>
          </div>
        </template>
      </a-upload>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'
import type { UploadInstance } from '@arco-design/web-vue/es/upload'

import { Message } from '@arco-design/web-vue'
import { TokenKeyOfHeader } from '@/settings'
import { getToken } from '@/utils/auth'
import { baseURL } from '@/api/base'
import { ref, inject } from 'vue'

const caseId = inject<string | number>('caseId')

interface Props {
  width?: string | number
  title: string
}

withDefaults(defineProps<Props>(), {
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
  (e: 'single'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const uploadURL = ref<string>(`${baseURL}/api/mis/mediate/file/save`)
const uploadHeader = ref({ [TokenKeyOfHeader]: getToken() })
const uploadRef = ref<UploadInstance>()
const batchUpload = ref<boolean>(false)
const okLoading = ref<boolean>(false)
const fileList = ref<FileItem[]>([])

const handleSaveModal = async () => {
  okLoading.value = true
  batchUpload.value = true
  let files = fileList.value.filter((item) => item.status && ['init', 'error'].includes(item.status))
  if (files && files.length) {
    await files.forEach((item) => {
      if (item.status === 'error' && item.file) item.status = 'init'
    })
    await uploadRef.value?.submit()
    return false
  } else {
    return true
  }
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const initFormModel = () => {
  fileList.value = []
}

const handleUploadSuccess = (file: FileItem) => {
  let files = fileList.value.filter((item) => item.status && ['init', 'error'].includes(item.status))
  if (files.length === 0) {
    if (batchUpload.value) {
      batchUpload.value = false
      okLoading.value = false
      emits('comfirm')
      initFormModel()
      Message.success('上传成功！')
    } else {
      Message.success('上传成功！')
      emits('single')
    }
  }
}

const handleUploadError = (file: FileItem) => {
  console.log('error', file)
  okLoading.value = false
}

const handleUploadChange = (fList: FileItem[], file: FileItem) => {
  console.log('change fList', fList)
  console.log('change file', file)

  if (okLoading.value && fileList.value.length < 1) {
    okLoading.value = false
    return
  }
  let targetFile = fileList.value.find((item) => item.uid === file.uid)
  if (okLoading.value && !targetFile) {
    uploadRef.value?.abort(file)
  }
}

const handleUploadProgress = (file: FileItem, ev?: ProgressEvent) => {
  console.log('Progress file', file)
  console.log('Progress ev', ev)
}

defineExpose({ initFormModel })
</script>

<style lang="scss">
.upload-form {
  .arco-upload-list {
    margin-top: 10px;
    max-height: 200px;
    overflow-y: scroll;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 8px;
      height: 10px;
      overflow: auto;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(144, 147, 153, 0.5);
    }
  }
  .upload-form-tip {
    height: 20px;
    line-height: 20px;
    color: rgb(71, 84, 103);
    font-size: 14px;
    text-align: left;
    font-family: AlibabaPuHui-regular;
  }
  .upload-form-box {
    margin-top: 21px;
    background-color: rgba(253, 253, 253, 1);
    color: rgba(16, 16, 16, 1);
    border: 1px solid rgba(230, 230, 230, 1);
    height: 138px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .upload-form-icon {
      height: 70px;
      width: 70px;
      margin: 0 auto;
      line-height: 70px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .upload-icon-do {
        height: 40px;
        width: 40px;
        z-index: 2;
        color: #8c8d8d;
        border-radius: 50%;
        line-height: 50px;
        text-align: center;
      }
    }
  }
}

.animate-wave .do-box {
  background: #ececec 100%;
  position: absolute;
  border-radius: 50%;
  animation: opac 4s infinite;
  z-index: 1;
}

.animate-wave .do-w2 {
  animation-delay: 1s;
}

.animate-wave .do-w3 {
  animation-delay: 2s;
}

.animate-wave .do-w4 {
  animation-delay: 3s;
}

@keyframes opac {
  from {
    opacity: 1;
    width: 0;
    height: 0;
    top: 50%;
    left: 50%;
  }

  to {
    opacity: 0;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
}
</style>
