<template>
<!--	46是card的header的默认高度-->
  <a-card title="辅助任务状态" :body-style="{height: (listHeight-46) + 'px' }">
    <template #extra>
      <a-button v-auth="['caseRecordSave']" type="primary" @click="handleCreateTask">登记</a-button>
    </template>
    <a-table
			:scroll="{ y:'100%' }"
			:scrollbar="true"
      size="mini"
      :table-layout-fixed="true"
      row-key="instanceId"
      :loading="loading"
      :columns="columns"
      :data="taskInstanceList"
      :pagination="false"
      :bordered="false"
    >
      <template #taskType="{ record }">
        <span>{{  dictStore.getDictTagByTypeAndKey(DictTypeEnum.TASK_TYPE, record.taskType) }}</span>
      </template>
      <template #taskStatus="{ record }">
				<span>{{  dictStore.getDictTagByTypeAndKey(DictTypeEnum.TASK_STATUS, record.taskStatus) }}</span>
      </template>
      <template #managerNames="{ record }">
        {{ record.managerNames }}
      </template>
      <template #operations="{ record }">
        <a-button
          v-if="record.taskStatus === 1 || record.taskStatus === 2"
          type="text"
          size="small"
          @click="handleProcessTask(record)"
        >
          办理
        </a-button>
        <a-button
          v-else
          type="text"
          size="small"
          @click="handleViewTask(record)"
        >
          查看
        </a-button>
      </template>
    </a-table>
  </a-card>

  <TaskInstanceModal
    v-model:visible="createModalVisible"
    :case-id="caseId"
    mode="create"
    @success="handleTaskSuccess"
  />
  <TaskInstanceModal
    v-model:visible="processModalVisible"
    :case-id="caseId"
    mode="process"
    :instance-id="currentInstanceId"
    :readonly="isViewMode"
    @success="handleTaskSuccess"
  />
</template>

<script lang="ts" setup>
import TaskInstanceModal from '@/views/case/caseDetails/components/modal/task-instance-modal.vue'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { getTaskInstanceList } from '@/api/arcoApi/taskWorkFlow/taskInstance'
import { DictTypeEnum } from '@/dict/systemManage'
import useLoading from '@arco/hooks/loading'
import { computed, ref, onMounted, reactive, onUnmounted } from 'vue'
import bus from '@/utils/eventBus'
import { useDictStore } from '@arco/store'
import { useIntervalFn } from '@vueuse/core'

const { loading, setLoading } = useLoading(false)
const dictStore = useDictStore()

interface Props {
  caseId: string,
	listHeight: number
}

const props = withDefaults(defineProps<Props>(), {})

// 响应式数据
const taskInstanceList = ref<REQUEST_GET_TASKINSTANCE_LIST_DATA_TYPE[]>([])
const createModalVisible = ref(false)
const processModalVisible = ref(false)
const currentInstanceId = ref<string | null>(null)
const isViewMode = ref(false)

// 表格列定义
const columns: TableColumnData[] = [
  {
    title: '任务',
    dataIndex: 'taskType',
    slotName: 'taskType'
  },
  {
    title: '状态',
    dataIndex: 'taskStatus',
    slotName: 'taskStatus'
  },
  {
    title: '负责人',
    dataIndex: 'managerIdList',
    slotName: 'managerNames',
    tooltip: { position: 'top' },
    ellipsis: true
  },
  {
    title: '操作',
    slotName: 'operations',
    width: 80,
    align: 'center'
  }
]





const fetchTaskInstanceListHide = async () => {
	if (!props.caseId) return
	try {
		const params:REQUEST_POST_TASKINSTANCE_LISTSEARCH_PARAM_TYPE = {caseId: props.caseId};
		getTaskInstanceList(params).then(result=>{
			taskInstanceList.value = result
		})
	} catch (error) {
		console.error('获取任务实例列表失败:', error)
		taskInstanceList.value = []
	} finally {
	}
}

//定时刷新任务状态
useIntervalFn(fetchTaskInstanceListHide, 5000)

// 获取任务实例列表
const fetchTaskInstanceList = async () => {
  if (!props.caseId) return

  setLoading(true)
  try {
    const params:REQUEST_POST_TASKINSTANCE_LISTSEARCH_PARAM_TYPE = {caseId: props.caseId};
    const result = await getTaskInstanceList(params)
    taskInstanceList.value = result
  } catch (error) {
    console.error('获取任务实例列表失败:', error)
    taskInstanceList.value = []
  } finally {
    setLoading(false)
  }
}

// 处理任务创建
const handleCreateTask = () => {
  createModalVisible.value = true
}

// 处理任务办理
const handleProcessTask = (record: REQUEST_GET_TASKINSTANCE_DATA_TYPE) => {
  currentInstanceId.value = record.instanceId
  isViewMode.value = false
  processModalVisible.value = true
}

// 处理任务查看
const handleViewTask = (record: REQUEST_GET_TASKINSTANCE_DATA_TYPE) => {
  currentInstanceId.value = record.instanceId
  isViewMode.value = true
  processModalVisible.value = true
}

// 任务操作成功回调
const handleTaskSuccess = () => {
  fetchTaskInstanceList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTaskInstanceList()
})
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
:deep(.arco-card-header) {
  border-bottom: none;
}
:deep(.arco-card-body) {
  padding-top: 0px;
}
</style>
