<template>
  <a-table
    row-key="recordId"
    :scroll="{ maxHeight: '500px'}"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
  >
    <template #phoneStatus="{ record }">
      <span> {{ phoneStatusObj[record.phoneStatus] || record.phoneStatus }} </span>
    </template>
    <template #phoneCheckStatus="{ record }">
      <span> {{ phoneCheckStatusObj[record.phoneCheckStatus] || record.phoneCheckStatus }} </span>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { byCaseIdGetNumberDetectRecord } from '@/api/arcoApi/mediateManage/jobLog/numberDetectRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import { computed, inject, onMounted, ref } from 'vue'
import _ from 'lodash'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())
const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('号码检测记录')

const phoneCheckStatusOptions = ref<SelectOptionData[]>(dict.phoneCheckStatusOptions)
const phoneStatusOptions = ref<SelectOptionData[]>(dict.phoneStatusOptions)

const renderData = ref<REQUEST_GET_NUMBERDETECT_LIST_DATA_TYPE[]>([])
const caseId = ref<string>('')

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, title: '操作人', dataIndex: 'operatorName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人所属组织', dataIndex: 'operatorCompanyName' },
  { width: 120, tooltip: true, ellipsis: true, title: '检测联系人姓名', dataIndex: 'litigantName' },
  { width: 140, tooltip: true, ellipsis: true, title: '检测号码', dataIndex: 'litigantPhone' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码状态', dataIndex: 'phoneStatus', slotName: 'phoneStatus' },
  { width: 160, title: '检测结果', dataIndex: 'phoneCheckStatus', slotName: 'phoneCheckStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '操作时间', dataIndex: 'checkDate', align: 'center' }
])

const phoneStatusObj = computed(() => {
  return dictEnumValToObject(phoneStatusOptions.value)
})

const phoneCheckStatusObj = computed(() => {
  return dictEnumValToObject(phoneCheckStatusOptions.value)
})

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetNumberDetectRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
// 组件挂载时获取组织列表
onMounted(() => {
	getTableData(caseContext.caseId)
});

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
