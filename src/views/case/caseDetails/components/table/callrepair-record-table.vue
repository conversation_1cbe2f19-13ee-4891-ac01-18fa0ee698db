<template>
  <a-table
    row-key="repairLitigantId"
    :scroll="{ maxHeight: '500px' }"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="true"
    :expandable="expandable"
  >
    <template #acquireStatus="{ record }">
      <span> {{ phoneStatusObj[record.acquireStatus] || record.acquireStatus }} </span>
    </template>
    <template #repairStatus="{ record }">
      <span> {{ repairStatusObj[record.repairStatus] || record.repairStatus }} </span>
    </template>
    <template #repairSuccessNet="{ record }">
      <span> {{ repairNetObj[record.repairSuccessNet] || record.repairSuccessNet }} </span>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import {
  byCaseIdGetCallRepairRecordNew,
  byRepairRecordIdGetPhone
} from '@/api/arcoApi/mediateManage/jobLog/callRepairRecord'
import { h, reactive, computed, ref, inject, onMounted, onUnmounted } from 'vue'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { Table, Button, Message } from '@arco-design/web-vue'
import { IconLoop } from '@arco-design/web-vue/es/icon'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import bus from '@/utils/eventBus'
import dayjs from 'dayjs'
import _ from 'lodash'

const emits = defineEmits<{ (e: 'virtualNo', id: string, timeStamp: number): void }>()
// const pageZhName = ref('失联修复记录')

const dateTimeRecord = ref<{ [x: string]: { timeStamp: number; litigantId: string } }>({})
const renderData = ref<REQUEST_GET_CALLREPAIR_LIST_NEW_DATA_TYPE[]>([])
const repairTaskTimer = ref<NodeJS.Timeout>()
const caseId = inject('caseId') as string

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))

const columns = computed<TableColumnData[]>(() => [
  { width: 220, tooltip: true, ellipsis: true, title: '系统修复批次号', dataIndex: 'repairLitigantId' },
  { width: 120, tooltip: true, ellipsis: true, title: '修复姓名', dataIndex: 'litigantName' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复身份证号', dataIndex: 'idCard' },
  { width: 150, title: '修复状态', dataIndex: 'repairStatus', slotName: 'repairStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '批次修复结果', dataIndex: 'repairResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复成功所属渠道', slotName: 'repairSuccessNet' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次有效截止时间', dataIndex: 'expireTime' },
  { width: 140, title: '号码获取状态', dataIndex: 'acquireStatus', slotName: 'acquireStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取结果', dataIndex: 'acquireResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取失败原因', dataIndex: 'failMsg' },
  { width: 140, align: 'center', tooltip: true, ellipsis: true, title: '修复号码', dataIndex: 'virtualNo' },
  { width: 200, tooltip: true, ellipsis: true, title: '发起人所属组织', dataIndex: 'companyName' },
  { width: 200, tooltip: true, ellipsis: true, title: '发起人', dataIndex: 'accountName' },
  { width: 180, tooltip: true, ellipsis: true, title: '修复时间', dataIndex: 'repairSuccessTime', align: 'center' }
])

const childColumns = computed<TableColumnData[]>(() => [
  { width: 220, tooltip: true, ellipsis: true, title: '系统修复批次号', dataIndex: 'repairLitigantId' },
  { width: 120, tooltip: true, ellipsis: true, title: '渠道修复批次号', dataIndex: 'repairRecordId' },
  { width: 200, tooltip: true, ellipsis: true, title: '修复渠道', slotName: 'repairNet' },
  { width: 150, title: '修复状态', dataIndex: 'repairStatus', slotName: 'repairStatus' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次修复结果', dataIndex: 'repairResult' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次修复失败原因', dataIndex: 'repairFailMsg' },
  { width: 200, tooltip: true, ellipsis: true, title: '批次有效截止时间', dataIndex: 'expireTime' },
  { width: 140, title: '号码获取状态', dataIndex: 'acquireStatus', slotName: 'acquireStatus' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取结果', dataIndex: 'acquireResult' },
  { width: 150, tooltip: true, ellipsis: true, title: '号码获取失败原因', dataIndex: 'failMsg' },
  { width: 140, align: 'center', tooltip: true, ellipsis: true, title: '修复号码', slotName: 'virtualNo' },
  { width: 150, tooltip: true, ellipsis: true, title: '修复时间', dataIndex: 'repairCompleteTime' }
])

const expandable = reactive({
  width: 50,
  expandedRowRender: (record) => {
    if (record.mdtNetRepairRecordDTOList && record.mdtNetRepairRecordDTOList.length) {
      return h('div', { style: { padding: '20px', border: '1px solid #ccc' } }, [
        h(
          Table,
          {
            bordered: true,
            rowKey: 'repairRecordId',
            columns: childColumns.value,
            data: record.mdtNetRepairRecordDTOList,
            pagination: false
          },
          {
            acquireStatus: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', phoneStatusObj.value[record.acquireStatus] || record.acquireStatus)
            },
            batchStatus: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', repairStatusObj.value[record.repairStatus] || record.repairStatus)
            },
            repairNet: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              return h('span', repairNetObj.value[record.repairNet] || record.repairNet)
            },
            virtualNo: ({ record }: { record: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE }) => {
              const showBtn = handleShowGetPhoneBtn(record)
              return showBtn === 'showBtn' && !caseComplated.value
                ? h(
                    Button,
                    { type: 'text', loading: record['repairloading'], onClick: () => handleGetVirtualNo(record) },
                    {
                      default: () => '获取号码',
                      icon: () => h(IconLoop)
                    }
                  )
                : showBtn === 'showPhone'
                  ? record.virtualNo
                  : ''
            }
          }
        )
      ])
    }
  }
})

const repairStatusObj = computed(() => {
  return dictEnumValToObject(dict.repairStatusOptions)
})

const phoneStatusObj = computed(() => {
  return dictEnumValToObject(dict.phoneStatusOptions)
})

const repairNetObj = computed(() => {
  return dictEnumValToObject(dict.repairNetOptions)
})

const getTableData = async () => {
  try {
    const res = await byCaseIdGetCallRepairRecordNew(caseId)
    renderData.value = res
  } catch (err) {
    renderData.value = []
  }
}

const handleShowGetPhoneBtn = (row: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE) => {
  const currentTimeStamp = dayjs().valueOf()
  const expireTimeStamp = dayjs(row.expireTime).valueOf()
  // 当前时间大于有效截止时间
  // 状态非修复成功，截止时间不存在
  if (row.repairResult !== '修复成功' || !row.expireTime || currentTimeStamp > expireTimeStamp) {
    return 'hiden'
  }

  if (dateTimeRecord.value[row.repairRecordId] && !row.virtualNo) {
    delete dateTimeRecord.value[row.repairRecordId]
    emits('virtualNo', row.litigantId, 0)
  }

  return dateTimeRecord.value[row.repairRecordId] ? 'showPhone' : 'showBtn'
}

const handleGetVirtualNo = async (row: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE) => {
  row['repairloading'] = true
  await byRepairRecordIdGetPhone(row.repairRecordId)
  Message.success('获取号码操作成功！')
  row['repairloading'] = false
  await getTableData()
  // 获取到到当前时间戳,
  const timeStamp = dayjs().valueOf()
  // 根据ID设定key值，存储时间戳
  dateTimeRecord.value[row.repairRecordId] = { litigantId: row.litigantId, timeStamp }
  emits('virtualNo', row.litigantId, timeStamp)
}

onMounted(() => {
	getTableData()
  repairTaskTimer.value && clearInterval(repairTaskTimer.value)
  repairTaskTimer.value = setInterval(() => {
    // 3分钟时限
    const currentTimeStamp = dayjs().valueOf() - 3 * 60 * 1000
    for (const key in dateTimeRecord.value) {
      const timeStamp = dateTimeRecord.value[key].timeStamp
      if (currentTimeStamp > timeStamp) {
        emits('virtualNo', dateTimeRecord.value[key].litigantId, 0)
        delete dateTimeRecord.value[key]
      }
    }
    caseId && getTableData()
  }, 10000)
})

onUnmounted(() => {
  repairTaskTimer.value && clearInterval(repairTaskTimer.value)
})

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
