<template>
  <a-table
    row-key="smsId"
    :scroll="{ maxHeight: '500px'}"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
  >
    <template #sendType="{ record }">
      <span> {{ sendTypeObj[record.sendType] || record.sendType }} </span>
    </template>
    <template #sendMessage="{ record }">
      <span> {{ sendMessageObj[record.sendMessage] || record.sendMessage }} </span>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { byCaseIdGetSmsSendRecord } from '@/api/arcoApi/mediateManage/jobLog/smsSendRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import { computed, inject, onMounted, ref } from 'vue'
import _ from 'lodash'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())
const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('号码检测记录')

const sendMessageOptions = ref<SelectOptionData[]>(dict.sendMessageOptions)
const sendTypeOptions = ref<SelectOptionData[]>(dict.sendTypeOptions)

const renderData = ref<REQUEST_GET_SMS_LIST_DATA_TYPE[]>([])
const caseId = ref<string>('')

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, title: '收信人', dataIndex: 'contactName' },
  { width: 140, tooltip: true, ellipsis: true, title: '接听电话', dataIndex: 'phoneNumber' },
  { width: 240, tooltip: true, ellipsis: true, title: '调解组织', dataIndex: 'companyName' },
  { width: 140, tooltip: true, ellipsis: true, title: '发送人', dataIndex: 'senderName' },
  { width: 120, title: '发送方式', dataIndex: 'sendType', slotName: 'sendType' },
  { width: 200, tooltip: true, ellipsis: true, align: 'center', title: '发送内容', dataIndex: 'sendContext' },
  { width: 140, title: '回执', dataIndex: 'receiveStatus', slotName: 'receiveStatus' },
  { width: 120, title: '发送状态', dataIndex: 'sendStatus', slotName: 'sendStatus' },
  { width: 120, tooltip: true, ellipsis: true, title: '发送结果', dataIndex: 'sendMessage', slotName: 'sendMessage' },
  { width: 180, title: '发送时间', dataIndex: 'sendTime', align: 'center' }
])

const sendTypeObj = computed(() => {
  return dictEnumValToObject(sendTypeOptions.value)
})

const sendMessageObj = computed(() => {
  return dictEnumValToObject(sendMessageOptions.value)
})

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetSmsSendRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
// 组件挂载时获取组织列表
onMounted(() => {
	getTableData(caseContext.caseId)
});

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
