<template>
  <a-row style="margin-bottom: 16px">
    <a-col :span="24" class="mdt-col-flexend">
      <a-space :size="10">
				<a-button type="primary" @click="getTableData(caseId)">刷新</a-button>
        <a-button v-auth="['caseDocumentAdd']" type="primary" @click="addDocumentModalVisible=true">新增文书</a-button>
      </a-space>
    </a-col>
  </a-row>
  <a-table
    row-key="fileId"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
    :scroll="{ maxHeight: '500px'}"
  >
    <template #createType="{ record }">
      <span> {{ createTypeObj[record.createType] || record.createType }} </span>
    </template>
    <template #instrumentsIdentityType="{ record }">
      <span>
				{{$useDict.getDictTagByTypeAndKey(DictTypeEnum.litigant_identity_type,record.instrumentsIdentityType)}}
			</span>
    </template>
    <template #instrumentsSignStatus="{ record }">
      <a-link :hoverable="false" style="text-decoration: none;" @click="handleStartSign(record)">
        {{ signStatusObj[record.instrumentsSignStatus] || record.instrumentsSignStatus }}
      </a-link>
    </template>
    <template #createStatus="{ record }">
      <template v-for="item in aiContentStatus" :key="item.value">
        <a-tag v-if="item.value == record.createStatus" :color="item.color">
          {{ item.label }}
        </a-tag>
      </template>
      <span v-if="!getStatusLabel(record.createStatus)">
        {{ record.createStatus || '--' }}
      </span>
    </template>
    <template #operations="{ record }">
			<template v-if="record.createStatus == 4 || record.createType!='3'">
      <a-upload action="/" accept=".pdf" :show-file-list="false" :auto-upload="false" @change="handleUploadChange">
        <template #upload-button>
          <a-button v-auth="['caseFileView']" type="text" @click="handleUpload(record)">上传</a-button>
        </template>
      </a-upload>
      <a-button v-auth="['caseFileView']" type="text" @click="handleDownd(record)">下载</a-button>
      <a-button v-auth="['caseFileView']" v-if="record.createType!='3'" type="text" @click="handlePreview(record)"> 预览 </a-button>
      <a-button v-auth="['caseFileDelete']" type="text" @click="handleDelete(record)">删除</a-button>
			</template>
    </template>
  </a-table>
  <FilePreviewModal v-model:visible="modalVisible" :viewer-url="viewerUrl" :title="viewerTitle" />
  <SigntModal
    :id="selectItem?.instrumentsId"
    ref="signMdoalRef"
    v-model:visible="signtModalVisible"
    :file-path="selectItem?.instrumentsFilePath"
    @qrcode="getTableData(caseId)"
    @end-sign="getTableData(caseId)"
  />

  <DocumentModal
    ref="documentModalRef"
    v-model:visible="addDocumentModalVisible"
    :width="700"
    title="新增文书"
    @comfirm="handleAddDocumentComfirm"
  />
</template>

<script lang="ts" setup>
import { byCaseIdGetDocumentList, byIdDeleteDocument } from '@/api/arcoApi/caseManage/document'
import { uploadDocumentFile, downloadDocumentFile } from '@/api/arcoApi/caseManage/document'

import FilePreviewModal from '../modal/ifrme-preview-modal.vue'
import SigntModal from '../modal/sign-modal.vue'

import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { Modal, Message, FileItem } from '@arco-design/web-vue'
import useLoading from '@/layouts/appArco/hooks/loading'

import { createMethodsOptions } from '@/dict/businessConf'
import { pdfJsViewer } from '@/assets/ts/const'
import { preview } from '@/api/commonApi/file'
import { dictEnumValToObject } from '@/utils'
import sysUtil from '@/utils/sysUtil'
import dict from '@/dict/caseManage'
import { computed, ref } from 'vue'
import _ from 'lodash'
import Qs from 'qs'
import DocumentModal from '@/views/case/caseDetails/components/modal/document-modal.vue'
import { DictTypeEnum } from '../../../../../dict/systemManage.ts'
import { aiContentStatus } from '@/dict/ai.ts'

const { loading, setLoading } = useLoading(false)

interface Props {
  caseId?: string
}

const props = withDefaults(defineProps<Props>(), { caseId: '' })

const renderData = ref<REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE[]>([])
const selectItem = ref<REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE>()
const signMdoalRef = ref<InstanceType<typeof SigntModal>>()
const signtModalVisible = ref(false)
const modalVisible = ref(false)
const pageZhName = ref('文书')
const viewerTitle = ref('')
const viewerUrl = ref('')
const addDocumentModalVisible = ref(false)

const columns = computed<TableColumnData[]>(() => [
  { width: 200, ellipsis: true, tooltip: true, title: '文书模板', dataIndex: 'instrumentsTmplName' },
  { width: 200, ellipsis: true, tooltip: true, title: '文书名称', dataIndex: 'instrumentsName' },
  { width: 120, title: '生成方式', dataIndex: 'createType', slotName: 'createType' },
  { width: 120, title: '更新人', dataIndex: 'updaterName' },
  { width: 180, title: '更新时间', dataIndex: 'updateTime' },
  { width: 180, title: '生成状态', dataIndex: 'createStatus', slotName: 'createStatus' },
  // { width: 120, title: '签名人身份', dataIndex: 'instrumentsIdentityType', slotName: 'instrumentsIdentityType' },
  // { width: 120, title: '签名人', dataIndex: 'instrumentsLitigantName' },
  // { width: 120, align: 'center', title: '签名', dataIndex: 'instrumentsSignStatus', slotName: 'instrumentsSignStatus' },
  { width: 280, align: 'center', title: '操作', fixed: 'right', dataIndex: 'operations', slotName: 'operations' }
])


const signStatusObj = computed(() => {
  return dictEnumValToObject(dict.signStatusOptions)
})

const createTypeObj = computed(() => {
  return dictEnumValToObject(createMethodsOptions)
})

// 获取状态标签
const getStatusLabel = (status: number) => {
  const statusItem = aiContentStatus.find(item => item.value === status)
  return statusItem?.label || ''
}



const handleDelete = (row: REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE) => {
  if (row.instrumentsId) {
    Modal.warning({
      title: '提示',
      hideCancel: false,
      alignCenter: true,
      content: `是否确认删除该${pageZhName.value}`,
      onOk: () => {
        row.instrumentsId &&
          byIdDeleteDocument(row.instrumentsId)
            .then(() => {
              Message.success('操作成功')
              getTableData(props.caseId)
            })
            .catch(() => {})
      },
      onCancel: () => {}
    })
  }
}

const handleStartSign = async (row: REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE) => {
  if (row.instrumentsId && [1, 2].includes(row.instrumentsSignStatus)) {
    signtModalVisible.value = true
    selectItem.value = row
    if (row.instrumentsSignStatus === 2) {
      signMdoalRef.value?.getSignDetails(row.instrumentsId)
    }
  }
}

const handleUpload = async (row: REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE) => {
  selectItem.value = row
}

const handleUploadChange = (fileList: FileItem[]) => {
  if (fileList.length > 1) fileList.splice(0, 1)
  if (fileList[0].file) {
    let id = selectItem.value?.instrumentsId || ''
    let param = new FormData()
    param.append('multipartFile', fileList[0].file)
    param.append('instrumentsId', id.toString())
    uploadDocumentFile(param).then((res) => {
      if (res) {
        Message.success('上传成功')
        getTableData(props.caseId)
      }
    })
  }
}

const handleDownd = async (row: REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE) => {
  if (row.instrumentsId) {
    try {
      let res = await downloadDocumentFile({ instrumentsId: row.instrumentsId })
      Message.success('下载成功')
      sysUtil.blobExport(res)
    } catch (error) {
      Message.error('下载失败')
    }
  }
}

const handlePreview = async (row: REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE) => {
  if (row.instrumentsId) {
    viewerUrl.value =
      pdfJsViewer + '?' + Qs.stringify({ file: preview({ fileFolder: row.instrumentsFilePath, fileName: '' }) })
    viewerTitle.value = row.instrumentsName
    modalVisible.value = true
  } else {
    Message.warning('暂无该文书')
  }
}

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetDocumentList(id)
    renderData.value = res
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

props.caseId && getTableData(props.caseId)

const handleAddDocumentComfirm = () => {
	addDocumentModalVisible.value = false
	getTableData(props.caseId)
}


defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
