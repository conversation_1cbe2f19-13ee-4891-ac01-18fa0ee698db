<template>
  <a-row style="margin-bottom: 16px">
    <a-col :span="24" class="mdt-col-flexend">
      <a-space :size="10">
        <a-button v-auth="['caseFileUpload']" type="primary" @click="fileUploadModalVisible=true">新增附件</a-button>
      </a-space>
    </a-col>
  </a-row>
  <a-table
    row-key="fileId"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
    :scroll="{ maxHeight: '500px'}"
  >
    <template #operations="{ record }">
      <a-button v-if="['doc', 'docx', 'pdf'].includes(record.fileFormat)" v-auth="['caseFileView']" type="text" @click="handlePreview(record)">
        预览
      </a-button>
      <a-button v-auth="['caseFileView']" type="text" @click="handleDownd(record)">下载</a-button>
      <a-button v-auth="['caseFileDelete']" type="text" @click="handleDelete(record)">删除</a-button>
    </template>
  </a-table>
  <FilePreviewModal v-model:visible="modalVisible" :viewer-url="viewerUrl" :title="viewerTitle" />

  <FileUploadModal
    ref="fileUploadRef"
    v-model:visible="fileUploadModalVisible"
    :case-id="caseId"
    :width="480"
    title="上传附件"
    @single="handleFileUploadSingle"
    @comfirm="handleFileUploadComfirm"
  />
</template>

<script lang="ts" setup>
import { caseDetailsQueryFileList, caseDetailsDeleteFile } from '@/api/arcoApi/caseManage/caseDetails'

import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import FilePreviewModal from '../modal/ifrme-preview-modal.vue'
import { download, preview } from '@/api/commonApi/file'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { pdfJsViewer } from '@/assets/ts/const'
import { computed, ref } from 'vue'
import _ from 'lodash'
import Qs from 'qs'
import FileUploadModal from '@/views/case/caseDetails/components/modal/file-upload-modal.vue'

interface Props {
  caseId?: string
}

const props = withDefaults(defineProps<Props>(), { caseId: '' })

const { loading, setLoading } = useLoading(false)
const pageZhName = ref('案件材料')
const viewerTitle = ref('')
const viewerUrl = ref('')

const renderData = ref<REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE[]>([])
const modalVisible = ref<boolean>(false)

const columns = computed<TableColumnData[]>(() => [
  { width: 300, ellipsis: true, tooltip: true, title: '文件名称', dataIndex: 'fileName' },
  { width: 120, title: '文件格式', dataIndex: 'fileFormat' },
  { width: 120, title: '上传人', dataIndex: 'uploadUser' },
  { width: 180, title: '上传日期', dataIndex: 'uploadDate' },
  { width: 220, align: 'center', title: '操作', dataIndex: 'operations', slotName: 'operations' }
])

const handleDelete = (row: REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE) => {
  if (row.fileId) {
    Modal.warning({
      title: '提示',
      hideCancel: false,
      alignCenter: true,
      content: `是否确认删除该${pageZhName.value}`,
      onOk: () => {
        row.fileId &&
          caseDetailsDeleteFile(row.fileId)
            .then(() => {
              Message.success('操作成功')
              getTableData(props.caseId)
            })
            .catch(() => {})
      },
      onCancel: () => {}
    })
  }
}

const handleDownd = async (row: REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE) => {
  if (row.fileId) {
    location.assign(download({ filePath: row.filePath }))
  }
}

const handlePreview = async (row: REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE) => {
  if (row.filePath) {
    viewerUrl.value = pdfJsViewer + '?' + Qs.stringify({ file: preview({ fileFolder: row.filePath, fileName: '' }) })
    viewerTitle.value = row.fileName
    modalVisible.value = true
  } else {
    Message.warning('暂无该案件材料')
  }
}

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await caseDetailsQueryFileList(id)
    renderData.value = res
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

props.caseId && getTableData(props.caseId)

const fileUploadModalVisible = ref(false)

const handleFileUploadComfirm = () => {
	fileUploadModalVisible.value = false
	getTableData(props.caseId)
}

const handleFileUploadSingle = () => {
	getTableData(props.caseId)
}


defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
