<template>
  <a-table
    row-key="recordId"
    :scroll="{ maxHeight: '500px' }"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
    :expandable="expandable"
  >
    <template #meetingStatus="{ record }">
      {{ mediateStatusObj[record.meetingStatus] || record.meetingStatus }}
    </template>
    <template #operations="{ record }">
      <a-space>
        <a-button v-if="!caseComplated" v-auth="['caseSMSSend']" type="text" @click="handleSendSms(record)">
          短信发送
        </a-button>
        <!-- <a-button type="text" @click="handleJoinMeeting(record.filePath)">进入会议</a-button> -->
        <a-button type="text" @click="handleVideoPlay(record.filePath)">播放</a-button>
        <a-button type="text" @click="handleText(record.textContent)">文字</a-button>
      </a-space>
    </template>
  </a-table>
  <SmsSendModal
    ref="smsSendModalRef"
    v-model:visible="smsSendVisible"
    :width="500"
    :record-id="recordId"
    :case-id="caseId"
    title="短信发送"
    @comfirm="handleSmsSendConfirm"
    @cancel="smsSendVisible = false"
  />
  <ChatModal v-model:visible="chatVisible" :width="500" :text="textContent" title="文字"></ChatModal>
  <a-modal
    v-model:visible="videoVisible"
    :footer="false"
    :width="500"
    :body-style="{ textAlign: 'center' }"
    :on-before-cancel="handleStopPlay"
    title="录屏播放"
  >
    <video ref="videoRef" :src="videoUrl" width="100%" height="320px" autoPlay :muted="true" :controls="true">
      您的浏览器不支持video
    </video>
  </a-modal>
</template>

<script lang="ts" setup>
import { byCaseIdGetVideoMediateRecord } from '@/api/arcoApi/mediateManage/jobLog/videoMediateRecord'
import { getMeetingVideoPath } from '@/api/arcoApi/mediateManage/jobLog/videoMediateRecord'
import { repairSendBusinessSms } from '@/api/arcoApi/mediateManage/jobLog/smsSendRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { Message, Table, Space, Button } from '@arco-design/web-vue'
import { h, computed, ref, reactive, nextTick, inject, onMounted } from 'vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import SmsSendModal from '../modal/sms-send-modal.vue'
import ChatModal from '../modal/chat-modal.vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/mediateManage'
import bus from '@/utils/eventBus'
import _ from 'lodash'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())
const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('视频调解记录')

const meetingStatusOptions = ref<SelectOptionData[]>(dict.meetingStatusOptions)

const renderData = ref<REQUEST_GET_VIDEOMEDIATE_BY_CASEID_DATA_TYPE[]>([])
const smsSendModalRef = ref<InstanceType<typeof SmsSendModal>>()
const recordId = ref<string | number>('')
const caseId = ref<string | number>('')

const smsSendVisible = ref(false)
const chatVisible = ref(false)
const textContent = ref('')

const videoRef = ref<HTMLVideoElement>()
const videoVisible = ref(false)
const videoUrl = ref('')

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))

const expandable = reactive({
  width: 50,
  expandedRowRender: (record) => {
    if (record.smsList && record.smsList.length) {
      return h('div', { style: { padding: '20px', background: '#fff', border: '1px solid #ccc' } }, [
        h(
          Table,
          { columns: smsColumns.value, data: record.smsList, pagination: false },
          {
            operations: ({ record: smsInfo }: { record: REQUEST_GET_VIDEOMEDIATE_BY_CASEID_SMS_DATA_TYPE }) => {
              return h(
                Space,
                { size: 4 },
                {
                  default: () =>
                    caseComplated.value
                      ? [
                          h(
                            Button,
                            { type: 'text', size: 'small', onClick: () => handleRepairSendSms(smsInfo) },
                            { default: () => '重新发送' }
                          )
                        ]
                      : ''
                }
              )
            }
          }
        )
      ])
    }
  }
})

const columns = computed<TableColumnData[]>(() => [
  { width: 180, tooltip: true, ellipsis: true, title: '会议名称', dataIndex: 'meetingName' },
  { width: 140, align: 'center', title: '会议发起人', dataIndex: 'mediatorName' },
  { width: 180, align: 'center', title: '参会人员', dataIndex: 'participant' },
  { width: 140, align: 'center', title: '会议状态', dataIndex: 'meetingStatus', slotName: 'meetingStatus' },
  { width: 120, align: 'center', title: '会议密码', dataIndex: 'password' },
  { width: 200, tooltip: true, ellipsis: true, align: 'center', title: '会议链接', dataIndex: 'meetingUrl' },
  { width: 180, title: '会议时间', dataIndex: 'mediationTime', align: 'center' },
  { width: 350, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const smsColumns = computed<TableColumnData[]>(() => [
  { width: 140, align: 'center', title: '姓名', dataIndex: 'contactName' },
  { width: 140, align: 'center', title: '电话', dataIndex: 'phoneNumber' },
  { width: 180, align: 'center', title: '发送结果', dataIndex: 'receiveDescription' },
  { width: 380, tooltip: true, ellipsis: true, align: 'center', title: '短信内容', dataIndex: 'msgContent' },
  { width: 200, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const mediateStatusObj = computed(() => {
  return dictEnumValToObject(meetingStatusOptions.value)
})

const handleSmsSendConfirm = () => {
  smsSendVisible.value = false
  getTableData(caseId.value)
}

// 发送短信
const handleSendSms = (row: REQUEST_GET_VIDEOMEDIATE_BY_CASEID_DATA_TYPE) => {
  recordId.value = row.recordId
  smsSendVisible.value = true
}

// 重新发送短信
const handleRepairSendSms = (smsInfo: REQUEST_GET_VIDEOMEDIATE_BY_CASEID_SMS_DATA_TYPE) => {
  let param = { smsSendId: smsInfo.smsSendId }
  repairSendBusinessSms(param).then(() => {
    Message.success('短信已重新发送')
    getTableData(caseId.value)
  })
}

// 播放录屏
const handleVideoPlay = async (filePath: string) => {
  if (filePath) {
    let _videoUrl = await getMeetingVideoPath({ filePath })
    if (_videoUrl) {
      videoVisible.value = true
      _videoUrl = _videoUrl.replace('http', 'https')
      nextTick(() => {
        videoUrl.value = _videoUrl
        videoRef.value?.play()
      })
    } else {
      Message.warning('这条记录没有录屏')
    }
  } else {
    Message.warning('这条记录没有录屏')
  }
}

const handleStopPlay = () => {
  if (videoRef.value) {
    videoRef.value.pause()
    videoRef.value.currentTime = 0
  }
  return true
}

const handleText = (text: string) => {
  if (!text) {
    Message.warning('暂无聊天记录')
    return
  }
  textContent.value = text
  chatVisible.value = true
}

const getTableData = async (id: string | number) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetVideoMediateRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}
// 组件挂载时获取组织列表
onMounted(() => {
	getTableData(caseContext.caseId)
});

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
