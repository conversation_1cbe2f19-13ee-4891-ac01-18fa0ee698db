<template>
  <a-table
    row-key="collectionId"
    :scroll="{ maxHeight: '500px'}"
    :loading="loading"
    :columns="cloneColumns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
  >
    <template #filePath="{ record }">
      <span
        v-if="record.filePath"
        class="mdt-common-text mdt-common-text-active"
        @click="handleDownload(record.filePath)"
      >
        {{ record.filePath.split('__').pop() }}
      </span>
      <span v-else class="mdt-common-text mdt-common-text-visited">(暂无附件)</span>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { byCaseIdGetCaseReceptionRecord } from '@/api/arcoApi/mediateManage/jobLog/caseReceptionRecord'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { download } from '@/api/commonApi/file'
import { computed, ref, watch } from 'vue'
import _ from 'lodash'

type Column = TableColumnData & { checked?: true }

const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('接收记录')

const renderData = ref<REQUEST_GET_CASERECEPTION_LIST_DATA_TYPE[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const caseId = ref<string>('')

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, title: '接收方式', dataIndex: 'collectionType' },
  { width: 120, tooltip: true, ellipsis: true, title: '案件创建结果', dataIndex: 'collectionResult' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建人', dataIndex: 'creatorName' },
  { width: 180, tooltip: true, ellipsis: true, title: '创建人所属组织', dataIndex: 'companyName' },
  { width: 180, tooltip: true, ellipsis: true, title: '所属案源方', dataIndex: 'entrustsName' },
  { width: 180, tooltip: true, ellipsis: true, title: '案件基本信息模板', dataIndex: 'tmplName' },
  { width: 180, tooltip: true, ellipsis: true, title: '相关附件', dataIndex: 'filePath', slotName: 'filePath' },
  { width: 180, tooltip: true, ellipsis: true, title: '接收时间', dataIndex: 'acceptTime', align: 'center' }
])

const handleDownload = (filePath: string) => {
  location.assign(download({ filePath }))
}

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetCaseReceptionRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
