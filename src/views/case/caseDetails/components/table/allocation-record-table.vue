<template>
  <a-table
    row-key="recordId"
    :scroll="{ maxHeight: '500px'}"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
  >
    <template #toStatus="{ record }">
      <span>
        调解状态由【{{ $useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,record.fromStatus) || record.fromStatus }}】变为 【
        {{ $useDict.getDictTagByTypeAndKey(DictTypeEnum.mediate_status,record.toStatus) || record.toStatus }}】
      </span>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { byCaseIdGetCaseAllocationRecord } from '@/api/arcoApi/mediateManage/jobLog/caseAllocationRecord'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, inject, onMounted, ref } from 'vue'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())
const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('案件分派记录')

const renderData = ref<REQUEST_GET_CASEALLOCATION_LIST_DATA_TYPE[]>([])
const caseId = ref<string>('')

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, title: '案件所属案源方', dataIndex: 'entrustsName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人', dataIndex: 'operatorName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人角色', dataIndex: 'roleName' },
  { width: 120, tooltip: true, ellipsis: true, title: '操作人所属组织', dataIndex: 'orgName' },
  { width: 120, tooltip: true, ellipsis: true, title: '分派对象', dataIndex: 'distributeTargetName' },
  { width: 150, tooltip: true, ellipsis: true, title: '状态变更记录', dataIndex: 'toStatus', slotName: 'toStatus' },
  { width: 200, tooltip: true, ellipsis: true, title: '操作时间', dataIndex: 'operateTime', align: 'center' }
])



const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetCaseAllocationRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

// 组件挂载时获取组织列表
onMounted(() => {
	getTableData(caseContext.caseId)
});

defineExpose({ getTableData })
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
