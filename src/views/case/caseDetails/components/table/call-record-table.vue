<template>
	<a-row align="end" justify="end" style="padding: 0 10px 10px 0">
		<a-button type="primary" @click="uploadModalVisible = true">上传</a-button>
	</a-row>
  <a-table
    v-model:selected-keys="selectedKeys"
    row-key="recordId"
    :scroll="{ maxHeight: '500px' }"
    :loading="loading"
    :columns="columns"
    :data="renderData"
    :pagination="false"
    :bordered="false"
    :row-selection="multiSelect ? rowSelection : undefined"
  >
    <template #timeLength="{ record }">
      <span> {{ record.timeLength || '--' }}秒 </span>
    </template>
    <template #ringLength="{ record }">
      <span> {{ record.ringLength || '--' }}秒 </span>
    </template>
    <template #operations="{ record }">
      <a-space :size="5">
        <a-button type="text" @click="handleAsrReview(record)">查看文字稿</a-button>
        <a-button type="text" @click="handleDownload(record.filePath)">下载</a-button>
        <a-button type="text" @click="handleAudioPlay(record.filePath)">播放</a-button>
      </a-space>
    </template>
  </a-table>

  <!-- 多选模式下的底部操作按钮 -->
  <div v-if="multiSelect" class="multi-select-footer">
    <a-space>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm" :disabled="selectedKeys.length === 0">
        确定 ({{ selectedKeys.length }})
      </a-button>
    </a-space>
  </div>

	<AIContentModal
		v-model:visible="asrReviewVisible"
		title="文字稿"
		:content-type="AIContentType.CALL_RECORD_ASR"
		:business-id="currentRecord.recordId"
		:show-reanalysis-button="isDebug?true:false"
	>
		<template #info><span style="margin-right: 10px"> 由录音转来</span> <a-button type="primary" @click="handleAudioPlay(currentRecord.filePath)">播放</a-button> </template>
	</AIContentModal>

  <FreeModal
    v-model:visible="audioVisible"
    :footer="true"
    :width="400"
    :body-style="{ textAlign: 'center' }"
    :on-before-cancel="handleStopPlay"
    title="录音播放"
  >
    <audio ref="audioRef" :src="audioUrl" autoplay controls controlslist="nodownload">您的浏览器不支持audio</audio>
  </FreeModal>
  <a-modal
    :visible="uploadModalVisible"
    title="上传通话记录"
    :width="500"
    @ok="handleUpload"
    @cancel="uploadCancel"
  >
    <a-form ref="uploadFormRef" :model="uploadForm" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="当事人" field="litigantIdList" :rules="[{ required: true, message: '请选择当事人' }]">
            <a-select
							multiple
              v-model="uploadForm.litigantIdList"
              :options="litigantListOptions"
              placeholder="请选择当事人"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">

					<a-form-item label="呼叫时间" field="startTime" :rules="[{ required: true, message: '请选择呼叫时间' }]">
						<a-date-picker v-model="uploadForm.startTime" show-time format="YYYY-MM-DD HH:mm:ss" placeholder="请选择呼叫时间" style="width: 100%" />
					</a-form-item>

        </a-col>
      </a-row>
      
      <a-row :gutter="16">

				<a-col :span="12">
					<a-form-item label="调解组织" field="orgId" :rules="[{ required: true, message: '请选择调解组织' }]">
						<a-select
							v-model="uploadForm.orgId"
							:options="orgOptions"
							placeholder="请选择调解组织"
							@change="handleOrgChange"
						/>
					</a-form-item>
				</a-col>
        <a-col :span="12">
          <a-form-item label="员工" field="mediatorId" :rules="[{ required: true, message: '请选择员工' }]">
            <a-select
              v-model="uploadForm.mediatorId"
              :options="mediatorOptions"
              placeholder="请选择员工"
              :disabled="!uploadForm.orgId"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-form-item label="录音文件" field="fileList" :rules="[{ required: true, message: '请选择录音文件' }]">
        <a-upload
          v-model:file-list="uploadForm.fileList"
					@change="uploadFileChange"
          draggable
          :auto-upload="false"
          :limit="1"
          accept=".mp3, .wav, .wma, .m4a"
        >
          <template #upload-button>
            <a-button><icon-upload /><span class="ml4">点击或拖拽至此上传文件</span></a-button>
          </template>
        </a-upload>
      </a-form-item>

			<a-form-item label="音频时长" field="timeLength" :rules="[{ required: true, message: '等待音频时长计算' }]">
				<a-input v-model="uploadForm.timeLength" disabled />
			</a-form-item>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="被叫号码" field="calleePhone">
            <a-input v-model="uploadForm.calleePhone" placeholder="请输入被叫号码" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主叫号码" field="callerPhone">
            <a-input v-model="uploadForm.callerPhone" placeholder="请输入主叫号码" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { byCaseIdGetCallRecord, getCallRecordStream, uploadCallRecord } from '@/api/arcoApi/mediateManage/jobLog/callRecord'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import useLoading from '@/layouts/appArco/hooks/loading'
import { download } from '@/api/commonApi/file'
import { FormInstance, Message, SelectOptionData } from '@arco-design/web-vue'
import { computed, ref, nextTick, inject, onMounted, reactive } from 'vue'
import bus from '@/utils/eventBus'
import _ from 'lodash'
import { getOrg, getMdtMediator } from '@/api/arcoApi/caseManage/caseManage'
import { AIContentType } from '@/api/arcoApi/ai/ai.ts'
import AIContentModal from '@/views/case/caseDetails/components/aiContent/AIContentModal.vue'
// 使用统一的上下文
import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())
import debug from '@arco/utils/env.ts'
import FreeModal from '@/components/FreeModal/freeModal.vue'

// Props 定义
interface Props {
  multiSelect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiSelect: false
})

// Emits 定义
const emit = defineEmits<{
  'multi-select-confirm': [selectedRecords: REQUEST_GET_CALL_LIST_DATA_TYPE[]]
  'multi-select-cancel': []
}>()

const isDebug = ref<Boolean>(debug);
const { loading, setLoading } = useLoading(true)
// const pageZhName = ref('呼叫记录')

const renderData = ref<REQUEST_GET_CALL_LIST_DATA_TYPE[]>([])
const audioRef = ref<HTMLAudioElement>()
const audioVisible = ref(false)
const caseId = ref<string>('')
const audioUrl = ref('')

// 多选相关状态
const selectedKeys = ref<(string | number)[]>([])

// 表格行选择配置
const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const caseComplated = ref(false)
bus.on('transfer', (state) => (caseComplated.value = state as boolean))

const columns = computed<TableColumnData[]>(() => [
  { width: 120, align: 'center', title: '呼叫类型', dataIndex: 'usageMode' },
  { width: 160, tooltip: true, ellipsis: true, align: 'center', title: '呼叫结果', dataIndex: 'callResult' },
  { width: 100, align: 'center', title: '通话时长', dataIndex: 'timeLength', slotName: 'timeLength' },
  { width: 100, align: 'center', title: '响铃时长', dataIndex: 'ringLength', slotName: 'ringLength' },
  { width: 180, align: 'center', title: '主叫号码', dataIndex: 'callerPhone' },
  { width: 180, align: 'center', title: '被叫号码', dataIndex: 'calleePhone' },
  { width: 140, tooltip: true, ellipsis: true, align: 'center', title: '当事人', dataIndex: 'calleeName' },
  { width: 210, tooltip: true, ellipsis: true, title: '所属组织', dataIndex: 'orgName', align: 'center' },
  { width: 120, tooltip: true, ellipsis: true, title: '员工姓名', dataIndex: 'mediatorName', align: 'center' },
  { width: 180, tooltip: true, ellipsis: true, title: '呼叫时间', dataIndex: 'startTime', align: 'center' },
  { width: 240, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleDownload = (filePath: string) => {
  if (filePath) {
    location.assign(download({ filePath }))
  } else {
    Message.warning('这条呼叫记录没有录音')
  }
}

// 播放录音
const handleAudioPlay = (filePath: string) => {
  if (filePath) {
    getCallRecordStream(filePath).then((res) => {
      if (res && res.data) {
        audioVisible.value = true
        nextTick(() => {
          let blob = new Blob([res.data])
          if (window.URL && audioRef.value) {
            audioUrl.value = window.URL.createObjectURL(blob)
          }
          audioRef.value?.play()
        })
      } else {
        Message.warning('这条记录没有录音')
      }
    })
  } else {
    Message.warning('这条记录没有录音')
  }
}

const handleStopPlay = () => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }
  return true
}

const getTableData = async (id: string) => {
  setLoading(true)
  try {
    const res = await byCaseIdGetCallRecord(id)
    renderData.value = res
    caseId.value = id
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

defineExpose({ getTableData })

const uploadModalVisible = ref(false)

interface UploadFormType {
	caseId: string;
  calleeName: string;
	litigantIdList:string[];
  calleePhone: string;
  callerPhone: string;
  startTime: string;
  fileList: any[]; // 或者使用更具体的类型，如 UploadFileInfo[]
	orgId: string;
  mediatorId: string;
	timeLength: string
}

const getInitUploadForm = (): UploadFormType => {
  return {
		caseId: caseContext.caseId,
    calleeName: '',
		litigantIdList: [],
    calleePhone: '',
    callerPhone: '',
    startTime: '',
    fileList: [],
		orgId: '',
    mediatorId: '',
		timeLength: ''
  }
}

const uploadFormRef = ref<FormInstance>();
const uploadForm = ref<UploadFormType>(getInitUploadForm());
// 组织和员工选项
const orgOptions = ref<SelectOptionData[]>([]);
const mediatorOptions = ref<SelectOptionData[]>([]);

// 获取组织列表
const getOrgList = async () => {
  try {
    const res = await getOrg();
    if (res && res.length) {
        orgOptions.value = res.map(item => ({
        label: item.orgName,
        value: item.orgId
      }));
    }
  } catch (error) {
    console.error('获取组织列表失败', error);
  }
};

// 获取员工列表
const getMediatorList = async (orgId: string) => {
  if (!orgId) {
    mediatorOptions.value = [];
    return;
  }
  
  try {
    // 直接通过组织ID获取员工列表
    const mediatorRes = await getMdtMediator({orgIds: [orgId]});
    if (mediatorRes && mediatorRes.length) {
      mediatorOptions.value = mediatorRes.map(item => ({
        label: item.employeeName,
        value: item.accountId
      }));
    } else {
      mediatorOptions.value = [];
    }
  } catch (error) {
    console.error('获取员工列表失败', error);
    mediatorOptions.value = [];
  }
};

// 组织变更处理
const handleOrgChange = (value: string) => {
  uploadForm.value.mediatorId = '';
  getMediatorList(value);
};



// 组件挂载时获取组织列表
onMounted(() => {
  getOrgList();
	getTableData(caseContext.caseId)
});

// 监听当事人列表变化，更新选项
const litigantListOptions = computed(() => {
	if (caseContext.litigantList.value) {
		return caseContext.litigantList.value.map((item) => ({
			label: item.litigantName,
			value: item.litigantId,
			phone: item.litigantPhone
		}))
	}
	return []
})


const handleUpload = async () => {
	const res = await uploadFormRef.value?.validate()
	if (!res) {
		const formData = new FormData()
		// 单独处理文件
		formData.append('file', uploadForm.value.fileList[0].file)

		//把当事人名字用逗号分割
		uploadForm.value.calleeName = uploadForm.value.litigantIdList.map((item) => {
			const litigant = litigantListOptions.value.find((litigant) => litigant.value === item)
			return litigant ? litigant.label : ''
		}).join(',')
		
		// 批量处理其他表单项
		const formDataObj = { ...uploadForm.value }
		// 移除fileList，因为已单独处理
		delete formDataObj.fileList
		
		// 将剩余字段批量添加到FormData
		Object.entries(formDataObj).forEach(([key, value]) => {
			if (value !== undefined && value !== null && value !== '') {
				formData.append(key, value.toString())
			}
		})

		try {
			await uploadCallRecord(formData)
			Message.success('上传成功')
			uploadModalVisible.value = false
			uploadForm.value = getInitUploadForm()
			getTableData(caseContext.caseId)
		} catch (err) {
			Message.error('上传失败')
		}
	}

	return false;
}

const uploadCancel = () => {
	uploadForm.value = getInitUploadForm()
	uploadFormRef.value?.clearValidate()
	uploadModalVisible.value = false
}

//获取音频数据
function uploadFileChange(multipleFile) {
	if (!multipleFile || multipleFile.length === 0) {
		return;
	}
	//获取录音时长
	var url = URL.createObjectURL(multipleFile[0].file);
	//经测试，发现audio也可获取视频的时长
	var audioElement = new Audio(url);
	audioElement.addEventListener("loadedmetadata", (_event) => {
		//保存获取时间长度
		uploadForm.value.timeLength = String(Math.round(audioElement.duration))
	});
}

//asr内容展示
const asrReviewVisible = ref(false)
const currentRecord = ref<REQUEST_GET_CALL_LIST_DATA_TYPE>({
	recordId: '',
	filePath:''
})
const handleAsrReview = (record: REQUEST_GET_CALL_LIST_DATA_TYPE) => {
	currentRecord.value = record
  asrReviewVisible.value = true
}

// 多选操作处理函数
const handleCancel = () => {
  selectedKeys.value = []
  emit('multi-select-cancel')
}

const handleConfirm = () => {
  const selectedRecords = renderData.value.filter(record =>
    selectedKeys.value.includes(record.recordId)
  )
  emit('multi-select-confirm', selectedRecords)
  //清空当前选择
  selectedKeys.value = []
}


</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

//隐藏文件列表的上传按钮
:deep(.arco-upload-progress) {
	display: none;
}

// 多选底部操作按钮样式
.multi-select-footer {
  padding: 16px;
  border-top: 1px solid var(--color-border-2);
  background-color: var(--color-bg-2);
  text-align: center;
  margin-top: 16px;
}

</style>
