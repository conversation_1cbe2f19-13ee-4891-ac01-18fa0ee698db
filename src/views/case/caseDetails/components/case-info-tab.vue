<template>
  <a-card style="height: 100%" :bordered="false">
    <record-tabs v-model:active-tab="activeTab">
      <record-tab
        value="videoMediate"
        title="视频调解"
        icon="icon-video-camera"
        :need-modal="false"
        @click="handleVideoMediate"
      >
      </record-tab>
      <record-tab value="videoMediateRecordTableRef" title="视频调解记录" icon="icon-live-broadcast">
        <VideoMediateRecordTable />
      </record-tab>
      <record-tab value="callRecordRef" title="呼叫记录" icon="icon-voice">
        <CallRecordTable />
      </record-tab>
      <record-tab value="smsRecordRef" title="短信记录" icon="icon-email">
        <SmsRecordTable />
      </record-tab>
      <record-tab value="documentListTableRef" title="案件文书" icon="icon-book">
        <DocumentListTable :case-id="caseId"></DocumentListTable>
      </record-tab>
      <record-tab value="caseMaterialsTableRef" title="案件附件" icon="icon-attachment">
        <CaseMaterialsTable :case-id="caseId" />
      </record-tab>
      <record-tab value="allocationRecordRef" title="分派记录" icon="icon-share-internal">
        <AllocationRecordTable />
      </record-tab>
<!--      <record-tab value="receptionRecordRef" title="接收记录" icon="icon-archive">-->
<!--        <ReceptionRecordTable> -->
<!--      </record-tab> -->

      <record-tab value="numbercheckRecordRef" title="号码检测记录" icon="icon-safe">
        <NumberCheckRecordTable />
      </record-tab>
      <record-tab value="callRepairRecordRef" title="失联修复记录" icon="icon-tool" :unmount-on-close="false">
        <CallRepairRecordTable @virtual-no="handleVirtualNoChange"/>
      </record-tab>
      <record-tab value="operationLogRef" title="操作日志" icon="icon-history">
				<operation-log :case-id="caseId"  />
      </record-tab>
    </record-tabs>
  </a-card>
</template>

<script lang="ts" setup>
import VideoMediateRecordTable from './table/videomediate-record-table.vue'
import NumberCheckRecordTable from './table/numbercheck-record-table.vue'
import CallRepairRecordTable from './table/callrepair-record-table.vue'
import AllocationRecordTable from './table/allocation-record-table.vue'
import CallRecordTable from './table/call-record-table.vue'
import SmsRecordTable from './table/sms-record-table.vue'
import { ref } from 'vue'
import RecordTabs from '@/views/case/caseDetails/components/tab/record-tabs.vue'
import RecordTab from '@/views/case/caseDetails/components/tab/record-tab.vue'
import { getMeetingUrl } from '@/api/arcoApi/caseManage/caseDetails.ts'
import { Message } from '@arco-design/web-vue'
import DocumentListTable from '@/views/case/caseDetails/components/table/document-list-table.vue'
import CaseMaterialsTable from '@/views/case/caseDetails/components/table/case-materials-table.vue'
import OperationLog from '@/views/system/operationLog/index.vue'


const { caseId } = defineProps({ caseId: { type: String, default: '' } })

const emits = defineEmits<{ (e: 'callRepair', id: string, timeStamp: number): void }>()

const activeTab = ref<string>()



const handleVirtualNoChange = (id: string, timeStamp: number) => {
	emits('callRepair', id, timeStamp)
}



function isValidURL(url) {
	var expression = /[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi
	var regex = new RegExp(expression)
	return regex.test(url)
}

const handleVideoMediate = async () => {
	let urlRes = await getMeetingUrl(caseId)
	if (urlRes) {
		if (isValidURL(urlRes)) {
			window.open(urlRes)
		} else {
			Message.error('该庭室链接无效，请联系管理员')
		}
	} else {
		Message.error('暂无庭室链接')
	}
}



</script>

<style scoped lang="scss"></style>
