<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="right"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              :label-col-props="{ span: 5 }"
              :wrapper-col-props="{ span: 18 }"
              field="auditDes"
              label="原因:"
            >
              <a-textarea
                v-model="baseInfoForm.auditDes"
                :auto-size="{ minRows: 4 }"
                :max-length="250"
                placeholder="请填写审核备注"
                allow-clear
              />
            </a-form-item>
            <a-form-item field="caseDelayTime" label="延期时间">
              <a-date-picker v-model="baseInfoForm.caseDelayTime" style="width: 100%">
                <template #suffix-icon><icon-schedule /></template>
              </a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { batchAuditApproval } from '@/api/arcoApi/caseManage/approvalRecord'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import { ref } from 'vue'

interface Props {
  approvalIdList: (string | number)[]
  width?: string | number
  mdtResult: '1' | '2'
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  approvalIdList: () => [],
  width: '500px',
  mdtResult: '1',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    approvalIdList: [],
    caseDelayTime: '',
    auditResult: '',
    auditDes: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_APPROVAL_AUDIT_PARAM_TYPE>(generateFormModel())

const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    baseInfoForm.value.approvalIdList = props.approvalIdList
    batchAuditApproval(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

const okClick = () => {}
</script>

<style lang="scss"></style>
