<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :label-col-props="{ span: 7 }"
        :wrapper-col-props="{ span: 17 }"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">案件信息：</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item :label-col-props="{ span: 2 }" :wrapper-col-props="{ span: 22 }" field="caseIds">
                已选择案件数量：{{ ifChooseAll ? '全部案件' : `${caseIds.length} 件` }}
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">联系人信息：</span>
          </div>
          <a-form-item field="identityTypeList" label="请选择当事人身份：">
						<DictDataSelect v-model="baseInfoForm.identityTypeList" :dict-type="DictTypeEnum.litigant_identity_type" multiple />
          </a-form-item>
          <a-form-item field="litigantTypeList" label="请选择当事人类型：">
            <a-select
              v-model="baseInfoForm.litigantTypeList"
              :options="litigantTypeOptions"
              :max-tag-count="1"
              placeholder="请选择"
              allow-clear
              multiple
            />

          </a-form-item>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">联系人号码情况：</span>
          </div>
          <a-form-item field="litigantPhoneStatusesList" label="需要修复的号码状态：">
            <a-select
              v-model="baseInfoForm.litigantPhoneStatusesList"
              :options="phoneStatusOptions"
              :max-tag-count="1"
              placeholder="请选择"
              allow-clear
              multiple
            />
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { batchCallRepair, batchCallRepairNew } from '@/api/arcoApi/mediateManage/jobLog/callRepairRecord'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/caseManage'
import { inject, ref } from 'vue'
import _ from 'lodash'
import { Ref } from 'vue'
import { DictTypeEnum } from '@/dict/systemManage.ts'

interface Props {
  caseIds: (string | number)[]
  width?: string | number
  ifChooseAll?: boolean
  callState: number
  title: string
}

const mediateCasePageBO = inject('mediateCasePageBO') as Ref<OBJ_KEY_STR_ANY_TYPE>

const props = withDefaults(defineProps<Props>(), {
  caseIds: () => [],
  ifChooseAll: false,
  width: '600px',
  callState: 0,
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    litigantPhoneStatusesList: [],
    ifChooseAll: false,
		mediateCasePageBO:mediateCasePageBO.value,
    identityTypeList: [],
    litigantTypeList: [],
    litigantId: '',
    caseIdList: []
  }
}

const phoneStatusOptions = ref<SelectOptionData[]>(dict.litigantPhoneStatusOptions)
const litigantTypeOptions = ref<SelectOptionData[]>(dict.litigantTypeOptions)

const baseInfoForm = ref<REQUEST_GET_CALLREPAIR_BATCH_PARAM_TYPE>(generateFormModel())
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const handleSaveModal = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    okLoading.value = true
    baseInfoForm.value.caseIdList = props.caseIds
    baseInfoForm.value.ifChooseAll = props.ifChooseAll
    baseInfoForm.value.mediateCasePageBO = props.ifChooseAll ? mediateCasePageBO.value : {}
    if(props.callState) {
      batchCallRepairNew(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
    } else {
      batchCallRepair(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
    }
    
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
