<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="formRules"
        :label-col-props="{ span: 1 }"
        :wrapper-col-props="{ span: 23 }"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">案件信息：</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item :label-col-props="{ span: 1 }" :wrapper-col-props="{ span: 22 }" field="caseIds">
                已选择案件数量：{{ ifChooseAll ? '全部案件' : `${caseIds.length} 件` }}
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">请选择要制作的文书：</span>
          </div>
          <a-form-item>
            <a-card class="mdt-card-common">
              <a-table
                v-model:selected-keys="tableSelectedKeys"
                row-key="docTmplId"
                :row-selection="rowSelection"
                :pagination="false"
                :columns="columns"
                :scroll="{ maxHeight: '30vh' }"
                :data="renderData"
                :bordered="false"
              >
              </a-table>
            </a-card>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { batchGenerateSingleDoc, findSingleByCaseIds } from '@/api/arcoApi/businessConf/docTemplateManage'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { computed, reactive, ref, watch, inject } from 'vue'
import { Message } from '@arco-design/web-vue'
import _ from 'lodash'
import { Ref } from 'vue'

interface Props {
  caseIds?: (string | number)[]
  width?: string | number
  ifChooseAll?: boolean
  title: string
}

const mediateCasePageBO = inject('mediateCasePageBO') as Ref<OBJ_KEY_STR_ANY_TYPE>

const props = withDefaults(defineProps<Props>(), {
  caseIds: () => [],
  ifChooseAll: false,
  width: '600px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    instrumentsLitigantIdList: [],
    instrumentsIdentityType: '',
    instrumentsFile: null,
    instrumentsTmplId: '',
    documentFileName: '',
    ifChooseAll: false,
		mediateCasePageBO:mediateCasePageBO.value,
    caseId: ''
  }
}

const baseInfoForm = ref<REQUEST_CASEDETAILS_DOC_SAVE_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_CREATEDOC_TEMPLATE_LIST_TYPE[]>([])
const tableSelectedKeys = ref<(string | number)[]>([])

const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const formRules = ref({
  instrumentsIdentityType: [{ required: true, message: '请选择当事人身份', trigger: 'blur' }],
  instrumentsLitigantIdList: [{ required: true, message: '请选择当事人', trigger: 'blur' }],
  instrumentsFile: [{ required: true, message: '请选择文书上传', trigger: 'blur' }]
})

const columns = computed<TableColumnData[]>(() => [
  { width: 100, tooltip: true, ellipsis: true, title: '文书模板名称', dataIndex: 'docTmplTitle' },
  { width: 100, tooltip: true, ellipsis: true, title: '关联模板', dataIndex: 'tmplTitle' }
])

const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const handleSaveModal = async () => {
  if (tableSelectedKeys.value.length === 0) {
    Message.error('请选择要制作的文书模板')
    return false
  }
  const state = await baseFormRef.value?.validate()
  if (!state) {
    okLoading.value = true
    // 生成方式-模板生成
    let param = { caseIdList: props.caseIds, docTmplId: tableSelectedKeys.value, ifChooseAll: props.ifChooseAll }
    batchGenerateSingleDoc(param)
      .then(() => {
        Message.success('操作成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
  tableSelectedKeys.value = []
}

const getCreateTemplateList = () => {
  if (!props.caseIds) return
  let param = { caseIdList: props.caseIds, ifChooseAll: props.ifChooseAll }
  if (props.ifChooseAll) param['mediateCasePageBO'] = mediateCasePageBO.value
  findSingleByCaseIds(param).then((res) => {
    renderData.value = res
  })
}

watch(
  () => visible.value,
  (val) => val && getCreateTemplateList()
)

defineExpose({ setFormModel, initFormModel })
</script>
<style lang="scss" scoped></style>
