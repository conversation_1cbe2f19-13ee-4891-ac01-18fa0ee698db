<template>
	<a-divider orientation="center" style="margin-top: 0; margin-bottom: 16px">
				<template v-if="selectedLabel">模板：{{selectedLabel}}</template>
	</a-divider>
	<a-form
		layout="vertical"
		:model="fieldForm"
		:label-col-props="{ span: 6 }"
		:wrapper-col-props="{ span: 18 }"
		label-align="left"
	>
		<a-grid :cols="3" :col-gap="16">
			<template v-for="module in fieldForm.customModules" :key="module.tmplModuleId">
				<a-grid-item v-for="field in module.fields" :key="field.tmplModuleFieldId">
					<a-form-item :label="field.fieldTitle">
						<template v-if="field.componentRelation=='ASelect'">
							<a-select
								v-model="field.fieldValue" :options="field.fieldValues"
								allow-clear @change="value => {field.isAbsent=value=='NULL'}"
							/>
						</template>
						<template v-else-if="field.componentRelation=='ASelectMultiple'">
							<a-select
								v-model="field.fieldValueList" :options="field.fieldValues"
								multiple allow-clear @change="value => {handelSelectMultiple(field,value)}"
							/>
						</template>
						<template v-else-if="field.componentRelation=='ADatePicker' || field.componentRelation=='ADateTimePicker'">
							<a-range-picker v-model="field.fieldValueList" class="percent-100" />
						</template>
						<template v-else-if="field.componentRelation=='AInputNumber'">
							<a-input-number v-model="field.fieldValue" allow-clear />
							&nbsp;至&nbsp;
							<a-input-number v-model="field.fieldValueSecond" allow-clear />
						</template>
						<template v-else-if="field.componentRelation=='ATextarea'">
							<a-textarea v-model="field.fieldValue" placeholder="请输入内容，支持模糊检索" allow-clear />
						</template>
						<template v-else>
							<a-input v-model="field.fieldValue" placeholder="请输入内容，支持模糊检索" allow-clear />
						</template>
					</a-form-item>
				</a-grid-item>
			</template>
		</a-grid>
	</a-form>


	<a-modal v-model:visible="customSearchModalModalVisible" @ok="handleSave" @before-open="beforeOpenHandle">
		<div class="flex items-center">
			<span class="f16 bold">筛选项显示</span>
			<a-tooltip position="bl">
				<template #content>
					<div>
						<div>仅可同时查询同一个案件模板下的案件信息，切换模板会导致历史已添加的查询条件自动清空。</div>
					</div>
				</template>
				<icon-exclamation-circle class="ml4" />
			</a-tooltip>
		</div>
		<a-form
			:model="modalForm"
			:label-col-props="{ span: 6 }"
			:wrapper-col-props="{ span: 18 }"
			label-align="left"
		>
			<a-row style="margin-bottom: 16px;margin-top: 10px">
				<a-form-item field="tmplCaseId" label="案件模板">
					<a-select v-model="modalForm.tmplCaseId" :options="tmplOptions" placeholder="请选择" allow-clear />
				</a-form-item>
			</a-row>
			<a-divider style="margin-top: 0; margin-bottom: 16px" />
			<a-select v-model="modalForm.selectFiledIds" multiple>
				<a-optgroup v-for="module in customModules" :key="module.tmplModuleId" :label="module.moduleTitle">
					<a-option v-for="field in module.moduleFields" :key="field.tmplModuleFieldId" :label="field.fieldTitle"
										:value="field.tmplModuleFieldId" />
				</a-optgroup>
			</a-select>
		</a-form>
	</a-modal>
</template>


<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
// 获取案件模板选项
import { getModuleFieldList } from '@/api/arcoApi/businessConf/caseTemplateManage.ts'
import { getAllTemplateList } from '@/api/arcoApi/caseManage/caseManage.ts'
import { useUserStore } from '@/layouts/appArco/store'
import { useSessionStorage } from '@vueuse/core'


//获取用户id,尝试初始化用户缓存配置
let accountId: string | null = null

interface modalFormModel {
	tmplCaseId?: number,
	selectFiledIds: []
}


//根据缓存更新表单
function recoverModalByLocal() {
	let itemLocal = localStorage.getItem(`caseCustomSearchFormLocal::${accountId}`)
	if (itemLocal) {
		modalForm.value = JSON.parse(itemLocal)
	}
}
function saveModalFormCacheLocal() {
	localStorage.setItem(`caseCustomSearchFormLocal::${accountId}`, JSON.stringify(modalForm.value))
}

//这里要保证，浏览器重新打开后，已选择的字段不丢失。而field则是保存了具体的字段值
//这里就不用useCore了。因为要考虑灵活性，modalForm弹窗在不点击保存的时候是要考虑下次打开复原的
const modalForm = ref<modalFormModel>({
	tmplCaseId: undefined,
	selectFiledIds: []
})

//字段查询表单,
//session缓存保证从详情页面返回后数据 每一个 value填写的值不丢失
const fieldForm = useSessionStorage<CASE_CUSTOM_MODULE_SEARCH_FORM_TYPE>('caseCustomSearchFormSession',{
	tmplCaseId: undefined,
	customModules: []
})

//转成查询api所需的表单结构
const getSearchCustomForm = (): CASE_CUSTOM_MODULE_SEARCH_FORM_TYPE => {

	let newItem = {} as CASE_CUSTOM_MODULE_SEARCH_FORM_TYPE
	let list = [] as CASE_CUSTOM_MODULE_SEARCH_FORM_MODULE_TYPE[]
	newItem.customModules = list
	for (let customModule of fieldForm.value.customModules) {
		let fields = customModule.fields.filter(field => field.isAbsent || (field.fieldValueList && field.fieldValueList.length > 0) || field.fieldValue || field.fieldValueSecond)
		if (fields.length > 0) {
			//如果字段有原始名就用原始名查询
			fields = fields.map(e => {
				if (e.originalName) {
					return { ...e, fieldTitle: e.originalName }
				}
				return { ...e }
			})
			list.push({
				...customModule,
				fields: fields
			})
		}
	}

	newItem.tmplCaseId = fieldForm.value.tmplCaseId
	return newItem
}


//初始化读取缓存
accountId = useUserStore().userInfo.accountId
//第一次加载时从缓存中恢复modal
recoverModalByLocal()


//只清理所有 input 的内容
const resetForm = function() {
	//重新构建一下，防止模板更改了
	createdFieldFormByModal()
	for (let customModule of fieldForm.value.customModules) {
		for (let field of customModule.fields) {
			field.fieldValue = undefined
			field.fieldValueSecond = undefined
			field.fieldValueList = undefined
			field.isAbsent = false
		}
	}
}


//模板的选项列表
const tmplOptions = ref<SelectOptionData[]>([])
const getTemplOptions = () => {
	getAllTemplateList().then((res) => {
		if (res && res.length) {
			tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
		}
	})
}

//当前选中的模板名称
const selectedLabel = computed(() => tmplOptions.value.find(e => e.value == modalForm.value.tmplCaseId)?.label)

//页面加载时加载
getTemplOptions();

//监听模板选择框改变
watch(() => modalForm.value?.tmplCaseId, async (newValue) => {
	if (!newValue) {
		return
	}
	//获取该模板的字段数据
	await getMainTmplInfo(newValue!)

	// 清空当前选择的字段
	modalForm.value.selectFiledIds = []
})


const beforeOpenHandle = async () => {
	//获取模板选项内容，因为此时有可能，已经选中一个模板id了
	if(modalForm.value.tmplCaseId){
		//获取该模板的字段数据
		await getMainTmplInfo(modalForm.value.tmplCaseId)
	}
	//将缓存中的modal恢复上去，因为用户可能上次没保存，下次打开时，要恢复
	recoverModalByLocal();
}


//用户点击保存时
const handleSave = () => {
	//赋值给查询列表
	fieldForm.value.tmplCaseId = modalForm.value.tmplCaseId

	createdFieldFormByModal()

	//保存该用户的查询偏好
	saveModalFormCacheLocal()
}




//模块以及所有字段,获取并构建一些映射做操作用
const customModules = ref<REQUEST_GET_MODULE_LIST_TYPE[]>([])
const moduleIdMap = new Map<number, REQUEST_GET_MODULE_LIST_TYPE>()
const fieldIdMap = new Map<number, REQUEST_GET_FIELD_LIST_TYPE>()
const getMainTmplInfo = async (id: string | number) => {
	let res = await getModuleFieldList(id)
	customModules.value = res.customModules

	//模块和字段id映射创建
	for (let customModules of res.customModules) {
		moduleIdMap[customModules.tmplModuleId] = customModules
		for (let moduleField of customModules.moduleFields) {
			fieldIdMap.set(moduleField.tmplModuleFieldId, moduleField)
		}
	}
}

//modal是否显示
const customSearchModalModalVisible = defineModel<boolean>('visible')

//根据modal中的选项id，重新构建界面的字段组
function createdFieldFormByModal() {
	//给modles赋值
	const moduleIdMap = new Map<number, CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE[]>

	//界面中有值的field，还保持原值
	const existFiledIdMap = fieldForm.value.customModules.reduce((acc, item) => {
		item.fields.forEach(field => {
			acc[field.tmplModuleFieldId] = field
		})
		return acc
	}, {})

	for (const filedId of modalForm.value.selectFiledIds) {
		if (!fieldIdMap.has(filedId)) {
			continue
		}
		let filedItem = fieldIdMap.get(filedId)
		const tmplModuleId = filedItem!.tmplModuleId
		if (!moduleIdMap.has(tmplModuleId)) {
			moduleIdMap.set(tmplModuleId, [])
		}

		//转成查询用的 item
		let newItem = { ...filedItem } as CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE
		//如果界面中已赋值，恢复该值
		if (newItem.tmplModuleFieldId in existFiledIdMap) {
			//filedItem value组的值必然是空，刚好可以更新其他属性保留value
			newItem = { ...existFiledIdMap[newItem.tmplModuleFieldId], ...filedItem } as CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE
		}


		let fieldValues = filedItem!.fieldValues
		if (fieldValues) {
			let _fieldValues = JSON.parse(fieldValues)
			if (_fieldValues && Object.prototype.toString.call(_fieldValues) === '[object Array]') {
				//所有选择条件增加空筛选
				let newOptions: OBJ_KEY_STR_ANY_TYPE = [{
					label: '空',
					value: 'NULL'
				}]
				for (let { value } of _fieldValues) {
					newOptions.push({
						'label': value,
						'value': value
					})
				}
				newItem.fieldValues = newOptions
			}
		}
		moduleIdMap.get(tmplModuleId)?.push(newItem)
	}

	//组装对象
	const modules: CASE_CUSTOM_MODULE_SEARCH_FORM_MODULE_TYPE[] = []

	for (let [key, value] of moduleIdMap) {
		modules.push(
			{ tmplModuleId: key, fields: value }
		)
	}

	fieldForm.value.customModules = modules
	fieldForm.value.tmplCaseId = modalForm.value.tmplCaseId

	console.log('自定义查询field已更新')
}




//自定义模块 field 用工具---------------------------
//select改变内容
function handelSelectMultiple(field: CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE, value: any[]) {
	if (value && value.includes('NULL')) {
		field.isAbsent = true
		if (value.length > 1) {
			//清理非空的其他元素
			field.fieldValueList = ['NULL']
		}

	} else {
		field.isAbsent = false
	}
}


defineExpose({
	getSearchCustomForm,
	resetForm
})
</script>


<style scoped lang="scss">

</style>
