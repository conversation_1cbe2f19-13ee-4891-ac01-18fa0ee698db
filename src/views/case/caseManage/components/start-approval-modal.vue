<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="auditFormRules"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="left"
      >
        <div v-if="isBatch" class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">案件信息：</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item :label-col-props="{ span: 2 }" :wrapper-col-props="{ span: 22 }" field="caseIdList">
                已选择案件数量：{{ ifChooseAll ? '全部案件' : `${caseIdList!.length} 件` }}
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div v-if="!isBatch" class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">办理类型</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="18">
              <a-form-item
                :label-col-props="{ span: 5 }"
                :wrapper-col-props="{ span: 19 }"
                field="approvalType"
                label=""
              >
                <a-radio-group v-model="baseInfoForm.approvalType" @change="handleSelectApprovalType" :options="filteredApprovalTypeOptions"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">办理信息</span>
          </div>
          <a-row :gutter="16">
						<template v-if="ApprovalStatusEnum.DELAY.code == baseInfoForm.approvalType">
							<a-col :span="24">
								<a-form-item
									:label-col-props="{ span: 5 }"
									:wrapper-col-props="{ span: 19 }"
									field="caseDelayTime"
									label="延期截止日期"
								>
									<a-date-picker v-model="baseInfoForm.caseDelayTime" style="width: 100%">
										<template #suffix-icon><icon-schedule /></template>
									</a-date-picker>
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item
									:label-col-props="{ span: 5 }"
									:wrapper-col-props="{ span: 19 }"
									field="delayReason"
									label="延期原因"
								>
									<a-textarea
										v-model="baseInfoForm.delayReason"
										:auto-size="{ minRows: 4 }"
										:max-length="250"
										placeholder="请输入内容"
										allow-clear
									/>
								</a-form-item>
							</a-col>
						</template>

						<template v-if="ApprovalStatusEnum.MEDIATE_CLOSE.code == baseInfoForm.approvalType">
							<a-col :span="24">
								<a-form-item
									:label-col-props="{ span: 4 }"
									:wrapper-col-props="{ span: 20 }"
									field="mdtResult"
									label="调解结果"
								>
									<a-radio-group v-model="baseInfoForm.mdtResult" @change="handleSelectMdtResult">
										<a-radio v-for="item in mediateResultOptions" :key="item.value" :value="item.value" v-show="item.value!='3' || suspendReasonSelectInstance?.dictDataTree.length>0">
											{{ item.label }}
										</a-radio>
									</a-radio-group>
								</a-form-item>
							</a-col>
							<a-col v-if="baseInfoForm.mdtResult=='1'" :span="13">
								<a-form-item field="successReason" label="结案方式">
									<DictDataSelect v-model="baseInfoForm.successReason" :dict-type="DictTypeEnum.case_success_reason" :business-type="caseContext.caseBasic.value.businessType" />
								</a-form-item>
							</a-col>

							<a-col v-if="baseInfoForm.mdtResult=='2'" :span="13">
								<a-form-item field="closeReason" label="终止原因">
									<DictDataSelect v-model="baseInfoForm.closeReason" :dict-type="DictTypeEnum.case_close_reason" :business-type="caseContext.caseBasic.value.businessType" />
								</a-form-item>
							</a-col>

							<a-col v-if="baseInfoForm.mdtResult=='3'" :span="13">
								<a-form-item field="suspendReason" label="中止原因">
									<DictDataSelect v-model="baseInfoForm.suspendReason" :dict-type="DictTypeEnum.case_suspend_reason" :business-type="caseContext.caseBasic.value.businessType" />
								</a-form-item>
							</a-col>
							<DictDataSelect style="display: none;" ref="suspendReasonSelectInstance" :dict-type="DictTypeEnum.case_suspend_reason" :business-type="caseContext.caseBasic.value.businessType" />
							<!-- <a-col v-if="!showDelayReason" :span="13">
								<a-form-item field="caseCloseTime" label="办结日期">
									<a-date-picker v-model="baseInfoForm.caseCloseTime" style="width: 100%">
										<template #suffix-icon><icon-schedule /></template>
									</a-date-picker>
								</a-form-item>
							</a-col> -->
						</template>

						<template v-if="ApprovalStatusEnum.CASE_MANAGE_CLOSE.code == baseInfoForm.approvalType">

							<a-col :span="15">
								<a-form-item field="caseManageCloseReason" label="案管办结">
									<DictDataSelect v-model="baseInfoForm.caseManageCloseReason" :dict-type="DictTypeEnum.case_manage_close_reason" :business-type="caseContext.caseBasic.value.businessType" />
								</a-form-item>
							</a-col>

						</template>

          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { RadioOption } from '@arco-design/web-vue/es/radio/interface'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
import { batchStartApproval, startAuditApproval } from '@/api/arcoApi/caseManage/approvalRecord.ts'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message, Modal } from '@arco-design/web-vue'
import { ref, computed, inject } from 'vue'
import dict from '@/dict/caseManage.ts'
import _ from 'lodash'
import { DictTypeEnum } from '@/dict/systemManage.ts'
import { ApprovalStatusEnum } from '@/dict/caseAuditManage.ts'

import { CaseContext, CaseContextKey, createDefaultCaseContext } from '@/views/case/caseDetails/context.ts'
const caseContext = inject<CaseContext>(CaseContextKey, createDefaultCaseContext())

const mediateCasePageBO = inject('mediateCasePageBO', null)

interface Props {
  width?: string | number
  caseId?: string | number
  title: string
  ifChooseAll?: boolean
  caseIdList?: any[]
	filterApprovalTypes?:number[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '700px',
  caseId: '',
  title: '',
	filterApprovalTypes:()=>[]
})

const isBatch = computed(() => {
  return props.caseIdList || props.ifChooseAll
})

const filteredApprovalTypeOptions = ApprovalStatusEnum.getOptions().filter(item => !props.filterApprovalTypes?.length||props.filterApprovalTypes.includes(item.value));

const emits = defineEmits(['confirm'])

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    successReason: '',
    approvalType: filteredApprovalTypeOptions?.length ? filteredApprovalTypeOptions[0].value : '',
    caseDelayTime: '',
    delayReason: '',
    closeReason: '',
    mdtResult: '1',
		suspendReason:'',
		caseManageCloseReason:'',
    caseId: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_APPROVAL_SAVE_PARAM_TYPE>(generateFormModel())


const mediateResultOptions = ref<RadioOption[]>(dict.mediateResultOptions)

const auditFormRules = ref({
  // caseCloseTime: [{ required: true, message: '请选择办结日期', trigger: 'blur' }],
  caseDelayTime: [{ required: true, message: '请选择延期截止日期', trigger: 'blur' }],
  successReason: [{ required: true, message: '请选择结案方式', trigger: 'blur' }],
  delayReason: [{ required: true, message: '请输入延期原因', trigger: 'blur' }],
  closeReason: [{ required: true, message: '请选择终止原因', trigger: 'blur' }],
  suspendReason: [{ required: true, message: '请选择中止原因', trigger: 'blur' }],
	caseManageCloseReason: [{ required: true, message: '请选择案管办结原因', trigger: 'blur' }]
})

const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)
const suspendReasonSelectInstance = ref<InstanceType<typeof DictDataSelect>>();


const handleSaveModal = async () => {

  const res = await baseFormRef.value?.validate()
  if (res) {
		console.log(res)
    return false
  }
  okLoading.value = true

  try {
    if (isBatch.value) {
      let formData = baseInfoForm.value as REQUEST_POST_BATCH_APPROVAL_START_PARAM_TYPE
      formData.ifChooseAll = props.ifChooseAll
      formData.mediateCasePageBO = mediateCasePageBO!['value']
      formData.caseIdList = props.caseIdList!

      await batchStartApproval(formData)
    } else {
      baseInfoForm.value.caseId = props.caseId
      await startAuditApproval(baseInfoForm.value)
    }

    Message.success('操作成功')
    visible.value = false
    initFormModel()

		emits('confirm')
  } catch (e: any) {
    //展示异常内容再弹框中
    Modal.error({
      title: '操作失败',
      content: e,
      bodyStyle: 'white-space:pre-wrap;max-height:500px'
    })
  } finally {
    okLoading.value = false
  }

  return false
}

const okClick = () => {}

const handleSelectMdtResult = (value: string | number | boolean) => {
	baseInfoForm.value = {
		...generateFormModel(),
		approvalType:baseInfoForm.value.approvalType,
		mdtResult:baseInfoForm.value.mdtResult
	}
}

const handleSelectApprovalType = (value: string | number | boolean) => {
	baseInfoForm.value = {
		...generateFormModel(),
		approvalType:baseInfoForm.value.approvalType,
		mdtResult:baseInfoForm.value.mdtResult
	}
}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_POST_APPROVAL_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
