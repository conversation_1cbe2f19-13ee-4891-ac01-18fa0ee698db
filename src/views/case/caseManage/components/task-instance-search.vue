<template>
	<div class="task-instance-search">
		<div class="form-title">
			<span class="title-text">辅助任务信息</span>
			<a-select
				v-model="modelValue.isExist"
				allow-clear
				placeholder="是否存在"
				:style="{width:'120px',fontWeight:'normal'}"
			>
				<a-option :value="0">不存在</a-option>
				<a-option :value="1">存在</a-option>
			</a-select>
		</div>
		<a-form
			:model="modelValue"
			:label-col-props="{ span: 7 }"
			:wrapper-col-props="{ span: 17 }"
			label-align="right"
		>
			<a-form-item field="assignAccountIdList" label="任务发起人">
				<DeptUserSelect
					v-model="modelValue.assignAccountIdList"
					multiple
					placeholder="请选择任务发起人"
				/>
			</a-form-item>

			<a-form-item field="taskTypes" label="任务类型">
				<DictDataSelect
					v-model="modelValue.taskTypes"
					:dict-type="DictTypeEnum.TASK_TYPE"
					:multiple="true"
					placeholder="请选择任务类型"
				/>
			</a-form-item>

			<a-form-item field="assignTime" label="分派登记时间">
				<DateRangePicker
					v-model:start-value="modelValue.assignTimeStart"
					v-model:end-value="modelValue.assignTimeEnd"
					show-time
					:time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
					class="percent-100"
				/>
			</a-form-item>

			<a-form-item field="deadline" label="截止时间">
				<DateRangePicker
					v-model:start-value="modelValue.deadlineStart"
					v-model:end-value="modelValue.deadlineEnd"
					show-time
					:time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
					class="percent-100"
				/>
			</a-form-item>

			<a-form-item field="managerIdList" label="任务负责人">
				<DeptUserSelect
					v-model="modelValue.managerIdList"
					multiple
					placeholder="请选择任务负责人"
				/>
			</a-form-item>
			<a-form-item field="taskStatuses" label="任务状态">
				<DictDataSelect
					v-model="modelValue.taskStatuses"
					:dict-type="DictTypeEnum.TASK_STATUS"
					placeholder="请选择任务状态"
					allow-clear
					multiple
					:max-tag-count="1"
				/>
			</a-form-item>

			<a-form-item field="finishAccountIdList" label="任务完成人">
				<DeptUserSelect
					v-model="modelValue.finishAccountIdList"
					multiple
					placeholder="请选择任务完成人"
				/>
			</a-form-item>

			<a-form-item field="finishTime" label="完成时间">
				<DateRangePicker
					v-model:start-value="modelValue.finishTimeStart"
					v-model:end-value="modelValue.finishTimeEnd"
					show-time
					:time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
					class="percent-100"
				/>
			</a-form-item>

			<a-form-item field="remark" label="备注">
				<a-input
					v-model="modelValue.remark"
					placeholder="请输入备注内容"
					allow-clear
				/>
			</a-form-item>
		</a-form>
	</div>
</template>

<script lang="ts" setup>
import DateRangePicker from '@/components/DateRangePicker/index.vue'
import DeptUserSelect from '@/components/deptUserSelect/index.vue'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
import { DictTypeEnum } from '@/dict/systemManage'


// 定义 props 和 emits
const modelValue = defineModel<TASK_INSTANCE_SEARCH_FORM_TYPE>({
	default: () => ({
			assignAccountIdList: [],
			taskTypes: [],
			assignTimeStart: undefined,
			assignTimeEnd: undefined,
			deadlineStart: undefined,
			deadlineEnd: undefined,
			managerIdList: [],
			taskStatuses: [],
			finishAccountIdList: [],
			finishTimeStart: undefined,
			finishTimeEnd: undefined,
			remark: undefined,
			isExist: undefined
		}
	)
})

// 重置表单
const resetForm = () => {
	modelValue.value ={
		assignAccountIdList: [],
		taskTypes: [],
		assignTimeStart: undefined,
		assignTimeEnd: undefined,
		deadlineStart: undefined,
		deadlineEnd: undefined,
		managerIdList: [],
		taskStatuses: [],
		finishAccountIdList: [],
		finishTimeStart: undefined,
		finishTimeEnd: undefined,
		remark: undefined,
		isExist: undefined
	}

}

// 暴露方法
defineExpose({
	resetForm
})
</script>

<style lang="scss" scoped>
.task-instance-search {
	.form-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-left: 4px solid $color-primary;
		font-size: 15px;
		font-weight: bold;
		padding-left: 10px;
		margin-bottom: 16px;
		min-height: 20px;

		.title-text {
			flex: 1;
		}


	}
}
</style>
