<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="modalForm"
        :rules="formRules"
        :label-col-props="{ span: 7 }"
        :wrapper-col-props="{ span: 17 }"
        label-align="right"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">案件信息：</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item :label-col-props="{ span: 2 }" :wrapper-col-props="{ span: 22 }" field="caseIds">
                已选择案件数量：{{ ifChooseAll ? '全部案件' : `${caseIds.length} 件` }}
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">任务信息：</span>
          </div>
          <a-form-item field="taskAccountType" label="选择任务：">
            <a-select v-model="modalForm.taskAccountType" placeholder="请选择" allow-clear>
              <a-option
                v-for="item in taskTypeList"
                :key="item.user"
                :label="item.type"
                :value="item.user"
                @click="modalForm.taskStatusType = item.status"
              />
            </a-select>
          </a-form-item>
          <a-form-item field="accountId" label="任务处理人：">
            <dept-user-select v-model="modalForm.accountId" />
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { FormInstance } from '@arco-design/web-vue/es/form'
import type { Ref } from 'vue'

import { bathTaskAssign } from '@/api/arcoApi/caseManage/taskState.ts'
import DeptUserSelect from '@/components/deptUserSelect/index.vue'

import { taskTypeList } from '@/dict/caseManage.ts'
import { Message } from '@arco-design/web-vue'
import { inject, ref } from 'vue'

interface Props {
  caseIds: (string | number)[]
  width?: string | number
  ifChooseAll?: boolean
  title: string
}

const mediateCasePageBO = inject('mediateCasePageBO') as Ref<OBJ_KEY_STR_ANY_TYPE>

const props = withDefaults(defineProps<Props>(), {
  caseIds: () => [],
  ifChooseAll: false,
  width: '600px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    ifChooseAll: false,
    mediateCasePageBO: mediateCasePageBO.value,
    caseIdList: []
  }
}

const baseInfoForm = ref<REQUEST_POST_BATCH_TASK_ASSIGN_PARAM_TYPE>(generateFormModel())

const formRules = ref({
  taskAccountType: [{ required: true, message: '请选择任务', trigger: 'blur' }],
  accountId: [{ required: true, message: '请选择任务处理人', trigger: 'blur' }]
})
const generateModalFormModel = () => {
  return {
    taskAccountType: undefined,
    taskStatusType: undefined,
    accountId: undefined
  }
}
const modalForm = ref(generateModalFormModel())

const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const handleSaveModal = async () => {
  const state = await baseFormRef.value?.validate()
  if (!state) {
    okLoading.value = true
    baseInfoForm.value.caseIdList = props.caseIds
    baseInfoForm.value.ifChooseAll = props.ifChooseAll
    baseInfoForm.value.mediateCasePageBO = props.ifChooseAll ? mediateCasePageBO.value : {}

    if (modalForm.value.taskAccountType) {
      //赋值对应的key
      baseInfoForm.value[modalForm.value.taskAccountType] = modalForm.value.accountId
      baseInfoForm.value[modalForm.value.taskStatusType!] = 1
    }
    bathTaskAssign(baseInfoForm.value)
      .then(() => {
        Message.success('任务分派成功')
        emits('comfirm')
        initFormModel()
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  modalForm.value = generateModalFormModel()
  baseFormRef.value?.resetFields()
}

defineExpose({ initFormModel })
</script>

<style lang="scss" scoped></style>
