<template>
  <div class="common-page container">
    <div class="content">
      <a-result class="result" status="404" subtitle="抱歉，页面消失了～"> </a-result>
      <div class="operation-row">
        <a-space :size="10" fill>
          <a-button key="again" @click="again">重试</a-button>
          <a-button key="back" type="primary" @click="back">返回</a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';

const route = useRoute()
const router = useRouter()

const again = () => {
  route.redirectedFrom && route.redirectedFrom.name && router.push({name: route.redirectedFrom.name})
}

const back = () => {
  router.back()
}
</script>

<script lang="ts">
export default { name: '404' }
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  :deep(.content) {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    background-color: var(--color-bg-1);
    border-radius: 4px;
  }
}
</style>
