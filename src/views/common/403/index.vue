<template>
  <div class="common-page container">
    <div class="content">
      <a-result class="result" status="403" subtitle="抱歉，你没有访问的权限，请联系管理员～" />
      <a-button key="back" type="primary" @click="back">返回</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const back = () => {
  router.back()
}
</script>

<script lang="ts">
export default {
  name: '403'
}
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  :deep(.content) {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    background-color: var(--color-bg-1);
    border-radius: 4px;
  }
}
</style>
