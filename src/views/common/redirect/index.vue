<template>
  <div></div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const gotoPath = route.params.path as string

const queryPath = gotoPath.split('?')[1]

function getQueryParams(path: string) {
  const serchParams = new URLSearchParams(path)
  const params: {[x:string]: string} = {}
  for(const [key, value] of serchParams.entries()) {
    params[key] = value
  }
  return params
}

router.replace({ path: gotoPath, query: getQueryParams(queryPath) })
</script>

<style scoped lang="scss"></style>
