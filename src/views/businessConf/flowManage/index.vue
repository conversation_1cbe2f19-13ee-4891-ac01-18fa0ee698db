<template>
	<div class="template-list-container common-page">
		<a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
			<template #title>
				<span class="bold">{{ lisPageTitle }}</span>
			</template>
			<a-row>
				<a-col :flex="1">
					<a-form
						ref="searchFormRef"
						:model="searchForm"
						:label-col-props="{ span: 8 }"
						:wrapper-col-props="{ span: 16 }"
						label-align="left"
					>
						<a-row :gutter="16">
							<a-col :span="8">
								<a-form-item field="workflowName" label="任务流程名称">
									<a-input v-model="searchForm.workflowName" placeholder="请输入内容" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="entrustsId" label="案源方">
									<a-select
										v-model="searchForm.entrustsId"
										:options="entrustsOptions"
										placeholder="请选择"
										allow-clear
										@change="handleEntrustsChange"
									/>
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="templateId" label="案件信息模板">
									<a-select v-model="searchForm.templateId" :options="tmplOptions" placeholder="请选择" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="status" label="流程状态">
									<a-select v-model="searchForm.status" :options="flowStatusOptions" placeholder="请选择" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="taskType" label="涉及任务类型">
                  <a-select
                    multiple
                    v-model="searchForm.taskType"
                    :options="taskTypeOptions"
                    placeholder="请选择"
                  />
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item field="mdtEntrustOrgUserIds" label="涉及任务负责人">
									<a-select
										v-model="searchForm.mdtEntrustOrgUserIds"
										:options="mdtOrgUsersOptions"
										placeholder="请选择"
										allow-clear
										multiple
										:max-tag-count="1"
									/>
								</a-form-item>
							</a-col>
						</a-row>
					</a-form>
				</a-col>
				<a-divider style="height: 84px" direction="vertical" />
				<a-col :flex="'86px'" style="text-align: right">
					<a-space direction="vertical" :size="18">
						<a-button type="primary" @click="search">
							<template #icon>
								<icon-search />
							</template>
							查询
						</a-button>
						<a-button @click="reset">
							<template #icon>
								<icon-refresh />
							</template>
							重置
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-divider style="margin: 0 0 16px 0" />
			<a-row v-auth="['workflowCreate']" style="margin-bottom: 16px">
				<a-col :span="24">
					<a-space>
						<a-button type="primary" @click="handleInfoAdd">
							<template #icon>
								<icon-plus />
							</template>
							新建
						</a-button>
					</a-space>
				</a-col>
			</a-row>
			<a-table
				row-key="id"
				:loading="loading"
				:pagination="pagination"
				:columns="cloneColumns"
				:data="renderData"
				:bordered="false"
				:pageize="size"
				@page-change="onPageChange"
				@page-size-change="onPageSizeChange"
			>
				<template #status="{ record }">
					<a-switch v-model="record.status" :disabled="true"></a-switch>
				</template>
				<template #operations="{ record }">
					<a-button v-auth="['workflowConfig']" type="text" @click="handleConfig(record)">配置</a-button>
					<a-button v-auth="['taskWorkFlowDelete']" type="text" @click="handleDelete(record)">删除</a-button>
					<a-button v-auth="['taskWorkFlowSave']" type="text" @click="handleEdit(record)">编辑</a-button>
				</template>
			</a-table>
<!--			<a-modal v-model:visible="modalVisible" :footer="false" width="600px" title="模板信息编辑" title-align="start">-->
<!--				<BaseInfo-->
<!--					ref="baseInfoRef"-->
<!--					comfirm-text="保存"-->
<!--					:disabled-select="true"-->
<!--					:tmpl-options="tmplOptions"-->
<!--					:entrusts-options="entrustsOptions"-->
<!--					:nature-title-options="natureTitleOptions"-->
<!--					@comfirm="handleComfirm"-->
<!--					@cancel="handleCancel"-->
<!--				/>-->
<!--			</a-modal>-->
			<FormInfoModal
				ref="formInfoRef"
				v-model:visible="editModalVisible"
				:mdt-tmpl-options="tmplOptions"
				:mdt-status-options="flowStatusOptions"
				:mdt-entrusts-options="entrustsOptions"
				:title="modalTitle"
				@comfirm="handleComfirm"
				@cancel="handleCancel"
			/>
		</a-card>
	</div>
</template>

<script lang="ts" setup>
import {
	getEntrustsList,
	deleteEntrusts,
	getEntrustsDetails,
	getChargePerson, getChargeAllPerson
} from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import { getAllMdtOrg } from '@/api/arcoApi/businessConf/mediateOrgInfoManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { computed, nextTick, ref, reactive, watch } from 'vue'
import FormInfoModal from './components/form-info-modal.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import _ from 'lodash'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage.ts'
import { deputyTmplList, getSelectTemplateList } from '@/api/arcoApi/businessConf/caseTemplateManage.ts'
import { deleteWorkFlow, getFlowList, getWorkFlowDetails } from '@/api/arcoApi/taskWorkFlow/taskWorkFlow.ts'
import CreateIndex from './create/index.vue'
import BaseInfo from '@/views/businessConf/templateManage/caseInfoTemplate/create/components/base-info.vue'
import { useRouter } from 'vue-router'
import { getTargetDict } from '@/api/eleApi/common.ts'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }
const router = useRouter()
const generateFormModel = () => {
	return {
		contactsName: '',
		creatorName: '',
		endTime: '',
		workflowName: '',
		entrustsId: '',
		status: '',
		relaMdtOrgIdList: [],
		startTime: '',
		allTime: []
	}
}

const { loading, setLoading } = useLoading(true)
const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('任务流程')
// const pageEnName = ref('Entrusts')

const searchForm = ref<{}>(generateFormModel())
const searchFormRef = ref<FormInstance>()
const renderData = ref<[]>([])
const formInfoRef = ref<InstanceType<typeof FormInfoModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const taskTypeOptions = ref<SelectOptionData[]>([])
const modalVisible = ref(false)
const editModalVisible = ref(false)
// 涉及任务负责人，数组类型
const mdtOrgUsersOptions = ref<SelectOptionData[]>([])

const basePagination = {
	pageSizeOptions: [10, 20, 30, 50, 100],
	showPageSize: true,
	showJumper: true,
	showTotal: true,
	pageSize: 10,
	current: 1,
	total: 0
}

const pagination = reactive({ ...basePagination })
const entrustsOptions = ref<SelectOptionData[]>([])
const tmplOptions = ref<SelectOptionData[]>([])

const columns = computed<TableColumnData[]>(() => [
	{ width: 140, align: 'center', tooltip: true, ellipsis: true, title: '编号', dataIndex: 'workflowId' },
	{ width: 200, title: '任务流程名称', dataIndex: 'workflowName', slotName: 'workflowName', align: 'center' },
	{ width: 120, tooltip: true, ellipsis: true, title: '案源方', dataIndex: 'entrustsName', align: 'center' },
	{ width: 120, tooltip: true, ellipsis: true, title: '案件信息模板', dataIndex: 'templateName', align: 'center' },
	{ width: 120, tooltip: true, ellipsis: true, title: '更新人', dataIndex: 'updaterName', align: 'center' },
	{ width: 120, align: 'center', title: '流程状态', dataIndex: 'status', slotName: 'status' },
	{ width: 120, align: 'center', tooltip: true, ellipsis: true, title: '备注', dataIndex: 'remark' },
	{ width: 180, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])
const flowStatusOptions = ref<SelectOptionData[]>([
	{ label: '启用', value: 1 },
	{ label: '停用', value: 0 }
])
const modalTitle = computed(() => {
	return `${pageZhName.value}信息${modalType.value === 'add' ? '创建' : '编辑'}`
})


const handleInfoAdd = () => {
	router.push({ name: 'workflowCreate' })
}

const search = () => {
	pagination.current = 1
	getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
	// 重置表单
	searchFormRef.value?.resetFields()
}

const onPageChange = (current: number) => {
	getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
	pagination.current = 1
	getTableData({ ...pagination, pageSize })
}
const handleConfig = (row) => {
	let id = row.workflowId
	router.push({ path: `workflowConfig/${id}`})
}
// 获取任务类型字典数据方法
const getTaskTypes = () => {
  getTargetDict({ type: 'task_type' }).then((res) => {
    if (res && res.sysDictDataList.length>0) {
      taskTypeOptions.value = res.sysDictDataList.map((item) => ({ label: item.dictTag, value: item.dictKey }))
    }
  })
}
const handleDelete = (row) => {
	Modal.warning({
		title: '提示',
		hideCancel: false,
		alignCenter: true,
		content: `是否确认删除“${row.workflowName}”任务流程吗？`,
		onOk: () => {
			deleteWorkFlow(row.workflowId)
				.then(() => {
					Message.success('操作成功')
					getTableData()
				})
				.catch(() => {
					Modal.error({
						title: '删除失败'
					})
				})
		},
		onCancel: () => {
		}
	})
}

const handleEdit = async (row: REQUEST_GET_TASKWORKFLOW_DATA) => {
	if (row.entrustsId) {
		let workflow = await getWorkFlowDetails(row.workflowId)
		if (workflow) {
			let params = { tmplStatus: 1, tmplType: '2', entrustsId: row.entrustsId }

			let result = await deputyTmplList(params)
			if (result && result.length) {
				tmplOptions.value = result.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
			}
			modalType.value = 'edit'
			editModalVisible.value = true
			nextTick(() => {
				formInfoRef.value?.setFormModel(workflow)
			})
		}
	}
}

const handleEntrustsChange = () => {
	searchFormRef.value?.resetFields(['templateId'])
	tmplOptions.value = []
	if (searchForm.value.entrustsId) {
		getTemplOptions()
	}
}

const getAllEntrusts = () => {
	findAllEntrusts().then((res: unknown) => {
		let data = res as any[]
		if (data && data.length) {
			entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
		}
	})
}

const getTemplOptions = () => {
	let params = { tmplStatus: 1, tmplType: '2', entrustsId: searchForm.value.entrustsId }
	getSelectTemplateList(searchForm.value.entrustsId).then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
    }
  })
}

const handleComfirm = () => {
	modalVisible.value = false
	editModalVisible.value = false
	getTableData()
}
const handleCancel = () => {
	modalVisible.value = false
	editModalVisible.value = false
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
	setLoading(true)

	try {
		const data = await getFlowList({
			pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
			param: searchForm.value
		})
		pagination.pageSize = pageInfo.pageSize
		pagination.current = pageInfo.current
		pagination.total = data.total
		renderData.value = data.list
	} catch (err) {
		// 输出err
		console.log(err)
	} finally {
		setLoading(false)
	}
}

const getAllMdtOrgUserData = () => {
	getChargeAllPerson().then((res) => {
		if (res && res.length) {
			mdtOrgUsersOptions.value = res.map((item) => ({ label: item.name, value: item.accountId }))
		}
	})
}

getAllMdtOrgUserData()
getTableData()
getAllEntrusts()
getTaskTypes()

watch(
	() => columns.value,
	(val) => {
		cloneColumns.value = _.cloneDeep(val)
		cloneColumns.value.forEach((item) => {
			item.checked = true
		})
		showColumns.value = _.cloneDeep(cloneColumns.value)
	},
	{ deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
	&:last-child {
		.arco-table-th-item-title {
			margin-left: 16px;
		}
	}
}
</style>
