<template>
  <div class="template-conf-page">
    <a-card :bordered="false" class="template-conf-card template-select-card">
      <template #title>
        <span class="bold">任务流程信息</span>
      </template>

      <a-row>
        <a-col :flex="1">
          <a-form
            :model="workflowInfo"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
            :disabled="true"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="workflowName" label="任务流程名称">
                  <a-input v-model="workflowInfo.workflowName" placeholder="请输入内容" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsName" label="案源方">
                  <a-select v-model="workflowInfo.entrustsName" :options="entrustsOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="templateName" label="案件信息模板">
                  <a-select v-model="workflowInfo.templateName" :options="tmplTypeOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="流程状态">
                  <a-select v-model="workflowInfo.status" :options="statusOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="triggerCondition" label="流程启动条件">
                  <a-radio-group v-model="workflowInfo.triggerCondition">
                    <a-radio :value="1">案件导入</a-radio>
                    <a-radio :value="2">案件分派组织</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </a-row>
    </a-card>
    <a-spin :loading="loading" class="spin-content">
      <a-card :bordered="false" class="template-conf-card template-select-card">
        <template #title>任务配置</template>
        <div class="module-tree-box">
          <a-row type="flex" justify="end">
            <a-button type="primary" @click="addTask">新增任务</a-button>
          </a-row>
          <a-row>
            <a-table
              row-key="id"
              :loading="loading"
              :columns="columns"
              :data="renderData"
              :bordered="false"
              :pageize="size"
            >
              <template #trigger="{ record }">
                <a-button v-auth="['entrustDelete']" type="text" @click="handleConfig(record)">触发配置</a-button>
              </template>
              <template #operations="{ record }">
								<a-button v-auth="['entrustSave']" type="text" @click="handleEdit(record)">编辑</a-button>
                <a-button v-auth="['entrustDelete']" type="text" @click="handleDelete(record)">删除</a-button>
              </template>
            </a-table>
          </a-row>
        </div>
      </a-card>
    </a-spin>
    <a-modal
      v-model:visible="configModalVisible"
      :footer="false"
      width="600px"
      :title="dialogTitle"
      title-align="start"
    >
      <BaseInfo
        ref="baseInfoRef"
        comfirm-text="确定"
        :disabled-select="true"
        :entrusts-id="workflowInfo.entrustsId"
        :workflow-id="workflowId"
        :task-type-options="taskTypeOptions"
        :mdt-org-users-options="mdtOrgUsersOptions"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-modal>
    <a-modal v-model:visible="triggerModalVisible" :footer="false" width="1000px" title="触发条件" title-align="start">
      <TriggerModal
        ref="triggerRef"
        comfirm-text="确定"
        :disabled-select="true"
        :entrusts-id="workflowInfo.entrustsId"
        :template-id="workflowInfo.templateId"
        :workflow-id="workflowId"
        :config-id="configId"
        :task-type-options="taskTypeOptions"
        :mdt-org-users-options="mdtOrgUsersOptions"
				:template-options="templateOptions"
				:field-type-map="fieldTypeMap"
				:modules-map="modulesMap"
				:system-dict-map="systemDictMap"
        @comfirm="handleTriggerModalComfirm"
        @cancel="handleTriggerModalCancel"
      />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { getDictList, getTargetDict } from '@/api/eleApi/common.ts'
import { getWorkFlowDetails } from '@/api/arcoApi/taskWorkFlow/taskWorkFlow.ts'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import { Message, Modal, SelectOptionGroup, TreeInstance } from '@arco-design/web-vue'

import TriggerModal from './trigger.vue'
import { useTabBarStore } from '@arco/store'
import { useRoute, useRouter } from 'vue-router'
import { computed, nextTick, ref, watch } from 'vue'
import dict from '@/dict/businessConf.ts'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import _ from 'lodash'
import { deleteConfig, getConfigDetails, getConfigList } from '@/api/arcoApi/taskWorkFlow/taskConfig.ts'
import { getChargeAllPerson, getChargePerson } from '@/api/arcoApi/businessConf/caseSourceInfoManage.ts'
import BaseInfo from './base-info.vue'
import { Column } from 'element-plus'
import { getTaskTriggerConditionGroup } from '@/api/arcoApi/taskWorkFlow/taskTriggerCondition.ts'
import { getModuleFieldList } from '@/api/arcoApi/businessConf/caseTemplateManage.ts'
import { getMediateStructure } from '@/api/eleApi/system/structManage.ts'

const route = useRoute()
const router = useRouter()

const routerParams = route.params

const tabBarStore = useTabBarStore()

const loading = ref(false)
const configModalVisible = ref(false)
const triggerModalVisible = ref(false)
const dialogTitle = ref<string>('新增任务')

const cloneColumns = ref<Column[]>([])
const renderData = ref<[]>([])
type SizeProps = 'mini' | 'small' | 'medium' | 'large'
const size = ref<SizeProps>('medium')
// 所有案源方信息
const orgUsersOptions = ref<SelectOptionData[]>([])
const orgUserList = ref([])
// 任务类型
const taskTypeOptions = ref<SelectOptionData[]>([])
const workflowId = ref<number>()
const configId = ref<number>()
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const triggerRef = ref<InstanceType<typeof TriggerModal>>()
const workflowInfo = ref<REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE>({
  triggerCondition: 1,
  entrustsName: '',
  isDesensitize: null,
  natureTitle: '',
  updaterName: '',
  entrustsId: '',
  tmplStatus: '',
  tmplTitle: '',
  companyId: '',
  tmplDesc: '',
  tmplType: '',
  tmplId: ''
})

const columns = computed<TableColumnData[]>(() => [
	{
		width: 40,
		align: 'center',
		title: '配置编号',
		dataIndex: 'configId'
	},
  {
    width: 140,
    align: 'center',
    title: '任务类型',
    dataIndex: 'taskType',
    render: ({ record }) => {
      // 在options中查找匹配的label
      const option = taskTypeOptions.value.find((opt) => opt.value === record.taskType)
      return option ? option.label : '未知类型'
    }
  },
  {
    width: 140,
    title: '负责人',
    dataIndex: 'managerIdList',
    align: 'center',
    render: ({ record }) => {
      // 将数组中的每个ID转换为对应的用户名
      const names = record.managerIdList.map((id) => {
        const user = orgUserList.value.find((u) => u.accountId === id)
        return user ? user.employeeName  : '未知用户'
      })
      // 用逗号分隔并显示
      return names.join(', ')
    }
  },
  { width: 140, tooltip: true, ellipsis: true, title: '任务时限（时）', dataIndex: 'timeLimit', align: 'center' },
  {
    width: 140,
    tooltip: true,
    ellipsis: true,
    title: '触发条件',
    dataIndex: 'trigger',
    align: 'center',
    slotName: 'trigger'
  },
  { width: 180, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' },
  // { width: 180, align: 'center', title: '操作', dataIndex: 'configId' },
])

const statusOptions = ref<SelectOptionData[]>([
  { label: '已启用', value: true },
  { label: '已禁用', value: false }
])

const tmplTypeOptions = ref<SelectOptionData[]>(dict.tmplTypeOptions)

const entrustsOptions = ref<SelectOptionData[]>([])
const mdtOrgUsersOptions = ref<SelectOptionData[]>([])

const addTask = async () => {
  dialogTitle.value = '新增任务'
  let res = await getChargePerson(workflowInfo.value.entrustsId)
  if (res && res.length) {
    mdtOrgUsersOptions.value = res.map((item) => ({ label: item.name, value: item.accountId }))
  }
	baseInfoRef.value?.setFormModel(null)
  configModalVisible.value = true
}
// 生成handleConfig 方法
const handleConfig = (record: any) => {
  // 通过id获取详情
  getTaskTriggerConditionGroup(record.configId)
    .then((res) => {
        // 通过id获取详情
        triggerRef.value?.setFormModel(res)
      triggerModalVisible.value = true
      configId.value = record.configId
    })
    .catch(() => {
      Message.error('获取详情失败')
    })
}
// 生成 handleDelete 方法
const handleDelete = (record: any) => {
  // 询问是否确认删除
  Modal.warning({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `请确认是否删除`,
    onOk: () => {
      deleteConfig(record.configId)
        .then(() => {
          Message.success('操作成功')
          getTableData()
        })
        .catch(() => {
          Message.error('删除失败')
        })
    },
    onCancel: () => {}
  })
}

const handleEdit = (record: any) => {
  dialogTitle.value = '编辑任务'
  // 通过id获取详情
  getConfigDetails(record.configId)
    .then((res) => {
      if (res) {
        // 通过id获取详情
        baseInfoRef.value?.setFormModel(res)
        configModalVisible.value = true
      }
    })
    .catch(() => {
      Message.error('获取详情失败')
    })
}

const handleComfirm = (str: string) => {
  configModalVisible.value = false
  if (str === 'submit') {
    getTableData()
  }
}

const handleCancel = () => {
  configModalVisible.value = false
}
const handleTriggerModalComfirm = (str: string) => {
  triggerModalVisible.value = false
  if (str === 'submit') {
    getTableData()
  }
}
const handleTriggerModalCancel = () => {
  triggerModalVisible.value = false
}
const systemDictMap= ref(new Map())
const getAllDict = () => {
	getDictList().then((res) => {
		systemDictMap.value = new Map()
		res.forEach((item) => {
			let list = []
			item.sysDictDataList.forEach((item) => {

				list.push({ label: item.dictTag, value: `${item.dictKey}` })
			})
			systemDictMap.value.set(item.dictType, list)
		})
	})
}
// 获取任务类型字典数据方法
const getTaskTypes = () => {
  getTargetDict({ type: 'task_type' }).then((res) => {
    if (res && res.sysDictDataList.length > 0) {
      taskTypeOptions.value = res.sysDictDataList.map((item) => ({ label: item.dictTag, value: item.dictKey }))
    }
  })
}

// 编写获取表格数据方法
const getTableData = () => {
  getConfigList(workflowId.value).then((res) => {
    renderData.value = res
  })
}
// key为模块id，value为该模块下的字段列表
const modulesMap = ref(new Map())
// 字段类型map
const fieldTypeMap = ref(new Map())
// 定义模板数据结构，供a-select使用
const templateOptions = ref<SelectOptionGroup[]>([])
// 定义 获取模板数据方法
const getTemplateData = () => {
	getModuleFieldList(workflowInfo.value.templateId).then((res) => {
		fieldTypeMap.value = new Map()
		modulesMap.value = new Map()
		// 构建字段列表
		const { systemModules } = res
		// 如果 systemModules 不为空列表
		if (systemModules && systemModules.length > 0) {
			let options = []
			systemModules.forEach((item) => {
				let itemFields = new Array()
				// 如果 item.moduleFields 不为空列表
				if (item.moduleFields && item.moduleFields.length > 0) {
					// 遍历 item.moduleFields
					item.moduleFields.forEach((field) => {
						// 构建第一列
						itemFields.push({ value: field.tmplModuleFieldId, label: field.fieldTitle })
						// 将二级每个字段的类型存储
						fieldTypeMap.value[field.tmplModuleFieldId] = {
							data_type: field.fieldDataType,
							dict_values: field.fieldValues,
							dict_type: field.fieldDictType
						}
					})
				}
				modulesMap.value[item.tmplModuleId] = itemFields
				// 构建第一列
				options.push({ label: item.moduleTitle, value: item.tmplModuleId })
			})
			templateOptions.value.push({ label: '系统模块', options: options, isGroup: true })
		}
		const { businessModules } = res
		if (businessModules && businessModules.length > 0) {
			let options = []
			businessModules.forEach((item) => {
				let itemFields = new Array()
				// 如果 item.moduleFields 不为空列表
				if (item.moduleFields && item.moduleFields.length > 0) {
					// 遍历 item.moduleFields
					item.moduleFields.forEach((field) => {
						itemFields.push({ value: field.tmplModuleFieldId, label: field.fieldTitle })
						// 将二级每个字段的类型存储
						fieldTypeMap.value[field.tmplModuleFieldId] = {
							data_type: field.fieldDataType,
							dict_values: field.fieldValues,
							dict_type: field.fieldDictType
						}
					})
				}
				modulesMap.value[item.tmplModuleId] = itemFields
				// 构建第一列
				options.push({ label: item.moduleTitle, value: item.tmplModuleId })
			})
			templateOptions.value.push({ label: '业务模块', options: options, isGroup: true })
		}
		const { customModules } = res
		if (customModules && customModules.length > 0) {
			let options = []
			customModules.forEach((item) => {
				let itemFields = new Array()
				// 如果 item.moduleFields 不为空列表
				if (item.moduleFields && item.moduleFields.length > 0) {
					// 遍历 item.moduleFields
					item.moduleFields.forEach((field) => {
						console.log(field.fieldTitle)
						itemFields.push({ value: field.tmplModuleFieldId, label: field.fieldTitle })
						// 将二级每个字段的类型存储
						fieldTypeMap.value[field.tmplModuleFieldId] = {
							data_type: field.fieldDataType,
							dict_values: field.fieldValues,
							dict_type: field.fieldDictType
						}
					})
				}
				modulesMap.value[item.tmplModuleId] = itemFields
				// 构建第一列
				options.push({ label: item.moduleTitle, value: item.tmplModuleId })
			})
			templateOptions.value.push({ label: '自定义模块', options: options, isGroup: true })
		}
	})
}

const init = async () => {
  getTaskTypes()
	getAllDict()
  if (routerParams.workflowId) {
    workflowId.value = Number(routerParams.workflowId)
    getWorkFlowDetails(workflowId.value).then((res) => {
      workflowInfo.value = res
			getTemplateData()
    })
    getTableData()
    // 获取案源方及组织下的用户
    // getChargeAllPerson().then((res) => {
    //   if (res && res.length) {
    //     orgUsersOptions.value = res.map((item) => ({ label: item.name, value: item.accountId }))
    //   }
    // })
		getMediateStructure().then((res)=>{
			res.forEach(dept=>{
				dept.deptEmployeeDTOList.forEach(empl=>{
					empl.employeeDTOList.forEach(em=>{
						orgUserList.value.push(em)
					})
				})
			})
		})
  }

}
init()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    // showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.template-conf-page {
  width: 100%;
  background-color: var(--color-fill-2) !important;

  :deep(.arco-virtual-list) {
    @include scrollBar;
  }
}

.spin-content {
  margin-top: 20px;
  display: flex;

  .template-select-card {
    width: 100%;
    height: 65vh;
    background-color: var(--color-bg-1);

    :deep(.arco-card-body) {
      height: calc(100% - 46px);
      width: 100%;
      display: flex;
      flex-flow: column;

      .module-tree-box {
        flex: 1;

        .module-tree-text {
          display: inline-block;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .arco-row-justify-start {
          margin-top: 5px;
        }
      }

      .module-icon-box {
        position: absolute;
        right: 8px;

        .module-icon {
          font-size: 14px;
          color: var(--color-fill-4);

          &-edit {
            margin: 0 5px;
          }

          &:hover {
            color: var(--color-text-3);
          }
        }
      }

      .module-tree-operation {
        height: 48px;
        margin: 0 auto;
        line-height: 48px;

        .template-module-add-btn {
          width: 212px;
          height: 44px;
        }
      }
    }
  }

  .template-select-table {
    padding: 20px;
    height: 65vh;
    margin-left: 10px;
    width: calc(100% - 260px);
    background-color: var(--color-bg-1);
  }

  .template-select-table-box {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }
}
</style>
