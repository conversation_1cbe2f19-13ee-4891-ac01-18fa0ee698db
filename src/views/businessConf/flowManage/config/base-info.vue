<template>
  <a-spin :loading="loading">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col-props="{ span: 8 }"
      :wrapper-col-props="{ span: 16 }"
      class="form"
    >
      <a-form-item field="taskType" label="任务类型">
        <a-select :disabled="updateFlag" v-model="formData.taskType" :options="taskTypeOptions" placeholder="请选择" />
      </a-form-item>
      <a-form-item field="timeLimit" label="任务时限（时）">
        <a-input-number v-model="formData.timeLimit" :min="1" allow-clear></a-input-number>
      </a-form-item>

      <a-form-item field="managerIdList" label="任务负责人">
<!--        <a-select v-model="formData.managerIdList" :options="mdtOrgUsersOptions" placeholder="请选择" multiple />-->
				<DeptUserSelect v-model="formData.managerIdList" multiple placeholder="请选择任务负责人" />
      </a-form-item>
      <a-form-item class="base-form-justify">
        <a-space :size="20">
          <a-button @click="onCancelClick">取消</a-button>
          <a-button type="primary" @click="onComfirmClick">{{ comfirmText }}</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@arco/hooks/loading.ts'
import { Message } from '@arco-design/web-vue'
import { defineProps, computed, ref } from 'vue'
import { addConfig, addTaskWorkFlow } from '@/api/arcoApi/taskWorkFlow/taskConfig.ts'
import DeptUserSelect from '@/components/deptUserSelect/index.vue'
import _ from 'lodash'

interface Props {
  taskTypeOptions: SelectOptionData[]
  mdtOrgUsersOptions: SelectOptionData[]
  comfirmText?: string
  entrustsId?: string | number
  workflowId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  TaskTypeOptions: () => [],
  mdtOrgUsersOptions: () => [],
  comfirmText: '确定',
  entrustsId: '',
  workflowId: ''
})

interface BASE_TEMPLATE_FORM_TYPE {
  managerIdList: Array
  timeLimit: number
  taskType: string
}

const updateFlag = ref<boolean>(false)
const emits = defineEmits(['comfirm', 'cancel'])

const { loading, setLoading } = useLoading(false)

const formData = ref<BASE_TEMPLATE_FORM_TYPE>({
  taskType: '',
  timeLimit: undefined,
  managerIdList: []
})
const formRef = ref<FormInstance>()
const formRules = ref({
  taskType: [{ required: true, message: '请选择任务类型' }],
  timeLimit: [{ required: true, message: '请输入任务时限' }],
  managerIdList: [{ required: true, message: '请选择任务负责人' }]
})

const resetModel = () => {
  formRef.value?.resetFields()
}

const setModel = (model: BASE_TEMPLATE_FORM_TYPE) => {
  if (model) {
    formData.value = model
  }
}

const submitForm = async (model: BASE_TEMPLATE_FORM_TYPE) => {
  setLoading(true)
  try {
		addConfig(model).then((res) => {
      Message.success((updateFlag.value ? '更新' : '新增') + '任务配置成功！')
      emits('comfirm','submit')
    })
  } catch (err) {
    Message.error((updateFlag.value ? '更新' : '新增') + '任务配置失败！')
  } finally {
    setLoading(false)
  }
}

const onComfirmClick = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    formData.value.workflowId = props.workflowId
    submitForm({ ...formData.value })
  }
}

const onCancelClick = async () => {
  const res = await formRef.value?.resetFields()
  if (!res) {
    emits('cancel')
  }
}
const setFormModel = (modal) => {
	if (modal) {
		formData.value = _.cloneDeep(modal)
		updateFlag.value = true
	}else {
		formData.value['configId']=undefined
		resetModel()
		updateFlag.value = false
	}
}
const init = () => {

}

init()
defineExpose({ setFormModel,resetModel, setModel })
</script>

<style scoped lang="scss">
.form {
  width: 500px;
}

.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}
</style>
