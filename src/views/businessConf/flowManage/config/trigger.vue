<template>
  <a-spin>
    <a-form :model="formState" ref="formRef" layout="vertical" class="filter-form">
      <template v-for="(group, groupIndex) in formState.groups" :key="groupIndex">
        <a-layout class="group-block">
          <a-layout-sider class="trigger-sider-left">
            <a-select
              size="small"
              v-if="group.items.length > 1"
              v-model="group.rowOperator"
              placeholder="请选择"
              :options="logicOptions"
            ></a-select>
          </a-layout-sider>
          <a-layout-content>
            <a-row
              :gutter="16"
              v-for="(row, itemIndex) in group.items"
              type="flex"
              align="center"
              justify="space-around"
              :key="itemIndex"
              class="filter-group"
            >
              <!--第一列-->
              <a-col :span="5">
                <a-select
                  size="small"
                  placeholder="请选择"
                  v-model="row.tmplModuleId"
                  :options="templateOptions"
                  style="width: 100%"
                  @change="changeModule(row)"
                >
                </a-select>
              </a-col>
              <!--第二列-->
              <a-col :span="6">
                <a-select
                  size="small"
                  v-model="row.tmplModuleFieldId"
                  placeholder="请选择"
                  :options="modulesMap[row.tmplModuleId]"
                  style="width: 100%"
                  @change="changeModuleField(row)"
                ></a-select>
              </a-col>
              <!--第三列-->
              <a-col :span="5">
                <a-select
                  size="small"
                  v-model="row.operator"
                  placeholder="请选择"
                  :options="row.operatorOptions"
                  style="width: 100%"
                >
                </a-select>
              </a-col>
              <!--第四列-->
              <a-col :span="6">
                <template v-if="row.operator != 1">
                  <a-select
                    size="small"
                    v-if="row.dictType === 'dict'"
                    v-model="row.targetValue"
                    placeholder="请选择"
                    :options="row.dictOptions"
                    style="width: 100%"
                  >
                  </a-select>
									<DictDataSelect :dict-type="row.system_dict_name" v-model="row.targetValue" v-else-if="row.dictType === 'system_dict'" checkStrictly></DictDataSelect>
                  <a-input size="small" v-model="row.targetValue" v-else-if="row.dictType === 'varchar'||row.dictType === 'number' || row.dictType === 'decimal'"></a-input>
<!--                  <a-input-number-->
<!--                    size="small"-->
<!--                    v-model="row.targetValue"-->
<!--                    :step="0.01"-->
<!--                    v-else-if="row.dictType === 'number' || row.dictType === 'decimal'"-->
<!--                  ></a-input-number>-->
                  <a-date-picker
                    show-time
                    size="small"
                    v-else-if="row.dictType === 'datetime'"
                    v-model="row.targetValue"
                    :time-picker-props="{ defaultValue: '09:00:00' }"
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                  <a-input :disabled="true" size="small" v-else></a-input>
                </template>
              </a-col>
              <a-col :span="2">
                <a-button
                  size="mini"
                  v-if="itemIndex == 0"
                  @click="addItem(groupIndex)"
                  type="primary"
                  shape="circle"
                  ghost
                >
                  <icon-plus />
                </a-button>
                <!--								<a-button size="mini" v-else class="danger" @click="removeItem(groupIndex, itemIndex)" shape="circle" ghost>-->
                <!--									<a-icon type="star" theme="filled"/>-->
                <icon-minus-circle
                  v-if="(formState.groups.length === 1 &&
                        (group.items.length === 1 ||
                         (group.items.length > 1 && itemIndex !== 0))) ||
                       (formState.groups.length > 1 &&
                        ((group.items.length > 1 && itemIndex !== 0) ||
                         groupIndex !== 0))"
                  :style="{ fontSize: '30px', color: '#e45655' }"
                  @click="removeItem(groupIndex, itemIndex)"
                />
								<!--								</a-button>-->
              </a-col>
            </a-row>
          </a-layout-content>
          <a-layout-sider class="trigger-sider-right">
            <a-button size="mini" v-if="groupIndex == 0" type="primary" shape="circle" @click="addGroup">
              <icon-plus />
            </a-button>
            <!--						<a-button size="mini"   type="danger" shape="circle" @click="removeGroup(groupIndex)">-->
            <icon-minus-circle
							v-else
              :style="{ fontSize: '30px', color: '#e45655' }"
              @click="removeGroup(groupIndex)"
            />
            <!--						// </a-button>-->
          </a-layout-sider>
        </a-layout>
        <a-row v-if="groupIndex < formState.groups.length - 1">
          <a-col :span="4" :offset="10">
            <a-select
              size="small"
              style="width: 100%"
              v-model="group.logicOperator"
              placeholder="请选择"
              :options="logicOptions"
            ></a-select>
          </a-col>
        </a-row>
      </template>
      <a-form-item class="base-form-justify" style="margin-top: 100px">
        <a-space :size="20">
          <a-button @click="onCancelClick">取消</a-button>
          <a-button type="primary" @click="onComfirmClick">{{ comfirmText }}</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@arco/hooks/loading.ts'
import { Message, SelectOptionGroup } from '@arco-design/web-vue'
import { defineProps, reactive, ref } from 'vue'
import _ from 'lodash'
import { saveTaskTriggerConditionGroup } from '@/api/arcoApi/taskWorkFlow/taskTriggerCondition.ts'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
interface Props {
  templateOptions: SelectOptionGroup[]
  taskTypeOptions: SelectOptionData[]
  mdtOrgUsersOptions: SelectOptionData[]
  modulesMap: Map<string, Array<any>>
  fieldTypeMap: Map<string, Array<any>>
  systemDictMap: Map<string, Array<any>>
  comfirmText?: string
  entrustsId?: string | number
  workflowId?: string | number
  templateId?: string | number
  configId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  taskTypeOptions: () => [],
  mdtOrgUsersOptions: () => [],
  comfirmText: '确定',
  entrustsId: '',
  workflowId: '',
  templateId: '',
  configId: ''
})

const updateFlag = ref<boolean>(false)
const emits = defineEmits(['comfirm', 'cancel'])

// 定义“且”，“或”，供a-select使用
const logicOptions = ref<SelectOptionData[]>([
  {
    label: '且',
    value: 1
  },
  {
    label: '或',
    value: 2
  }
])
// 定义基础运算符数据，供a-select使用。范围：有值、等于、包含
const baseOperatorOptions = ref<SelectOptionData[]>([
  {
    label: '等于',
    value: 2
  },
  {
    label: '有值',
    value: 1
  },
  {
    label: '包含',
    value: 3
  }
])

const formRef = ref<FormInstance>()
const { setLoading } = useLoading()
// 表单数据结构
const formState = reactive({
  groups: [
    {
      rowOperator: 1, // 同组内行之间的逻辑：且/或
      logicOperator: 1, // 分组逻辑：且/或
      configId: props.configId,
      items: [
        {
          tmplModuleId: undefined,
          tmplModuleFieldId: undefined,
          operator: undefined,
          targetValue: undefined,
          dictType: undefined // 附加属性：第二列的字段类型
        }
      ]
    }
  ]
})

// 新增分组
const addGroup = () => {
  formState.groups.push({
    configId: props.configId,
    rowOperator: 1, // 同组内行之间的逻辑：且/或
    logicOperator: 1, // 分组逻辑：且/或
    items: [
      {
        tmplModuleId: undefined,
        tmplModuleFieldId: undefined,
        operator: undefined,
        targetValue: undefined,
        dictType: undefined // 附加属性：第二列的字段类型
      }
    ]
  })
}

// 删除分组
const removeGroup = (index) => {
  formState.groups.splice(index, 1)
}

// 新增条件项到指定分组
const addItem = (groupIndex) => {
  formState.groups[groupIndex].items.push({
    tmplModuleId: undefined,
    tmplModuleFieldId: undefined,
    operator: undefined,
    targetValue: undefined,
    dictType: undefined // 附加属性：第二列的字段类型
  })
}

// 删除指定分组的条件项
const removeItem = (groupIndex, itemIndex) => {
  formState.groups[groupIndex].items.splice(itemIndex, 1)
}

const submitForm = async (model) => {
  setLoading(true)
  try {
    saveTaskTriggerConditionGroup(model).then((res) => {
      Message.success((updateFlag.value ? '更新' : '新增') + '成功！')
      emits('comfirm', 'submit')
    })
  } catch (err) {
    Message.error((updateFlag.value ? '更新' : '新增') + '失败！')
  } finally {
    setLoading(false)
  }
}

function validateFormStateGroups(groups) {
  // 检查整个groups数组是否为空
  if (!groups || groups.length === 0) {
    Message.error('请至少配置一个条件组')
    return false
  }
	if (groups[0].items.length === 0) {
		return true
	}
  for (const [groupIndex, group] of groups.entries()) {
    // 检查组级属性
    const groupKeys = ['rowOperator', 'logicOperator']
    for (const key of groupKeys) {
      if (group[key] === undefined) {
        Message.error(`组 ${groupIndex + 1} 缺少必要属性: ${key}`)
        return false
      }
    }

    // 检查条件项
    if (!group.items || group.items.length === 0) {
      Message.error(`组 ${groupIndex + 1} 缺少条件项`)
      return false
    }

    for (const [itemIndex, item] of group.items.entries()) {
      // 检查基础字段
      const showFields = ['模块名称', '字段名称', '操作符']
      const baseFields = ['tmplModuleId', 'tmplModuleFieldId', 'operator']
      for (const field of baseFields) {
        if (item[field] === undefined) {
          Message.error(
            `组 ${groupIndex + 1} 的条件项 ${itemIndex + 1} 缺少 ${showFields[baseFields.indexOf(field)]} 字段`
          )
          return false
        }
      }

      // 特殊检查 targetValue
      if (item.operator !== 1 && item.targetValue === undefined) {
        Message.error(`组 ${groupIndex + 1} 的条件项 ${itemIndex + 1} 需要 触发值 字段`)
        return false
      }
    }
  }

  // 所有校验通过
  return true
}

const onComfirmClick = async () => {
  if (validateFormStateGroups(formState.groups)) {
    // 强制赋值，防止configId错误
    formState.groups[0].configId = props.configId
    submitForm(formState.groups)
  }
}
const changeModule = (row) => {
  row.tmplModuleFieldId = undefined
  row.operator = undefined
  row.targetValue = undefined
  row.dictOptions = []
  row.dictType = undefined
}
const changeModuleField = (row) => {
  row.targetValue = undefined
  row.dictOptions = []
  const fourColumnObj = props.fieldTypeMap[row.tmplModuleFieldId]
  let dataType = fourColumnObj.data_type
  let type = 'varchar'
  if (dataType === 'dict') {
    type = 'dict'
    let values = fourColumnObj.dict_values
    let json_obj = JSON.parse(values)
    try {
      if (json_obj.hasOwnProperty('type')) {
        row['dictOptions'] = customToArco(json_obj.values)
      } else {
        row['dictOptions'] = customToArco(values)
      }
    } catch (e) {
      Message.error('字典格式错误')
    }
  } else if (dataType === 'system_dict') {
    type = 'dict'
		row['system_dict_name'] = fourColumnObj.dict_type
    // 如果是系统字典
    // row['dictOptions'] = props.systemDictMap.get(fourColumnObj.dict_type)
  }
  if (type === 'dict') {
    row['operatorOptions'] = baseOperatorOptions.value.filter((item) => item.value !== 3)
  } else {
    row['operatorOptions'] = baseOperatorOptions.value
  }
  row['dictType'] = dataType
}

// 定义解析json的函数
const customToArco = (json) => {
  let customJSONArray = json
	if (typeof json === 'string'){
		customJSONArray = JSON.parse(json)
	}
  let arcoJSONArray = []
  // 遍历customJSONArray
  customJSONArray.forEach((item) => {
    let key = item.key != undefined && item.key!='' && !item.hasOwnProperty('uuid') ? item.key : item.value
    arcoJSONArray.push({
      label: item.value,
      value: `${key}`
    })
  })
  return arcoJSONArray
}
const onCancelClick = async () => {
  // if (!res) {
  emits('cancel')
  // }
  // console.log(formState.groups[0])
}
const setFormModel = async (modal) => {
  // await getTemplateData()
  if (modal.length > 0) {
    formState.groups = _.cloneDeep(modal)
    // 遍历formState.groups
    formState.groups.forEach((group) => {
      group.items.forEach((row) => {
        let dict = props.fieldTypeMap[row.tmplModuleFieldId]
        row['dictType'] = dict['data_type']
        let type = false
        if (dict.data_type === 'dict') {
          type = true
          let json_obj = JSON.parse(dict.dict_values)
          if (json_obj.hasOwnProperty('type')) {
            row['dictOptions'] = customToArco(json_obj.values)
          } else {
            row['dictOptions'] = customToArco(json_obj)
          }
        } else if (dict.data_type === 'system_dict') {
          type = true
					row['system_dict_name'] = dict.dict_type
          row['dictOptions'] = props.systemDictMap.get(dict.dict_type)
        }
        if (type) {
          row['operatorOptions'] = baseOperatorOptions.value.filter((item) => item.value !== 3)
        } else {
          row['operatorOptions'] = baseOperatorOptions.value
        }
      })
    })
    updateFlag.value = true
  } else {
    formState.groups = []
    addGroup()
    formState.groups[0].configId = props.configId
    updateFlag.value = false
  }
}

const init = () => {}

init()
defineExpose({ setFormModel })
</script>

<style scoped lang="scss">
.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}

.group-block {
  background-color: #c4dedb;
  //margin-top: 10px;
}

.trigger-sider-left,
.trigger-sider-right {
  width: 90px !important;
  text-align: center;
  background-color: #c4dedb;
  // 垂直居中
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: none;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.trigger-sider-right {
  width: 40px !important;
}

::v-deep .arco-layout-sider-children {
  height: auto !important;
}

.arco-layout-content {
  width: 830px;
}

.arco-row {
  display: flex;
  flex-flow: row wrap;
  min-height: 40px;
  align-items: center;

  .arco-col-2 {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 0px !important;
  }
}

.arco-row-justify-space-around {
  margin-left: 0px !important;
}
</style>
