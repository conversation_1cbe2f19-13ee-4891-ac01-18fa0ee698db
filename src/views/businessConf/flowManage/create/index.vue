<template>
	<div class="template-create-page">
		<a-card :bordered="false" class="template-create-card">
			<template #title>
				<span class="bold">创建模板</span>
			</template>
			<div class="wrapper">
				<a-steps v-model:current="step" style="width: 400px" line-less class="steps">
					<a-step description="创建流程信息">选择基础信息</a-step>
					<a-step description="创建完成">完成创建</a-step>
				</a-steps>
				<keep-alive>
					<BaseInfo
						v-if="step === 1"
						ref="baseInfoRef"
						:mdt-status-options="flowStatusOptions"
						:mdt-entrusts-options="entrustsOptions"
						@comfirm="changeStep"
						@cancel="handleCancel"
						/>
						<Success v-else-if="step === 2" @change-step="changeStep" />
				</keep-alive>
			</div>
		</a-card>
	</div>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import { getAllTemplateList } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import { getCaseNatures } from '@/api/eleApi/common'

import BaseInfo from './components/base-info.vue'
import Success from './components/success.vue'
import { useRouter } from 'vue-router'
import { nextTick, ref } from 'vue'

const router = useRouter()

const tmplInfo = ref<{ id: number; tmplType: string }>()
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const natureTitleOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const tmplOptions = ref<SelectOptionData[]>([])
const step = ref(1)

const changeStep = (direction: string, info?: { id: number; tmplType: string }) => {
	if (direction === 'newcreate') {
		step.value = 1
		getTemplOptions()
		nextTick(() => baseInfoRef.value?.resetModel())

	} else if (direction === 'submit') {
		step.value = 2
		tmplInfo.value = info
	} else if (direction === 'backward') {
		step.value -= 1
	} else if (direction === 'viewConf') {
		router.push('flowManage')
	}
}

const getTemplOptions = () => {
	getAllTemplateList().then((res) => {
		if (res && res.length) {
			tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
		}
	})
}

const getAllEntrusts = () => {
	findAllEntrusts().then((res: unknown) => {
		let data = res as any[]
		if (data && data.length) {
			entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
		}
	})
}

const getCaseNaturesData = () => {
	getCaseNatures().then((res) => {
		if (res && res.length) {
			natureTitleOptions.value = res.map((item) => ({ label: item.dictTag, value: item.dictTag }))
		}
	})
}

const handleCancel = () => {
	router.go(-1)
}

getCaseNaturesData()
getTemplOptions()
getAllEntrusts()
</script>

<style scoped lang="scss">
.wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px 0;
	background-color: var(--color-bg-2);

	:deep(.arco-form) {
		.arco-form-item {
			&:last-child {
				margin-top: 20px;
			}
		}
	}
}

.steps {
	margin-bottom: 56px;
}
</style>
