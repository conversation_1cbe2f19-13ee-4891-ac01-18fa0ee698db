<template>
  <a-spin :loading="loading">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      class="form"
    >
      <a-form-item field="workflowName" label="任务流程名称" :max-length="50">
        <a-input v-model="formData.workflowName" placeholder="请输入" allow-clear/>
      </a-form-item>

      <a-form-item field="entrustsId" label="案源方">
        <a-select
          v-model="formData.entrustsId"
          :options="mdtEntrustsOptions"
          placeholder="请选择"
					allow-clear
					@change="handleEntrustsChange"
        />
      </a-form-item>

      <a-form-item field="templateId" label="案件信息模板">
        <a-select v-model="formData.templateId" :options="tmplOptions" placeholder="请选择" allow-clear/>
      </a-form-item>
			<a-form-item field="triggerCondition" label="流程启动条件">
				<a-radio-group v-model="formData.triggerCondition">
					<a-radio :value="1">案件导入</a-radio>
					<a-radio :value="2">案件分派组织</a-radio>
				</a-radio-group>
				</a-form-item>
      <a-form-item field="status" label="流程状态">
        <a-switch
          v-model="formData.status"
					:options="mdtStatusOptions"
        ></a-switch>
      </a-form-item>
      <a-form-item field="remark" label="备注">
        <a-textarea v-model="formData.remark" :auto-size="{ minRows: 4 }" placeholder="请输入" allow-clear/>
      </a-form-item>
      <a-form-item class="base-form-justify">
        <a-space :size="20">
          <a-button type="primary" @click="onComfirmClick">{{ comfirmText }}</a-button>
          <a-button @click="onCancelClick">取消</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {
	deputyTmplList,
	getSelectTemplateList,
	saveDeputyTemplate,
	saveMainTemplate
} from '@/api/arcoApi/businessConf/caseTemplateManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import { computed, ref } from 'vue'
import { addTaskWorkFlow } from '@/api/arcoApi/taskWorkFlow/taskWorkFlow.ts'

interface Props {
	mdtStatusOptions?: SelectOptionData[]
	mdtEntrustsOptions?: SelectOptionData[]
  natureTitleOptions: SelectOptionData[]
  tmplOptions: SelectOptionData[]
  disabledSelect?: boolean
  comfirmText?: string
}

interface BASE_TEMPLATE_FORM_TYPE {
  entrustsId: string | number
  parentId: string | number
  id: string | number
  entrustsName: string
  natureTitle: string
  updaterName: string
  tmplStatus: number
  tmplTitle: string
  tmplType: string
  tmplDesc: string
}

const props = withDefaults(defineProps<Props>(), {
  natureTitleOptions: () => [],
  entrustsOptions: () => [],
  disabledSelect: false,
  tmplOptions: () => [],
  comfirmText: '创建'
})
const tmplOptions = ref<SelectOptionData[]>([])
const emits = defineEmits(['comfirm', 'cancel'])

const { loading, setLoading } = useLoading(false)

const formData = ref<{}>({
	workflowName: '',
	entrustsId: null,
	entrustsName: '',
	templateId: null,
	triggerCondition: 1,
	contactsName: '',
	status: true,
	workflowId:null
})
const formRef = ref<FormInstance>()
const formRules = ref({
	workflowName: [{ required: true, message: '请输入任务流程名称' }],
	entrustsId: [{ required: true, message: '请输入案源方名称', trigger: 'blur' }],
	templateId: [{ required: true, message: '请选择案件信息模板', trigger: 'blur' }],
	triggerCondition: [{ required: true, message: '请选择流程启动条件', trigger: 'blur' }],
	status: [{ required: true, message: '请选择案件信息模板', trigger: 'blur' }]
})

const tmplTypeOptions = ref<SelectOptionData[]>(dict.tmplTypeOptions)

const showTmplType = computed(() => {
  return formData.value.tmplType && formData.value.tmplType !== '1'
})

const resetModel = () => {
  formRef.value?.resetFields()
}

const setModel = (model: BASE_TEMPLATE_FORM_TYPE) => {
  if (model) {
    formData.value = model
  }
}

const getTmplList = () => {
	let params = { tmplStatus: 1, tmplType: '2', entrustsId: formData.value.entrustsId }
	getSelectTemplateList(formData.value.entrustsId).then((res) => {
		if (res && res.length) {
			tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
		}
	})
}
const handleEntrustsChange = () => {
	formRef.value?.resetFields(['templateId'])
	tmplOptions.value = []
	if (formData.value.entrustsId) {
		getTmplList()
	}
}

const submitForm = async (model: BASE_TEMPLATE_FORM_TYPE) => {
  setLoading(true)
  try {
    if (model) {
      let id: number | null = null
      if (model.tmplType === '1') {
        id = await saveMainTemplate({
          entrustsName: model.entrustsName,
          natureTitle: model.natureTitle,
          entrustsId: model.entrustsId,
          tmplStatus: model.tmplStatus,
          tmplTitle: model.tmplTitle,
          tmplType: model.tmplType,
          tmplDesc: model.tmplDesc,
          tmplId: model.id
        })
      } else {
        id = await saveDeputyTemplate({
          tmplStatus: model.tmplStatus,
          tmplTitle: model.tmplTitle,
          tmplType: model.tmplType,
          tmplDesc: model.tmplDesc,
          tmplId: model.parentId,
          deputyTmplId: model.id
        })
      }
      emits('comfirm', 'submit', { id, tmplType: model.tmplType })
    }
  } catch (err) {
    Message.error('创建模板失败！')
  } finally {
    setLoading(false)
  }
}

const onComfirmClick = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
		setLoading(true)
		addTaskWorkFlow({ ...formData.value }).then(res=>{
			emits('comfirm', 'submit')
		}).catch ((err)=>{
			// Message.error(err.message)
		}). finally(()=> {
			setLoading(false)
		})
  }
	// emits('comfirm', 'submit')
}


const onCancelClick = async () => {
  const res = await formRef.value?.resetFields()
  if (!res) {
    emits('cancel')
  }
}

defineExpose({ resetModel, setModel })
</script>

<style scoped lang="scss">
.form {
  width: 500px;
}

.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}
</style>
