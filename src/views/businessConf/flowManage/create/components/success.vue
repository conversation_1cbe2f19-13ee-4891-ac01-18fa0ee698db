<template>
  <div class="success-wrap">
    <a-result status="success" title="创建成功" subtitle="模板创建成功" />
    <a-space :size="16">
      <a-button key="view" type="primary" @click="viewConf"> 查看配置 </a-button>
      <a-button key="again" type="secondary" @click="newCreate"> 再次创建 </a-button>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits(['changeStep'])

const viewConf = () => {
  emits('changeStep', 'viewConf')
}

const newCreate = () => {
  emits('changeStep', 'newcreate')
}
</script>

<style scoped lang="scss">
.success-wrap {
  text-align: center;
}
:deep(.arco-result) {
  padding-top: 0;
}
</style>
