<template>
	<a-modal
		v-model:visible="visible"
		:closable="false"
		:width="width"
		:title="title"
		:ok-loading="okLoading"
		:on-before-ok="handleSaveModal"
		:on-before-cancel="handleCancelModal"
		:footer="null"
		title-align="start"
		modal-class="mdt-modal-flow-dialog"
		:ok-text="nextText"
	>
		<div style="width: 50%;margin:0 auto">
			<div class="form-info">
				<a-form
					ref="baseFormRef"
					:model="baseInfoForm"
					:rules="baseInfoRules"
					:label-col-props="{ span: 6 }"
					:wrapper-col-props="{ span: 18 }"
					label-align="right"
				>
					<div class="base-form-box mdt-form-content">
						<a-row :gutter="16">
							<a-col :span="24">
								<a-form-item field="workflowName" label="任务流程名称">
									<a-input v-model="baseInfoForm.workflowName" :max-length="50" placeholder="请输入" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item field="entrustsId" label="案源方" disabled>
									<a-select
										v-model="baseInfoForm.entrustsId"
										:options="mdtEntrustsOptions"
										placeholder="请选择"
										allow-clear
										@change="handleEntrustsChange"
									/>
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item field="templateId" label="案件信息模板" disabled>
									<a-select v-model="baseInfoForm.templateId" :options="tmplOptions" placeholder="请选择" allow-clear />
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item field="triggerCondition" label="流程启动条件">
									<a-radio-group v-model="baseInfoForm.triggerCondition">
										<a-radio :value="1">案件导入</a-radio>
										<a-radio :value="2">案件分派组织</a-radio>
									</a-radio-group>
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item field="status" label="流程状态">
									<!-- 使用select选择器，v-model绑定baseInfoForm.status,数据源使用mdtStatusOptions               -->
									<a-switch v-model="baseInfoForm.status" :options="mdtStatusOptions" placeholder="请选择" />
								</a-form-item>
							</a-col>
							<a-col :span="24">
								<a-form-item
									:label-col-props="{ span: 5 }"
									:wrapper-col-props="{ span: 19 }"
									field="remark"
									label="备注"
								>
									<a-textarea
										v-model="baseInfoForm.remark"
										:auto-size="{ minRows: 4 }"
										style="margin-left: 15px"
										placeholder="请输入内容"
										allow-clear
									/>
								</a-form-item>
							</a-col>
							<a-col :span="6" :offset="6">
								<a-button type="primary" @click="handleSaveModal">确定</a-button>
							</a-col>
							<a-col :span="6">
								<a-button @click="handleCancelModal">取消</a-button>
							</a-col>
						</a-row>
					</div>
				</a-form>
			</div>

		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { editEntrustsInfo, getDeptInfoById } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message, SelectOption } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import { computed, ref } from 'vue'
import _ from 'lodash'
import { deputyTmplList, getSelectTemplateList } from '@/api/arcoApi/businessConf/caseTemplateManage.ts'
import { addTaskWorkFlow } from '@/api/arcoApi/taskWorkFlow/taskWorkFlow.ts'

interface Props {
	mdtTmplOptions?:SelectOptionData[]
	mdtStatusOptions?: SelectOptionData[]
	mdtEntrustsOptions?: SelectOptionData[]
	width?: string | number
	title: string
}

interface RENDER_BYIDDEPT_DATA_TYPE extends REQUEST_GET_ENTRUSTS_BYIDDEPT_DATA_TYPE {
	sysMdtOrgIdList: (string | number)[]
	showSelect: boolean
}

withDefaults(defineProps<Props>(), {
	mdtStatusOptions: () => [],
	mdtTmplOptionsf: () => [],
	width: '1000px',
	title: ''
})

const emits = defineEmits<{
	(e: 'comfirm'): void
	(e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
	return {
		workflowName: '',
		entrustsId: null,
		entrustsName: '',
		templateId: null,
		contactsName: '',
		status: true
	}
}

const baseInfoForm = ref<{}>(generateFormModel())

const renderData = ref<RENDER_BYIDDEPT_DATA_TYPE[]>([])
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)
// 定义current ，默认0
const current = ref<number>(1)
const tmplOptions = ref<SelectOptionData[]>([])
const nextText = ref<string>('创建')

const baseInfoRules = ref({
	workflowName: [{ required: true, message: '请输入任务流程名称' }],
	entrustsId: [{ required: true, message: '请输入案源方名称', trigger: 'blur' }],
	templateId: [{ required: true, message: '请选择案件信息模板', trigger: 'blur' }],
	triggerCondition: [{ required: true, message: '请选择流程启动条件', trigger: 'blur' }],
	status: [{ required: true, message: '请选择案件信息模板', trigger: 'blur' }]
})

const getEntrustsDeptList = () => {
	if (!baseInfoForm.value.entrustsId) return
	getDeptInfoById(baseInfoForm.value.entrustsId).then((res) => {
		if (res && res.length > 0) {
			renderData.value = res.map((item) => {
				return {
					sysMdtOrgIdList: item.sysMdtOrgDTOList.map((item) => item.orgId) || [],
					showSelect: false,
					...item
				}
			})
		}
	})
}

const getTmplList = () => {
	let params = { tmplStatus: 1, tmplType: '2', entrustsId: baseInfoForm.value.entrustsId }
	getSelectTemplateList(baseInfoForm.value.entrustsId).then((res) => {
		if (res && res.length) {
			tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: `${item.tmplId}` }))
		}
	})
}

const handleEntrustsChange = () => {
	baseFormRef.value?.resetFields(['templateId'])
	tmplOptions.value = []
	if (baseInfoForm.value.entrustsId) {
		getTmplList()
	}
}

const handleSaveModal = async () => {
	const res = await baseFormRef.value?.validate()
	if (!res) {
		okLoading.value = true
		const editFlag = baseInfoForm.value.workflowId != undefined
		addTaskWorkFlow(baseInfoForm.value)
			.then(() => {
				Message.success(editFlag ? '编辑成功' : '新增成功')
				emits('comfirm', 'submit')
				initFormModel()
				current.value = 2
			}).catch((err) => {
			// Message.error(err)
			console.log(err)
		})
			.finally(() => {
				okLoading.value = false
			})
	}
	// return false
	visible.value = true
}

const handleCancelModal = () => {
	initFormModel()
	visible.value = false
	return true
}

const setFormModel = (modal: REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE) => {
	baseInfoForm.value = _.cloneDeep(modal)
	renderData.value = []
	// getEntrustsDeptList()
	handleEntrustsChange()

}

const initFormModel = () => {
	baseInfoForm.value = generateFormModel()
	baseFormRef.value?.resetFields()
}
defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped>
.form-info {
	margin-top: 30px;
}
</style>
