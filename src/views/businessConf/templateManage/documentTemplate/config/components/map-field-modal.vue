<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-row :size="20">
        <a-col :span="8">
          <a-card :bordered="false" class="template-conf-card template-select-card" title="模块名称">
            <div class="module-tree-box">
              <a-input-search v-model="searchKey" style="margin-bottom: 8px" />
              <a-tree
                ref="treeRef"
                v-model:selected-keys="treeSelectedKeys"
                :block-node="true"
                :data="treeData"
                :virtual-list-props="{ height: '43vh' }"
                @select="handleTreeSelect"
              >
                <template #title="nodeData">
                  <a-tooltip :content="nodeData?.title">
                    <div class="module-tree-text">
                      <template v-if="getMatchIndex(nodeData?.title) < 0">{{ nodeData?.title }}</template>
                      <span v-else>
                        {{ nodeData?.title?.substr(0, getMatchIndex(nodeData?.title)) }}
                        <span style="color: var(--color-primary-light-4)">
                          {{ nodeData?.title?.substr(getMatchIndex(nodeData?.title), searchKey.length) }}
                        </span>
                        {{ nodeData?.title?.substr(getMatchIndex(nodeData?.title) + searchKey.length) }}
                      </span>
                    </div>
                  </a-tooltip>
                </template>
              </a-tree>
            </div>
          </a-card>
        </a-col>
        <a-col :span="16">
          <a-card :bordered="false" class="template-conf-card template-select-card" title="字段列表">
            <div class="template-select-table-box mdt-common-scrollbar">
              <a-table
                v-model:selected-keys="tableSelectedKeys"
                row-key="tmplModuleFieldId"
                :row-selection="rowSelection"
                :columns="columns"
                :data="moduleFields"
                :bordered="false"
              >
                <template #isValidated="{ record: {isValidated} }"> {{ isValidated ? '是' : '否' }}</template>
                <template #isRequire="{ record: {isRequire} }"> {{ isRequire ? '是' : '否' }}</template>
                <template #fieldDataType="{ record: {fieldDataType} }">
                  {{ fielldTypeObj[fieldDataType] || fieldDataType }}
                </template>
              </a-table>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import { getModuleFieldList } from '@/api/arcoApi/businessConf/caseTemplateManage'

import type { TreeNodeData } from '@arco-design/web-vue/es/tree'
import { dictEnumValToObject } from '@/utils'
import { computed, reactive, ref, watch } from 'vue'
import dict from '@/dict/businessConf'

import _ from 'lodash'

interface Props {
  record?: 'single' | 'multiple'
  tmplId?: string | number
  width?: string | number
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  record: 'single',
  width: '800px',
  tmplId: '',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm', data: REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[]): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const [tableSelectedKeys] = defineModel<(string | number)[]>('tableSelectedKeys')

interface TEMPLATE_CONFTREE_TREE_DATA_TYPE extends TreeNodeData {
  children?: TEMPLATE_CONFTREE_TREE_DATA_TYPE[]
  fields?: REQUEST_GET_FIELD_LIST_TYPE[]
  mainId?: string | number
  key: string | number
  tableName?: string
  title: string
}
const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const mainTmplTreeData = ref<TEMPLATE_CONFTREE_TREE_DATA_TYPE[]>([
  { title: '系统模块', key: 'module1', draggable: false, mainId: 0, children: [] },
  { title: '业务模块', key: 'module2', draggable: false, mainId: 0, children: [] },
  { title: '自定义模块', key: 'module3', draggable: false, mainId: 0, children: [] }
])
const selectFields = ref<REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[]>([])
const treeSelectNode = ref<TEMPLATE_CONFTREE_TREE_DATA_TYPE>()
const moduleFields = ref<REQUEST_GET_FIELD_LIST_TYPE[]>([])
const treeSelectedKeys = ref<(string | number)[]>()
// const multipleNames = ref(['当事人信息', '调解记录'])
const searchKey = ref('')

const columns = computed<TableColumnData[]>(() => [
  { width: 100, tooltip: true, ellipsis: true, title: '字段名称', dataIndex: 'fieldTitle' },
  {
    width: 100,
    tooltip: true,
    ellipsis: true,
    title: '字段类型',
    dataIndex: 'fieldDataType',
    slotName: 'fieldDataType'
  },
  { width: 120, tooltip: true, title: '是否必填', dataIndex: 'isRequire', slotName: 'isRequire' },
  { width: 120, tooltip: true, title: '是否效验格式', dataIndex: 'isValidated', slotName: 'isValidated' }
])

const fielldTypeObj = computed(() => {
  return dictEnumValToObject(dict.fieldDataTypeOptions)
})

const treeData = computed(() => {
  if (!searchKey.value) return mainTmplTreeData.value
  return searchData(searchKey.value)
})

function searchData(keyword: string) {
  const loop = (data: TEMPLATE_CONFTREE_TREE_DATA_TYPE[]) => {
    const result: TEMPLATE_CONFTREE_TREE_DATA_TYPE[] = []
    data.forEach((item) => {
      if (item.title.toLowerCase().indexOf(keyword.toLowerCase()) > -1) {
        result.push({ ...item })
      } else if (item.children) {
        const filterData = loop(item.children)
        if (filterData.length) {
          result.push({ ...item, children: filterData })
        }
      }
    })
    return result
  }

  return loop(mainTmplTreeData.value)
}

function getMatchIndex(title: string) {
  if (!searchKey.value) return -1
  return title.toLowerCase().indexOf(searchKey.value.toLowerCase())
}

function handleTreeSelect(ids: any, data: any) {
  if (data.node) {
    treeSelectNode.value = data.node
    if (data.node.mainId === 0) {
      moduleFields.value = []
    } else {
      moduleFields.value = data.node.fields || []
      moduleFields.value = data.node.fields || []
    }
  }
}

const handleSelectFieldChange = (ids: (string | number)[], status: boolean) => {
  if (status) {
    let addFields = moduleFields.value.filter((item) => {
      return ids.includes(item.tmplModuleFieldId)
    })
    let mapFields: REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[] = addFields.map((item) => {
      return {
        tableName: treeSelectNode.value?.tableName || '',
        moduleTitle: treeSelectNode.value?.title || '',
        tmplModuleId: treeSelectNode.value?.key || '',
        tmplModuleFieldId: item.tmplModuleFieldId,
        fieldDataType: item.fieldDataType,
        originalName: item.originalName,
        fieldValues: item.fieldValues,
        fieldTitle: item.fieldTitle,
        fieldName: item.fieldName
      }
    })
    selectFields.value = selectFields.value.concat(mapFields)
  } else {
    selectFields.value = selectFields.value.filter((item) => !ids.includes(item.tmplModuleFieldId))
  }
}

const okClick = () => {
  emits('comfirm', selectFields.value)
}

const getMainTmplInfo = async (id: string | number) => {
  let res = await getModuleFieldList(id)
  let isSingle = props.record === 'single'
  mainTmplTreeData.value[0].children = res.systemModules.map((item) => {
    let { moduleTitle, tmplModuleId, moduleFields, tableName, allowMultipleRecord } = item
    return {
      disabled: (isSingle && allowMultipleRecord === 1) || (!isSingle && allowMultipleRecord === 0),
      fields: moduleFields,
      tableName: tableName,
      title: moduleTitle,
      mainId: 'module1',
      key: tmplModuleId,
      draggable: false
    }
  })
  mainTmplTreeData.value[1].children = res.businessModules.map((item) => {
    let { moduleTitle, tmplModuleId, moduleFields, tableName, allowMultipleRecord } = item
    return {
      disabled: (isSingle && allowMultipleRecord === 1) || (!isSingle && allowMultipleRecord === 0),
      tableName: tableName,
      fields: moduleFields,
      title: moduleTitle,
      mainId: 'module2',
      key: tmplModuleId,
      draggable: false
    }
  })
  mainTmplTreeData.value[2].children = res.customModules.map((item, idx) => {
    let { moduleTitle, tmplModuleId, moduleFields, tableName, allowMultipleRecord } = item
    return {
      disabled: (isSingle && allowMultipleRecord === 1) || (!isSingle && allowMultipleRecord === 0),
      fields: moduleFields,
      tableName: tableName,
      title: moduleTitle,
      mainId: 'module3',
      key: tmplModuleId,
      idx
    }
  })
}

watch(
  () => tableSelectedKeys.value,
  (newVal, oldVal) => {
    if (newVal && oldVal) {
      // 新增情况下
      if (newVal.length > oldVal.length) {
        let addIds = newVal.filter((id) => !oldVal.includes(id))
        handleSelectFieldChange(addIds, true)
      }
      if (newVal.length < oldVal.length) {
        let removeIds = oldVal.filter((id) => !newVal.includes(id))
        handleSelectFieldChange(removeIds, false)
      }
    }
  }
)

watch(
  () => visible.value,
  (newVal) => {
    if (newVal && tableSelectedKeys.value) {
      selectFields.value = tableSelectedKeys.value.map((id) => {
        return {
          tmplModuleFieldId: id,
          tmplModuleId: '',
          originalName: '',
          moduleTitle: '',
          fieldTitle: '',
          fieldName: '',
          tableName: ''
        }
      })
    } else {
      selectFields.value = []
    }
  }
)

defineExpose({ getMainTmplInfo })
</script>

<style lang="scss" scoped></style>
