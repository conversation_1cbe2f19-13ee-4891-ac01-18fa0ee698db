<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="okClick"
  >
    <div class="form-info">
      <a-form ref="baseFormRef" :model="baseInfoForm" label-align="left">
        <a-form-item field="allowMultipleRecord" label="映射方式">
          <a-radio-group v-model="baseInfoForm.allowMultipleRecord" @change="handleMapMethodsChange">
            <a-radio v-for="item in mapMethodsOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item field="parameterName" :rules="[{ required: true, message: '请输入参数名称' }]" label="参数名称">
          <a-input v-model="baseInfoForm.parameterName" :max-length="100" placeholder="请输入内容" allow-clear />
        </a-form-item>
        <a-form-item field="caseFields" label="映射字段">
          <a-card class="mdt-card-common" title="映射字段">
            <template #extra>
              <a-button type="text" @click="handleInfoAddField">添加映射字段 </a-button>
            </template>
            <a-table
              row-key="mappingId"
              :pagination="false"
              :columns="columns"
              :scroll="{ maxHeight: '30vh' }"
              :data="renderData"
              :bordered="false"
            >
              <template #rank="{ rowIndex }"> {{ rowIndex < 9 ? '0' : '' }}{{ rowIndex + 1 }} </template>
              <template #operations="{ record }">
                <a-button v-auth="['tmplSave']" type="text" @click="handleDelete(record)">删除</a-button>
              </template>
            </a-table>
          </a-card>
        </a-form-item>

        <a-form-item field="parameterContent" label="参数内容">
          <a-space direction="vertical" fill>
            <a-textarea
              v-model="baseInfoForm.parameterContent"
              :auto-size="{ minRows: 4 }"
              :max-length="250"
              placeholder="请输入内容"
              allow-clear
            />
            <a-typography>
              <a-typography-text>
                说明：
                <a-typography-text>
                  可以在“当事人信息”模块后增加 " <a-typography-text type="danger" copyable>(原告)</a-typography-text>"
                  或者 "<a-typography-text type="danger" copyable>(被告)</a-typography-text>"
                  来指明映射原告信息还是被告信息，如 "
                  <a-typography-text type="success">
                    {{ '{' }}{当事人信息(原告).当事人名称}{{ '}' }}
                  </a-typography-text>
                  "。
                </a-typography-text>
              </a-typography-text>
            </a-typography>
          </a-space>
        </a-form-item>
        <!-- 多记录映射和单记录的差别在于增加了分隔符设置。 -->
        <a-form-item v-if="baseInfoForm.allowMultipleRecord === 1" field="delimiter" label="分割符">
          <a-radio-group v-model="delimiter" @change="handleDelimiter">
            <a-radio value="return">用回车分割 </a-radio>
            <a-radio value="other">
              <a-space :size="10">
                <span> 其他分割符: </span>
                <a-input v-model="baseInfoForm.delimiter" :readonly="delimiter === 'return'" allow-clear />
              </a-space>
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <Suspense>
        <MapFieldModal
          ref="mapFieldModalRef"
          v-model:visible="modalVisible"
          v-model:table-selected-keys="tableSelectedKeys"
          :record="baseInfoForm.allowMultipleRecord === 0 ? 'single' : 'multiple'"
          title="选择映射字段"
          @comfirm="handleMapFieldModalComfirm"
        ></MapFieldModal>
      </Suspense>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { getDocTemplateMappingInfo, saveDocTemplateMapping } from '@/api/arcoApi/businessConf/docTemplateManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { computed, inject, nextTick, ref } from 'vue'
import MapFieldModal from './map-field-modal.vue'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import _ from 'lodash'

interface Props {
  mappingId: string | number
  tmplId: string | number
  width?: string | number
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '700px',
  mappingId: '',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const docTmplId = inject<string | number>('docTmplId')

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const generateFormModel = () => {
  return {
    allowMultipleRecord: 0,
    parameterContent: '',
    parameterName: '',
    delimiter: '',
    caseFields: [],
    createTime: '',
    updateTime: '',
    creatorId: '',
    updaterId: '',
    docTmplId: '',
    mappingId: '',
    tmplId: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_DOC_TEMPLATE_MAPPING_SAVE_PARAM_TYPE>(generateFormModel())

const renderData = ref<REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[]>([])
const mapMethodsOptions = ref<SelectOptionData[]>(dict.mapMethodsOptions)

const mapFieldModalRef = ref<InstanceType<typeof MapFieldModal>>()
const tableSelectedKeys = ref<(string | number)[]>([])
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)
const modalVisible = ref(false)
const delimiter = ref('return')

const columns = computed<TableColumnData[]>(() => [
  { width: 60, title: '序号', dataIndex: 'rank', slotName: 'rank' },
  { width: 100, tooltip: true, ellipsis: true, title: '字段名称', dataIndex: 'fieldTitle' },
  { width: 100, tooltip: true, ellipsis: true, title: '所属模块名称', dataIndex: 'moduleTitle' },
  { width: 50, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleMapMethodsChange = (value) => {
  baseInfoForm.value.parameterContent = ''
  renderData.value = []
  value ? (baseInfoForm.value.delimiter = '/n') : (baseInfoForm.value.delimiter = '')
}

const handleDelimiter = (value) => {
  value === 'return' ? (baseInfoForm.value.delimiter = '\n') : (baseInfoForm.value.delimiter = '')
}

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    baseInfoForm.value.caseFields = renderData.value
    baseInfoForm.value.mappingId = props.mappingId
    baseInfoForm.value.docTmplId = docTmplId || ''
    baseInfoForm.value.tmplId = props.tmplId || ''
    saveDocTemplateMapping(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        visible.value = false
        emits('comfirm')
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const handleInfoAddField = () => {
  tableSelectedKeys.value = renderData.value.map((item) => item.tmplModuleFieldId)
  modalVisible.value = true
  nextTick(() => {
    mapFieldModalRef.value?.getMainTmplInfo(props.tmplId)
  })
}

const handleMapFieldModalComfirm = (data: REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[]) => {
  // 复制一份原有数据
  let copyRenderData = _.cloneDeep(renderData.value)
  renderData.value = data.map((item) => {
    const record = copyRenderData.find((copyItem) => item.tmplModuleFieldId === copyItem.tmplModuleFieldId)
    if (!record) {
      // 参数内容默认值是每选一个字段就在参数内容后面中添加：{{信息模块名称 . 字段名称}}
      baseInfoForm.value.parameterContent += `{{${item.moduleTitle} . ${item.fieldTitle}}}`
    }
    // 判断原有数据是否存在该项，如果存在，抛出原有
    return record ? record : item
  })
}

const handleDelete = (record: REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE) => {
  // 查找下标
  const index = renderData.value.findIndex((item) => item.tmplModuleFieldId === record.tmplModuleFieldId)
  // 根据下标删除
  renderData.value.splice(index, 1)
  // 参数内容默认值是每删除一个字段，就把参数内容中对应的 {{信息模块名称 . 字段名称}} 删除掉
  baseInfoForm.value.parameterContent = baseInfoForm.value.parameterContent.replace(
    new RegExp(`{{${record?.moduleTitle} . ${record?.fieldTitle}}}`, 'g'),
    ''
  )
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const getFormData = () => {
  getDocTemplateMappingInfo(props.mappingId)
    .then((res) => {
      if (res) {
        for (const key in baseInfoForm.value) {
          if (Object.prototype.hasOwnProperty.call(res, key)) {
            baseInfoForm.value[key] = res[key]
          }
        }
        renderData.value = res.caseFields || []
      }
    })
    .catch(() => {
      baseInfoForm.value = generateFormModel()
    })
}

const setFormModel = (modal: REQUEST_POST_DOC_TEMPLATE_MAPPING_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
  renderData.value = []
}

defineExpose({ setFormModel, initFormModel, getFormData })
</script>

<style lang="scss" scoped></style>
