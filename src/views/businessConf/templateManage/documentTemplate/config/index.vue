<template>
  <div class="document-config-page">
    <a-space direction="vertical" :size="10" fill>
      <a-card class="mdt-card-common" title="模板基本信息">
        <a-form
          ref="baseFormRef"
          :model="baseInfoForm"
          :rules="baseInfoRules"
          :label-col-props="{ span: 9 }"
          :wrapper-col-props="{ span: 15 }"
          label-align="left"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item field="docTmplTitle" label="文书名称">
                <a-input v-model="baseInfoForm.docTmplTitle" placeholder="请输入内容" readonly />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="tmplTitle" label="关联案件模板">
                <a-input v-model="baseInfoForm.tmplTitle" placeholder="请输入内容" readonly />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="docTmplType" label="生成方式">
                <a-input v-model="docTmplTypeObj[baseInfoForm.docTmplType]" placeholder="请输入内容" readonly />
                <!-- <a-select
                  v-model="baseInfoForm.docTmplType"
                  :options="docTmplTypeOptions"
                  placeholder="请选择"
                  readonly
                /> -->
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="appearanceUserName" label="模板文件">
                <a-upload :auto-upload="false" :show-file-list="false" action="/" accept=".pdf" @change="onFileChange">
                  <template #upload-button>
                    <!-- 最大宽度150px,超出隐藏 -->
                    <a-space v-if="baseInfoForm.docPath" :size="10">
                      <a-tooltip :content="baseInfoForm.docPath">
                        <a-button
                          type="text"
                          style="width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                          @click.stop="previewFile"
                        >
                          {{ baseInfoForm.docName }}
                        </a-button>
                      </a-tooltip>
                      <icon-upload />
                    </a-space>
                    <a-button v-else>
                      <template #icon><icon-upload /></template>上传文件
                    </a-button>
                  </template>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
      <a-card class="mdt-card-common" title="模板配置">
        <template #extra>
          <a-button type="primary" @click="handleInfoAdd">
            <template #icon><icon-plus /> </template> 新建
          </a-button>
        </template>
        <a-table
          row-key="mappingId"
          :loading="loading"
          :pagination="false"
          :columns="columns"
          :data="renderData"
          :bordered="false"
        >
          <template #rank="{ rowIndex }"> {{ rowIndex < 9 ? '0' : '' }}{{ rowIndex + 1 }} </template>
          <template #allowMultipleRecord="{ record }">
            {{ mapMethodsObj[record.allowMultipleRecord] || record.allowMultipleRecord }}
          </template>
          <template #operations="{ record }">
            <a-button type="text" @click="handleConfig(record)">配置</a-button>
            <a-button type="text" @click="handleDelete(record)">删除</a-button>
          </template>
        </a-table>
      </a-card>
    </a-space>
    <ModuleConfigModal
      ref="modalgRef"
      v-model:visible="modalVisible"
      :tmpl-id="baseInfoForm.tmplId"
      :mapping-id="mappingId"
      title="字段映射配置"
      @comfirm="getTableData"
    />
    <FilePreviewModal v-model:visible="fileModalVisiable" :viewer-url="viewerUrl" :title="viewerTitle" />
  </div>
</template>

<script lang="ts" setup>
import { deleteDocTemplateMapping, getDocTemplateInfo } from '@/api/arcoApi/businessConf/docTemplateManage'
import { getDocTemplateMappingList, uploadDocTemplate } from '@/api/arcoApi/businessConf/docTemplateManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import FilePreviewModal from '@/views/case/caseDetails/components/modal/ifrme-preview-modal.vue'
import ModuleConfigModal from './components/module-config-modal.vue'
import { provide, computed, ref, nextTick } from 'vue'
import { Modal, Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import { useRoute } from 'vue-router'
import _ from 'lodash'

import { pdfJsViewer } from '@/assets/ts/const'
import { preview } from '@/api/commonApi/file'
import Qs from 'qs'

const route = useRoute()

const generateFormModel = () => {
  return {
    docTmplTitle: '',
    docTmplType: '',
    docTmplId: '',
    tmplTitle: '',
    docName: '',
    docPath: '',
    tmplId: ''
  }
}

const baseInfoForm = ref<REQUEST_GET_DOC_TEMPLATE_INFO_TYPE>(generateFormModel())
const renderData = ref<REQUEST_POST_DOC_TEMPLATE_MAPPING_LIST_DATA_TYPE[]>([])
const modalgRef = ref<InstanceType<typeof ModuleConfigModal>>()
const mappingId = ref<string | number>('')
const baseFormRef = ref<FormInstance>()
const fileModalVisiable = ref(false)
const modalVisible = ref(false)
const loading = ref(false)

const viewerTitle = ref('')
const viewerUrl = ref('')

const baseInfoRules = ref({
  appearanceName: [{ required: true, message: '请选择登录页名称' }],
  appearancePath: [{ required: true, message: '请输入路径' }]
})

const docTmplTypeOptions = ref<SelectOptionData[]>(dict.docTmplTypeOptions)
const mapMethodsOptions = ref<SelectOptionData[]>(dict.mapMethodsOptions)

const docTmplId = ref<string>(route.params.tmplId as string)
provide('docTmplId', docTmplId.value)

const docTmplTypeObj = computed(() => {
  return dictEnumValToObject(docTmplTypeOptions.value)
})

const mapMethodsObj = computed(() => {
  return dictEnumValToObject(mapMethodsOptions.value)
})

const columns = computed<TableColumnData[]>(() => [
  { width: 60, title: '序号', dataIndex: 'rank', slotName: 'rank' },
  { width: 140, tooltip: true, ellipsis: true, title: '参数名称', dataIndex: 'parameterName' },
  { width: 160, tooltip: true, ellipsis: true, title: '映射字段', dataIndex: 'mappingField' },
  { width: 180, tooltip: true, ellipsis: true, title: '所属模块名称', dataIndex: 'moduleName' },
  { width: 140, tooltip: true, ellipsis: true, title: '映射方式', dataIndex: 'allowMultipleRecord', slotName: 'allowMultipleRecord' },
  { width: 120, tooltip: true, ellipsis: true, title: '分隔符', dataIndex: 'delimiter' },
  { width: 180, tooltip: true, ellipsis: true, title: '参数内容', dataIndex: 'parameterContent' },
  { width: 200, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const previewFile = () => {
  let {docPath, docName} = baseInfoForm.value
  viewerUrl.value = pdfJsViewer + '?' + Qs.stringify({ file: preview({ fileFolder: docPath, fileName: '' }) })
   viewerTitle.value = docName
  fileModalVisiable.value = true
}

const handleConfig = (row: REQUEST_POST_DOC_TEMPLATE_MAPPING_LIST_DATA_TYPE) => {
  mappingId.value = row.mappingId
  modalVisible.value = true
  nextTick(() => {
    modalgRef.value?.getFormData()
  })
}

const handleDelete = (row: REQUEST_POST_DOC_TEMPLATE_MAPPING_LIST_DATA_TYPE) => {
  Modal.info({
    title: '提示',
    hideCancel: false,
    bodyStyle: { padding: '0 30px' },
    content: '删除后将不可恢复, 是否确认删除？',
    onOk: () => {
      deleteDocTemplateMapping(row.mappingId).then(() => {
        Message.success('操作成功')
        getTableData()
      })
    },
    onCancel: () => {}
  })
}

const handleInfoAdd = () => {
  mappingId.value = ''
  modalVisible.value = true
  nextTick(() => {
    modalgRef.value?.initFormModel()
  })
}

const onFileChange = (fileList: FileItem[]) => {
  if (fileList.length > 1) {
    fileList.splice(0, 1)
  }
  if (!fileList[0].file) {
    return
  }
  let param = new FormData()
  param.append('multipartFile', fileList[0].file)
  uploadDocTemplate(docTmplId.value, param)
    .then(() => {
      Message.success('上传成功')
      getFormData()
    })
    .finally(() => {})
}

const getFormData = () => {
  getDocTemplateInfo(docTmplId.value)
    .then((res) => {
      if (res) {
        for (const key in baseInfoForm.value) {
          if (Object.prototype.hasOwnProperty.call(res, key)) {
            baseInfoForm.value[key] = res[key]
          }
        }
      }
    })
    .catch(() => {
      baseInfoForm.value = generateFormModel()
    })
}

const getTableData = () => {
  getDocTemplateMappingList(docTmplId.value).then((res) => {
    renderData.value = res || []
  })
}

if (docTmplId.value) {
  getTableData()
  getFormData()
}
</script>

<script lang="ts">
export default {
  name: 'Basic'
}
</script>

<style lang="scss" scoped>
.document-config-page {
  background-color: var(--color-fill-2) !important;
}

.document-config-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
