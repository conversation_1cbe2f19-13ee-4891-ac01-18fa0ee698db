<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="docTmplTitle" label="模板名称">
                  <a-input v-model="searchForm.docTmplTitle" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="docTmplType" label="模板类型">
                  <a-select
                    v-model="searchForm.docTmplType"
                    :options="docTmplTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsId" label="案源方">
                  <a-select
                    v-model="searchForm.entrustsId"
                    :options="entrustsOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="natureTitle" label="案由">
                  <a-select
                    v-model="searchForm.natureTitle"
                    :options="natureTitleOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="updaterName" label="更新人">
                  <a-input v-model="searchForm.updaterName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="tmplStatus" label="模板状态">
                  <a-select v-model="searchForm.tmplStatus" :options="statusOptions" placeholder="请选择" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row>
        <a-col v-auth="['docCreate']" style="margin-bottom: 16px" :span="24">
          <a-space>
            <a-button type="primary" @click="handleInfoAdd">
              <template #icon><icon-plus /> </template> 新建
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="docTmplId"
        :loading="loading"
        :pagination="pagination"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #tmplStatus="{ record }">
          <a-switch
            :default-checked="record.tmplStatus === 1 ? true : false"
            checked-value="1"
            unchecked-value="0"
            @click="handleSwitchStatus(record)"
          ></a-switch>
        </template>
        <template #docTmplType="{ record }">
          {{ docTmplTypeObj[record.docTmplType] || record.docTmplType }}
        </template>
        <template #operations="{ record }">
          <a-button v-auth="['docConf']" type="text" @click="handleConfig(record)">配置</a-button>
          <a-button v-auth="['docDelete']" type="text" @click="handleDelete(record)">删除</a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getDocTemplateList, saveDocTemplate } from '@/api/arcoApi/businessConf/docTemplateManage'
import { deleteDocTemplate } from '@/api/arcoApi/businessConf/docTemplateManage'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import { getCaseNatures } from '@/api/eleApi/common'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { computed, ref, reactive, watch } from 'vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import { useRouter } from 'vue-router'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    docTmplTitle: '',
    entrustsName: '',
    docTmplType: '',
    updaterName: '',
    natureTitle: '',
    tmplStatus: '',
    entrustsId: '',
    docTmplId: '',
    docPath: '',
    remark: '',
    tmplId: ''
  }
}

const { loading, setLoading } = useLoading(true)

const searchForm = ref<REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_DOC_TEMPLATE_LIST_TYPE[]>([])
const lisPageTitle = ref<string>('文书模板列表')
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 220, fixed: 'left', title: '模板编号', dataIndex: 'docTmplId' },
  { width: 140, tooltip: true, ellipsis: true, title: '文书模板名称', dataIndex: 'docTmplTitle' },
  { width: 120, tooltip: true, ellipsis: true, title: '制作方式', dataIndex: 'docTmplType', slotName: 'docTmplType' },
  { width: 220, tooltip: true, ellipsis: true, title: '所属案件模板', dataIndex: 'tmplTitle' },
  { width: 220, tooltip: true, ellipsis: true, title: '案源方', dataIndex: 'entrustsName' },
  { width: 180, tooltip: true, ellipsis: true, title: '案由', dataIndex: 'natureTitle' },
  { width: 120, tooltip: true, ellipsis: true, title: '更新人', dataIndex: 'updaterName' },
  { width: 120, align: 'center', title: '模板状态', dataIndex: 'tmplStatus', slotName: 'tmplStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '修改时间', dataIndex: 'updateTime' },
  { width: 140, tooltip: true, ellipsis: true, title: '备注', dataIndex: 'remark' },
  { width: 200, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const docTmplTypeOptions = ref<SelectOptionData[]>(dict.docTmplTypeOptions)

const statusOptions = ref<SelectOptionData[]>(dict.statusOptions)
const natureTitleOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])

const docTmplTypeObj = computed(() => {
  return dictEnumValToObject(docTmplTypeOptions.value)
})

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getDocTemplateList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const handleSwitchStatus = (row: REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE) => {
  row.tmplStatus = row.tmplStatus === 1 ? 0 : 1
  saveDocTemplate(row).then(() => {
    Message.success('操作成功')
    getTableData()
  })
}

const handleInfoAdd = () => {
  router.push({ name: 'documentTemplateCreate' })
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handleConfig = (row: REQUEST_GET_DOC_TEMPLATE_LIST_TYPE) => {
  let id = row.docTmplId
  router.push({ path: `documentTemplateConfig/${id}` })
}

const handleDelete = (row: REQUEST_GET_DOC_TEMPLATE_LIST_TYPE) => {
  Modal.info({
    title: '提示',
    hideCancel: false,
    bodyStyle: { padding: '0 30px' },
    content: '删除后将不可恢复，相关模板将无法在更新 是否确认删除？',
    onOk: () => {
      deleteDocTemplate(row.docTmplId).then(() => {
        Message.success('操作成功')
        getTableData()
      })
    },
    onCancel: () => {}
  })
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

const getCaseNaturesData = () => {
  getCaseNatures().then((res) => {
    if (res && res.length) {
      natureTitleOptions.value = res.map((item) => ({ label: item.dictTag, value: item.dictTag}))
    }
  })
}

getCaseNaturesData()
getAllEntrusts()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
