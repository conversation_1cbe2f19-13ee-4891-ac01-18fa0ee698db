<template>
  <div class="document-create-page">
    <a-card :bordered="false" class="document-create-card">
      <template #title>
        <span class="bold">创建文书模板</span>
      </template>
      <div class="wrapper">
        <a-steps v-model:current="step" style="width: 400px" line-less class="steps">
          <a-step description="创建模板信息"> 选择基础信息 </a-step>
          <a-step description="创建成功"> 完成创建 </a-step>
        </a-steps>
        <keep-alive>
          <BaseInfo v-if="step === 1" ref="baseInfoRef" @comfirm="baseInfoComfirm" @cancel="baseInfoCancel" />
          <Status v-else-if="step === 2" :status="status" @opreation="handleOpreation" />
        </keep-alive>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import BaseInfo from './components/base-info.vue'
import Status from './components/status.vue'
import { useRouter } from 'vue-router'
import { nextTick, ref } from 'vue'

const router = useRouter()

const status = ref<'info' | 'success' | 'warning' | 'error' | '403' | '404' | '500' | null>('info')
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const docTmplId = ref<string | number>('')

const step = ref(1)
const baseInfoComfirm = (state: typeof status.value, id?: string | number) => {
  status.value = state
  step.value = 2
  if (id) {
    docTmplId.value = id
  }
}

const baseInfoCancel = () => {
  router.push({ name: `documentTemplateList` })
}

const handleOpreation = (val: string) => {
  switch (val) {
    case 'viewTmpl':
      router.push({ path: `documentTemplateConfig/${docTmplId.value}` })
      break
    case 'createTmpl':
      docTmplId.value = ''
      step.value = 1
      nextTick(() => baseInfoRef.value?.resetModel())
      break
    case 'toList':
      router.push({ name: `documentTemplateList` })
      break
    case 'backUpdate':
      step.value = 1
      break
  }
}
</script>

<style scoped lang="scss">
.document-create-page {
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-bg-2);
    :deep(.arco-form) {
      .arco-form-item {
        &:last-child {
          margin-top: 20px;
        }
      }
    }
  }

  .steps {
    margin-bottom: 45px;
  }
}
</style>
