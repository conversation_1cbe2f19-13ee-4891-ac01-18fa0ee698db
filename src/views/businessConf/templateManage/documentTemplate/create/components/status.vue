<template>
  <div class="status-wrap">
    <a-result :status="status" :title="title" :subtitle="subtitle" />
    <a-space v-if="status === 'success'" :size="16">
      <a-button key="view" @click="handleBtnClick('viewTmpl')"> 查看模板 </a-button>
      <a-button key="again" type="primary" @click="handleBtnClick('createTmpl')"> 再次创建 </a-button>
    </a-space>
    <a-space v-else :size="16">
      <a-button key="view" @click="handleBtnClick('toList')"> 回到首页 </a-button>
      <a-button key="again" type="primary" @click="handleBtnClick('backUpdate')"> 返回修改 </a-button>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  status?: 'info' | 'success' | 'warning' | 'error' | '403' | '404' | '500' | null
}

const props = withDefaults(defineProps<Props>(), {
  status: 'success'
})

const emits = defineEmits(['opreation'])

const handleBtnClick = (val: string) => {
  emits('opreation', val)
}

const subtitle = computed(() => {
  if (props.status === 'success') {
    return '模板创建成功'
  } else {
    return '模板创建失败!'
  }
})

const title = computed(() => {
  if (props.status === 'success') {
    return '创建成功'
  } else {
    return '创建失败'
  }
})
</script>

<style scoped lang="scss">
.status-wrap {
  text-align: center;
}
:deep(.arco-result) {
  padding-top: 0;
}
</style>
