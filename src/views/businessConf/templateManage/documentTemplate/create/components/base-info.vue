<template>
  <a-spin>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      class="form"
    >
      <a-form-item field="docTmplTitle" label="模板名称" :max-length="50">
        <a-input v-model="formData.docTmplTitle" placeholder="请输入" allow-clear />
      </a-form-item>
      <a-form-item field="docTmplType" label="制作方式">
        <a-select v-model="formData.docTmplType" :options="docTmplTypeOptions" placeholder="请选择" allow-clear />
      </a-form-item>
      <a-form-item field="tmplId" label="案件基础模板">
        <a-select v-model="formData.tmplId" :options="tmplOptions" placeholder="请选择" allow-clear />
      </a-form-item>
      <a-form-item field="remark" label="备注">
        <a-input v-model="formData.remark" :max-length="250" placeholder="请输入" allow-clear />
      </a-form-item>
      <a-form-item class="base-form-justify">
        <a-space :size="20">
          <a-button @click="onCancelClick">取消</a-button>
          <a-button :loading="loading" type="primary" @click="onComfirmClick">保存</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { getAllTemplateList } from '@/api/arcoApi/businessConf/caseTemplateManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import dict from '@/dict/businessConf'
import { ref } from 'vue'
import { saveDocTemplate } from '@/api/arcoApi/businessConf/docTemplateManage'

const emits = defineEmits(['comfirm', 'cancel'])

const generateFormModel = () => {
  return {
    docTmplTitle: '',
    entrustsName: '',
    docTmplType: '',
    updaterName: '',
    natureTitle: '',
    tmplStatus: '',
    entrustsId: '',
    docTmplId: '',
    docPath: '',
    remark: '',
    tmplId: ''
  }
}

const formData = ref<REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE>(generateFormModel())
const docTmplTypeOptions = ref<SelectOptionData[]>(dict.docTmplTypeOptions)
const tmplOptions = ref<SelectOptionData[]>([])
const formRef = ref<FormInstance>()
const loading = ref(false)

const formRules = ref({
  docTmplTitle: [{ required: true, message: '请输入模板名称' }],
  docTmplType: [{ required: true, message: '请选择制作方式' }],
  tmplId: [{ required: true, message: '请选择案件基础模板' }]
})

const resetModel = () => {
  formRef.value?.resetFields()
  getTemplOptions()
}

const setModel = (model: REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE) => {
  if (model) {
    formData.value = model
  }
}

const onComfirmClick = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    loading.value = true
    saveDocTemplate(formData.value)
      .then((res) => res && emits('comfirm', 'success', res))
      .catch(() => emits('comfirm', 'error'))
      .finally(() => {
        loading.value = false
      })
  }
}

const onCancelClick = async () => {
  const res = await formRef.value?.resetFields()
  if (!res) {
    emits('cancel')
  }
}

const getTemplOptions = () => {
  getAllTemplateList().then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
    }
  })
}

getTemplOptions()

defineExpose({ resetModel, setModel })
</script>

<style scoped lang="scss">
.form {
  width: 500px;
}

.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}
</style>
