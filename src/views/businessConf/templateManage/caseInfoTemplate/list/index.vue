<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="tmplTitle" label="模板名称">
                  <a-input v-model="searchForm.tmplTitle" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="tmplType" label="模板类型">
                  <a-select v-model="searchForm.tmplType" :options="tmplTypeOptions" placeholder="请选择" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsId" label="案源方">
                  <a-select
                    v-model="searchForm.entrustsId"
                    :options="entrustsOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="natureTitle" label="案由">
                  <a-select
                    v-model="searchForm.natureTitle"
                    :options="natureTitleOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="updaterName" label="更新人">
                  <a-input v-model="searchForm.updaterName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="tmplStatus" label="模板状态">
                  <a-select v-model="searchForm.tmplStatus" :options="statusOptions" placeholder="请选择" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row>
        <a-col v-auth="['tmplSave']" style="margin-bottom: 16px" :span="24">
          <a-space>
            <a-button type="primary" @click="handleInfoAdd">
              <template #icon><icon-plus /> </template> 新建
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #tmplStatus="{ record }">
          <a-switch
            v-model="record.tmplStatus"
            :default-checked="record.tmplStatus === 1? true : false"
            :checked-value="1"
            :unchecked-value="0"
            disabled
          ></a-switch>
        </template>
        <template #tmplType="{ record }">
          {{ tmplTypeObj[record.tmplType] || record.tmplType }}
        </template>
        <template #operations="{ record }">
          <a-button v-auth="['tmplSave']" type="text" @click="handleConfig(record)">配置</a-button>
          <a-button v-auth="['tmplView']" type="text" @click="handleDwoload(record)">下载</a-button>
          <a-button v-auth="['tmplDelete']" type="text" @click="handleDelete(record)">删除</a-button>
          <a-button v-auth="['tmplSave']" type="text" @click="handleEdit(record)">编辑</a-button>
        </template>
      </a-table>
      <a-modal v-model:visible="modalVisible" :footer="false" width="600px" title="模板信息编辑" title-align="start">
        <BaseInfo
          ref="baseInfoRef"
          comfirm-text="保存"
          :disabled-select="true"
          :tmpl-options="tmplOptions"
          :entrusts-options="entrustsOptions"
          :nature-title-options="natureTitleOptions"
          @comfirm="handleComfirm"
          @cancel="handleCancel"
        />
      </a-modal>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getAllTemplateList, deleteMainTemplate, downloadTmpl } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { getPageMainTemplateList, deleteDeputyTemplate } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import { getCaseNatures } from '@/api/eleApi/common'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import { computed, nextTick, ref, reactive, watch } from 'vue'
import BaseInfo from '../create/components/base-info.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import { useRouter } from 'vue-router'
import sysUtil from '@/utils/sysUtil'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    entrustsName: '',
    isDesensitize: 0,
    natureTitle: '',
    updaterName: '',
    entrustsId: '',
    tmplStatus: '',
    tmplTitle: '',
    companyId: '',
    tmplDesc: '',
    tmplType: '',
    tmplId: ''
  }
}

const { loading, setLoading } = useLoading(true)

const searchForm = ref<REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_TEMPLATE_LIST_TYPE[]>([])
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const lisPageTitle = ref<string>('模板列表')
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const modalVisible = ref(false)

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 120, fixed: 'left', title: '模板编号', dataIndex: 'id' },
  { width: 140, tooltip: true, ellipsis: true, title: '模板名称', dataIndex: 'tmplTitle' },
  { width: 140, tooltip: true, ellipsis: true, title: '关联模板名称', dataIndex: 'baseTmplTitle' },
  { width: 120, tooltip: true, ellipsis: true, title: '模板类型', dataIndex: 'tmplType', slotName: 'tmplType' },
  { width: 220, tooltip: true, ellipsis: true, title: '案源方', dataIndex: 'entrustsName' },
  { width: 180, tooltip: true, ellipsis: true, title: '案由', dataIndex: 'natureTitle' },
  { width: 120, tooltip: true, ellipsis: true, title: '更新人', dataIndex: 'updaterName' },
  { width: 120, align: 'center', title: '模板状态', dataIndex: 'tmplStatus', slotName: 'tmplStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '修改时间', dataIndex: 'updateTime' },
  { width: 140, tooltip: true, ellipsis: true, title: '备注', dataIndex: 'tmplDesc' },
  { width: 280, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const tmplTypeOptions = ref<SelectOptionData[]>(dict.tmplTypeOptions)
const statusOptions = ref<SelectOptionData[]>(dict.statusOptions)
const natureTitleOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const tmplOptions = ref<SelectOptionData[]>([])

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getPageMainTemplateList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const handleInfoAdd = () => {
  router.push({ name: 'templateCreate' })
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handleConfig = (row: REQUEST_GET_TEMPLATE_LIST_TYPE) => {
  let id = row.tmplType === '1' ? row.tmplId : row.deputyTmplId
  router.push({ path: `templateConf/${id}`, query: { type: row.tmplType === '1' ? 1 : 0 } })
}

const handleDwoload = (row: REQUEST_GET_TEMPLATE_LIST_TYPE) => {
  let id = row.tmplType === '1' ? row.tmplId : row.deputyTmplId
  downloadTmpl({ tmplType: row.tmplType, tmplId: id }).then((res) => {
    Message.success('下载成功')
    sysUtil.blobExport(res)
  })
}

const handleDelete = (row: REQUEST_GET_TEMPLATE_LIST_TYPE) => {
  Modal.info({
    title: '提示',
    hideCancel: false,
    bodyStyle: { padding: '0 30px' },
    content: '删除后将不可恢复，相关模板将无法在更新 是否确认删除？',
    onOk: () => {
      if (row.tmplType === '1') {
        deleteMainTemplate(row.tmplId).then(() => {
          Message.success('操作成功')
          getTableData()
        })
      } else {
        deleteDeputyTemplate(row.deputyTmplId).then(() => {
          Message.success('操作成功')
          getTableData()
        })
      }
    },
    onCancel: () => {}
  })
}

const handleEdit = (row: REQUEST_GET_TEMPLATE_LIST_TYPE) => {
  if (row.id) {
    modalVisible.value = true
    nextTick(() => {
      let entrustsItem = entrustsOptions.value.find((item) => item.label === row.entrustsName)
      baseInfoRef.value?.setModel({
        entrustsId: entrustsItem ? (entrustsItem.value as string) : '',
        id: row.tmplType === '1' ? row.tmplId : row.deputyTmplId,
        parentId: row.tmplType === '1' ? '' : row.tmplId,
        entrustsName: row.entrustsName,
        natureTitle: row.natureTitle,
        updaterName: row.updaterName,
        tmplStatus: row.tmplStatus,
        tmplTitle: row.tmplTitle,
        tmplType: row.tmplType,
        tmplDesc: row.tmplDesc
      })
    })
  }
}

const handleComfirm = (str: string) => {
  if (str === 'submit') {
    modalVisible.value = false
    getTableData()
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

const getTemplOptions = () => {
  getAllTemplateList().then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
    }
  })
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

const getCaseNaturesData = () => {
  getCaseNatures().then((res) => {
    if (res && res.length) {
      natureTitleOptions.value = res.map((item) => ({ label: item.dictTag, value: item.dictTag }))
    }
  })
}

getCaseNaturesData()
getTemplOptions()
getAllEntrusts()
getTableData()

const tmplTypeObj = computed(() => {
  return dictEnumValToObject(tmplTypeOptions.value)
})

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
