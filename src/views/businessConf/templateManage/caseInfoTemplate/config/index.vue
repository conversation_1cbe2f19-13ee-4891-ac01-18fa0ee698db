<template>
  <div class="template-conf-page">
    <a-card :bordered="false" class="template-conf-card template-select-card">
      <template #title>
        <span class="bold">模板信息</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="tmplInfo"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
            :disabled="true"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="tmplTitle" label="模板名称">
                  <a-input v-model="tmplInfo.tmplTitle" placeholder="请输入内容" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="tmplType" label="模板类型">
                  <a-select v-model="tmplInfo.tmplType" :options="tmplTypeOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsName" label="案源方">
                  <a-select v-model="tmplInfo.entrustsName" :options="entrustsOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="updaterName" label="案件批次">
                  <a-input v-model="tmplInfo.updaterName" placeholder="请输入内容" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="natureTitle" label="案由">
                  <a-select v-model="tmplInfo.natureTitle" :options="natureTitleOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="tmplStatus" label="模板状态">
                  <a-select v-model="tmplInfo.tmplStatus" :options="statusOptions" placeholder="请选择" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="showEditModel"> 编辑 </a-button>
            <!-- <a-button> 脱敏 </a-button> -->
          </a-space>
        </a-col>
      </a-row>
    </a-card>
    <a-spin :loading="loading" class="spin-content">
      <a-card :bordered="false" class="template-conf-card template-select-card">
        <template #title>模块名称 </template>
        <div class="module-tree-box">
          <a-input-search v-model="searchKey" style="margin-bottom: 8px" />
          <a-tree
            ref="treeRef"
            v-model:selected-keys="selectedKeys"
            :draggable="!isMainTmpl"
            :block-node="true"
            :data="treeData"
            :virtual-list-props="{ height: '43vh' }"
            @select="handleTreeSelect"
            @drop="handleTreeDrop"
          >
            <template #title="nodeData">
              <a-tooltip :content="nodeData?.title">
                <div class="module-tree-text">
                  <template v-if="getMatchIndex(nodeData?.title) < 0">{{ nodeData?.title }}</template>
                  <span v-else>
                    {{ nodeData?.title?.substr(0, getMatchIndex(nodeData?.title)) }}
                    <span style="color: var(--color-primary-light-4)">
                      {{ nodeData?.title?.substr(getMatchIndex(nodeData?.title), searchKey.length) }}
                    </span>
                    {{ nodeData?.title?.substr(getMatchIndex(nodeData?.title) + searchKey.length) }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template #extra="nodeData">
              <div v-if="nodeData.mainId === 'module3' && isMainTmpl" class="module-icon-box">
                <IconDown class="module-icon module-icon-down" @click="handleNodeMove(nodeData, 'down')"></IconDown>
                <IconUp class="module-icon module-icon-up" @click="handleNodeMove(nodeData, 'up')"></IconUp>
                <IconEdit class="module-icon module-icon-edit" @click="handleNodeEdit(nodeData)"></IconEdit>
                <IconDelete class="module-icon module-icon-delete" @click="handleNodeDelete(nodeData)"></IconDelete>
              </div>
            </template>
          </a-tree>
        </div>
        <div v-if="isMainTmpl" class="module-tree-operation">
          <a-button class="template-module-add-btn" @click="handleModuleAdd">
            <template #icon><icon-plus /> </template> 新增模块
          </a-button>
        </div>
      </a-card>
      <div class="template-select-table">
        <div class="template-select-table-box mdt-common-scrollbar">
          <module-field
            v-if="isMainTmpl"
            :show-add="showAdd"
            :show-operation="showOperation"
            :field-data="moduleFields"
            :tmpl-id="sTmplId"
            :tmpl-module-id="sTmplModuleId"
            @delete="handleTmplIdChange"
            @field-save="handleTmplIdChange"
          ></module-field>
          <FieldConfig
            v-else
            :tmpl-id="sTmplId"
            :tmpl-module-id="sTmplModuleId"
            :target-module="targetModule"
            :main-tmpl-table-data="moduleFields"
            @ok="getDeputyInfoOk"
          />
        </div>
      </div>
    </a-spin>
    <a-modal v-model:visible="tmplModalVisible" :footer="false" width="600px" title="模板信息编辑" title-align="start">
      <BaseInfo
        ref="baseInfoRef"
        comfirm-text="保存"
        :disabled-select="true"
        :tmpl-options="tmplOptions"
        :entrusts-options="entrustsOptions"
        :nature-title-options="natureTitleOptions"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-modal>
    <a-modal
      v-model:visible="moduleModalVisible"
      :ok-loading="moduleModalLoading"
      width="500px"
      title="模块信息"
      ok-text="保存"
      title-align="start"
      :on-before-ok="handleSaveModule"
    >
      <a-form
        ref="moduleFormRef"
        :model="moduleFormData"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 16 }"
      >
        <a-form-item
          field="moduleTitle"
          label="模块名称"
          :max-length="50"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input v-model="moduleFormData.moduleTitle" placeholder="请输入" />
        </a-form-item>
        <a-form-item field="docTmplType" label="模块记录">
          <a-select
            v-model="moduleFormData.allowMultipleRecord"
            :options="multipleRecordOptions"
            placeholder="请选择"
            readonly
          />
        </a-form-item>
        <a-form-item field="moduleDesc" label="模块描述">
          <a-input v-model="moduleFormData.moduleDesc" placeholder="请输入" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { deleteMainTemplateModule, saveMainTemplateModule } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { tmplCaseModuleMoveDown, tmplCaseModuleMoveUp } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { getAllTemplateList, getModuleFieldList } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { findAllEntrusts } from '@/api/eleApi/case/batchManage'
import { getCaseNatures } from '@/api/eleApi/common'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

import type { TreeNodeData } from '@arco-design/web-vue/es/tree'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import type { TreeInstance } from '@arco-design/web-vue'

import ModuleField from './components/moduleField/index.vue'
import FieldConfig from './components/fieldConf/index.vue'
import BaseInfo from '../create/components/base-info.vue'
import { useTabBarStore } from '@/layouts/appArco/store'
import { Modal, Message } from '@arco-design/web-vue'
import { useRouter, useRoute } from 'vue-router'
import { computed, nextTick, ref } from 'vue'
import dict from '@/dict/businessConf'

const route = useRoute()
const router = useRouter()

const routerQuery = route.query
const routerParams = route.params

const tabBarStore = useTabBarStore()

const sTmplId = ref<number | null>(null)
const parentId = ref<number | string>('')
const sTmplModuleId = ref<number | null>(null)

type MODULE_KEY_STRING_TYPE = 'moduleAll' | 'module1' | 'module2' | 'module3'

const searchKey = ref('')

const loading = ref(false)
const showAdd = ref(false)
const isMainTmpl = ref(false)
const showOperation = ref(false)
const tmplModalVisible = ref(false)
const moduleModalLoading = ref(false)

const treeRef = ref<TreeInstance>()
const moduleFormRef = ref<FormInstance>()
const selectedKeys = ref<(string | number)[]>()
const targetModule = ref<MODULE_KEY_STRING_TYPE>('moduleAll')

const moduleModalVisible = ref(false)
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>()
const tmplInfo = ref<REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE>({
  entrustsName: '',
  isDesensitize: null,
  natureTitle: '',
  updaterName: '',
  entrustsId: '',
  tmplStatus: '',
  tmplTitle: '',
  companyId: '',
  tmplDesc: '',
  tmplType: '',
  tmplId: ''
})

const generateModuleFormModel = () => {
  return {
    allowMultipleRecord: 0,
    tmplModuleId: null,
    moduleTitle: '',
    moduleDesc: '',
    tmplId: null
  }
}

const moduleFormData = ref<REQUEST_POST_MODULE_SAVE_TYPE>(generateModuleFormModel())

interface TEMPLATE_CONFTREE_TREE_DATA_TYPE extends TreeNodeData {
  children?: TEMPLATE_CONFTREE_TREE_DATA_TYPE[]
  fields?: REQUEST_GET_FIELD_LIST_TYPE[]
  mainId?: string | number
  key: string | number
  recordType?: number
  title: string
  desc?: string
}

const mainTmplTreeData = ref<TEMPLATE_CONFTREE_TREE_DATA_TYPE[]>([
  { title: '系统模块', key: 'module1', draggable: false, mainId: 0, children: [] },
  { title: '业务模块', key: 'module2', draggable: false, mainId: 0, children: [] },
  { title: '自定义模块', key: 'module3', draggable: false, mainId: 0, children: [] }
])

// interface DEPUTY_TMPL_TREE_DATA_TYPE {
//   moduleAll: { title: string; key: string; mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[] }
//   module1: { title: string; key: string; mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[] }
//   module2: { title: string; key: string; mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[] }
//   module3: { title: string; key: string; mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[] }
// }

// const deputyTmplTreeData = ref<DEPUTY_TMPL_TREE_DATA_TYPE>({
//   moduleAll: { title: '系统模块', key: 'module1', mappings: [] },
//   module1: { title: '系统模块', key: 'module1', mappings: [] },
//   module2: { title: '业务模块', key: 'module2', mappings: [] },
//   module3: { title: '自定义模块', key: 'module3', mappings: [] }
// })

const statusOptions = ref<SelectOptionData[]>([
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
])

const tmplTypeOptions = ref<SelectOptionData[]>(dict.tmplTypeOptions)
const multipleRecordOptions = ref<SelectOptionData[]>(dict.multipleRecordOptions)

const tmplOptions = ref<SelectOptionData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const natureTitleOptions = ref<SelectOptionData[]>([])
const moduleFields = ref<REQUEST_GET_FIELD_LIST_TYPE[]>([])

const treeData = computed(() => {
  if (!searchKey.value) return mainTmplTreeData.value
  return searchData(searchKey.value)
})

function searchData(keyword: string) {
  const loop = (data: TEMPLATE_CONFTREE_TREE_DATA_TYPE[]) => {
    const result: TEMPLATE_CONFTREE_TREE_DATA_TYPE[] = []
    data.forEach((item) => {
      if (item.title.toLowerCase().indexOf(keyword.toLowerCase()) > -1) {
        result.push({ ...item })
      } else if (item.children) {
        const filterData = loop(item.children)
        if (filterData.length) {
          result.push({ ...item, children: filterData })
        }
      }
    })
    return result
  }

  return loop(mainTmplTreeData.value)
}

function getMatchIndex(title: string) {
  if (!searchKey.value) return -1
  return title.toLowerCase().indexOf(searchKey.value.toLowerCase())
}

function handleTreeSelect(ids: any, data: any) {
  if (data.node) {
    showAdd.value = data.node.mainId === 'module3'
    showOperation.value = data.node.mainId === 'module3'

    if (data.node.mainId === 0) {
      targetModule.value = data.node.key as MODULE_KEY_STRING_TYPE
      sTmplModuleId.value = null
      moduleFields.value = []
    } else {
      targetModule.value = data.node.mainId as MODULE_KEY_STRING_TYPE
      // 模板类型为导出模板时，字段可以随意选择，其他根据isUpdate字段判断
      const isExport = tmplInfo.value.tmplType === '4'
      moduleFields.value =
        data.node.fields.map((item) => ({ ...item, disabled: isExport ? false : !item.isUpdate })) || []
      sTmplModuleId.value = ids[0]
    }
  }
}

function handleTreeDrop({ dragNode, dropNode, dropPosition }: any) {
  type LOOP_CALLBACK_FUNCTION_TYPE = (
    _: TEMPLATE_CONFTREE_TREE_DATA_TYPE,
    id: string | number,
    data: TEMPLATE_CONFTREE_TREE_DATA_TYPE[]
  ) => void

  type LOOP_FUNCTION_TYPE = (
    data: TEMPLATE_CONFTREE_TREE_DATA_TYPE[],
    key: string | number,
    callback: LOOP_CALLBACK_FUNCTION_TYPE
  ) => void

  if (dropPosition === 0) return
  const data = treeData.value

  const loop: LOOP_FUNCTION_TYPE = (data, key, callback) => {
    data.some((item, index, arr) => {
      if (item.key === key) {
        callback(item, index, arr)
        return true
      }
      if (item.children) {
        return loop(item.children, key, callback)
      }
      return false
    })
  }

  loop(data, dragNode.key, (_, index, arr) => {
    arr.splice(Number(index), 1)
  })
  // loop(data, dropNode.key, (item: TEMPLATE_CONFTREE_TREE_DATA_TYPE) => {
  //   item.children = item.children || []
  //   item.children.push(dragNode)
  // })
  loop(data, dropNode.key, (_, index, arr) => {
    arr.splice(dropPosition < 0 ? Number(index) : Number(index) + 1, 0, dragNode)
  })
}

const handleNodeMove = (node: TEMPLATE_CONFTREE_TREE_DATA_TYPE, method: 'down' | 'up') => {
  if (tmplInfo.value.tmplId) {
    if (method === 'down') {
      tmplCaseModuleMoveDown(node.key).then(() => {
        Message.success('操作成功')
        init()
      })
    } else if (method === 'up') {
      tmplCaseModuleMoveUp(node.key).then(() => {
        Message.success('操作成功')
        init()
      })
    }
  }
}

const handleNodeEdit = (node: TEMPLATE_CONFTREE_TREE_DATA_TYPE) => {
  if (tmplInfo.value.tmplId) {
    moduleModalVisible.value = true
    nextTick(() => {
      moduleFormRef.value?.resetFields()
      moduleFormData.value = {
        allowMultipleRecord: node.recordType || 0,
        tmplModuleId: Number(node.key),
        moduleDesc: node.desc || '',
        moduleTitle: node.title,
        tmplId: sTmplId.value
      }
    })
  }
}

const handleNodeDelete = (node: TEMPLATE_CONFTREE_TREE_DATA_TYPE) => {
  Modal.info({
    title: '删除提示',
    hideCancel: false,
    content: '是否确认删除该模板字段?',
    onOk: () => {
      deleteMainTemplateModule(node.key).then((res) => {
        Message.success('操作成功')
        handleTmplIdChange(res)
      })
    },
    onCancel: () => {}
  })
}

async function handleTmplIdChange(data: REQUEST_MODULE_FIELD_RESINFO_TYPE) {
  if (data.tmplId != Number(routerParams.tmplId)) {
    let idx = tabBarStore.tagList.findIndex((el) => el.fullPath === route.fullPath)
    tabBarStore.deleteTag(idx, tabBarStore.tagList[idx])
    let routeTmplId = route.params.tmplId as string
    router.replace({ path: `${route.path.replace(routeTmplId, data.tmplId.toString())}`, query: routerQuery })
  }
  await init()

  if (data.tmplModuleId) {
    selectedKeys.value = []
    await treeRef.value?.selectNode(data.tmplModuleId)
    nextTick(() => {
      let selectNode = treeRef.value?.getSelectedNodes()
      if (selectNode && selectNode.length) {
        handleTreeSelect([data.tmplModuleId], { node: selectNode[0] })
      }
    })
  }
}

const handleComfirm = (str: string) => {
  if (str === 'submit') {
    init()
  }
}

const handleModuleAdd = () => {
  moduleFormData.value = generateModuleFormModel()
  moduleFormData.value.tmplId = sTmplId.value
  treeRef.value?.expandNode('module3')
  moduleFormRef.value?.resetFields()
  moduleModalVisible.value = true
}

const handleSaveModule = async () => {
  const res = await moduleFormRef.value?.validate()
  if (!res) {
    moduleModalLoading.value = true
    try {
      let res = await saveMainTemplateModule(moduleFormData.value)
      if (res.tmplId) {
        moduleModalLoading.value = false
        Message.success('操作成功')
        handleTmplIdChange(res)
        return true
      } else {
        moduleModalLoading.value = false
        return true
      }
    } catch (error) {
      moduleModalLoading.value = false
    }
  }
  return false
}

const handleCancel = () => {
  tmplModalVisible.value = false
}

const showEditModel = () => {
  if (tmplInfo.value.tmplId) {
    tmplModalVisible.value = true
    nextTick(() => {
      let entrustsItem = entrustsOptions.value.find((item) => item.label === tmplInfo.value.entrustsName)
      baseInfoRef.value?.setModel({
        entrustsId: entrustsItem ? (entrustsItem.value as string) : '',
        updaterName: tmplInfo.value.updaterName || '',
        tmplStatus: Number(tmplInfo.value.tmplStatus),
        entrustsName: tmplInfo.value.entrustsName,
        natureTitle: tmplInfo.value.natureTitle,
        tmplTitle: tmplInfo.value.tmplTitle,
        tmplDesc: tmplInfo.value.tmplDesc,
        tmplType: tmplInfo.value.tmplType,
        id: tmplInfo.value.tmplId,
        parentId: parentId.value
      })
    })
  }
}

const setTmplInfo = (data: any) => {
  tmplInfo.value = {
    isDesensitize: data.isDesensitize,
    entrustsName: data.entrustsName,
    natureTitle: data.natureTitle,
    updaterName: data.updaterName,
    entrustsId: data.entrustsId,
    tmplStatus: data.tmplStatus,
    tmplTitle: data.tmplTitle,
    companyId: data.companyId,
    tmplDesc: data.tmplDesc,
    tmplType: data.tmplType,
    tmplId: isMainTmpl.value ? Number(routerParams.tmplId) : data.tmplId
  }
}

const getMainTmplInfo = async (id: string | number, save = true) => {
  parentId.value = id
  let res = await getModuleFieldList(id)
  let isUpdateTmpl = tmplInfo.value.tmplType == '3'
  mainTmplTreeData.value[0].children = res.systemModules.map((item) => {
    let { moduleTitle, tmplModuleId, moduleFields, moduleDesc, allowMultipleRecord } = item
    return {
      selectable: isUpdateTmpl ? !allowMultipleRecord : true,
      recordType: allowMultipleRecord,
      fields: moduleFields,
      title: moduleTitle,
      mainId: 'module1',
      key: tmplModuleId,
      draggable: false,
      desc: moduleDesc
    }
  })
  mainTmplTreeData.value[1].children = res.businessModules.map((item) => {
    let { moduleTitle, tmplModuleId, moduleFields, moduleDesc, allowMultipleRecord } = item
    return {
      selectable: isUpdateTmpl ? !allowMultipleRecord : true,
      recordType: allowMultipleRecord,
      fields: moduleFields,
      title: moduleTitle,
      mainId: 'module2',
      key: tmplModuleId,
      draggable: false,
      desc: moduleDesc
    }
  })
  mainTmplTreeData.value[2].children = res.customModules.map((item, idx) => {
    let { moduleTitle, tmplModuleId, moduleFields, moduleDesc, allowMultipleRecord } = item
    return {
      selectable: isUpdateTmpl ? !allowMultipleRecord : true,
      recordType: allowMultipleRecord,
      fields: moduleFields,
      title: moduleTitle,
      key: tmplModuleId,
      mainId: 'module3',
      desc: moduleDesc,
      idx
    }
  })
  save && setTmplInfo(res)
}

const getDeputyInfoOk = (res: REQUEST_DEPUTY_MODULE_FIELD_LIST_TYPE) => {
  getMainTmplInfo(res.tmplId, false)
  setTmplInfo(res)
}

const init = async () => {
  if (routerParams.tmplId) {
    sTmplId.value = Number(routerParams.tmplId)
    isMainTmpl.value = routerQuery.type === '1'
    if (sTmplId.value && isMainTmpl.value) {
      await getMainTmplInfo(sTmplId.value)
    }
  }
}

const getTemplOptions = () => {
  getAllTemplateList().then((res) => {
    if (res && res.length) {
      tmplOptions.value = res.map((item) => ({ label: item.tmplTitle, value: item.tmplId }))
    }
  })
}

const getAllEntrusts = () => {
  findAllEntrusts().then((res: unknown) => {
    let data = res as any[]
    if (data && data.length) {
      entrustsOptions.value = data.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

const getCaseNaturesData = () => {
  getCaseNatures().then((res) => {
    if (res && res.length) {
      natureTitleOptions.value = res.map((item) => ({ label: item.dictTag, value: item.dictTag }))
    }
  })
}

getCaseNaturesData()
getTemplOptions()
getAllEntrusts()
init()
</script>
<style scoped lang="scss">
.template-conf-page {
  width: 100%;
  background-color: var(--color-fill-2) !important;

  :deep(.arco-virtual-list) {
    @include scrollBar;
  }
}

.spin-content {
  margin-top: 20px;
  display: flex;

  .template-select-card {
    width: 260px;
    height: 65vh;
    background-color: var(--color-bg-1);
    :deep(.arco-card-body) {
      height: calc(100% - 46px);
      width: 100%;
      display: flex;
      flex-flow: column;
      .module-tree-box {
        flex: 1;
        .module-tree-text {
          display: inline-block;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .module-icon-box {
        position: absolute;
        right: 8px;
        .module-icon {
          font-size: 14px;
          color: var(--color-fill-4);
          &-edit {
            margin: 0 5px;
          }
          &:hover {
            color: var(--color-text-3);
          }
        }
      }
      .module-tree-operation {
        height: 48px;
        margin: 0 auto;
        line-height: 48px;
        .template-module-add-btn {
          width: 212px;
          height: 44px;
        }
      }
    }
  }
  .template-select-table {
    padding: 20px;
    height: 65vh;
    margin-left: 10px;
    width: calc(100% - 260px);
    background-color: var(--color-bg-1);
  }
  .template-select-table-box {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }
}
</style>
