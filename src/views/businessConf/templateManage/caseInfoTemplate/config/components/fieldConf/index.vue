<template>
  <div class="field-config-page">
    <a-row :gutter="20" class="field-config-row">
      <a-col :flex="1">
        <a-row style="margin-bottom: 16px">
          <a-space direction="horizontal" :size="18">
            <a-button type="primary" @click="hanldeModuleFieldSave"> 保存 </a-button>
            <!-- <a-button> 脱敏 </a-button> -->
          </a-space>
        </a-row>
        <a-table
          v-model:selected-keys="selectedIds"
          row-key="tmplModuleFieldId"
          :columns="columns_01"
          :data="mainTmplTableData"
          :bordered="true"
          :pageize="size"
          :row-selection="rowSelection"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @selection-change="handleSelectChange"
        ></a-table>
      </a-col>
      <a-col :flex="1">
        <a-row style="margin-bottom: 16px">
          <a-col :span="8">
            <span class="field-remove-btn">已选 {{ selectedIds.length }} 项</span>
          </a-col>
          <a-col :span="16" class="mdt-col-flexend" justify="end">
            <span class="field-remove-btn" @click="removeFiled">移除</span>
          </a-col>
        </a-row>
        <a-table
          row-key="tmplModuleFieldId"
          :columns="columns_02"
          :data="targetDeputyFieldData"
          :bordered="true"
          :pageize="size"
        >
          <template #operations="{ record }">
            <a-button type="text" @click="handleDelete(record.tmplModuleFieldId)">移除</a-button>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { getDeputyTmplMappingList, saveDeputyTmplField } from '@/api/arcoApi/businessConf/caseTemplateManage'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue/es/table/interface'
import { Message } from '@arco-design/web-vue'

import { computed, ref, reactive } from 'vue'
import { watch } from 'vue'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'

interface FIELDCONF_MODULE_DATA_TYPE {
  moduleAll: REQUEST_GET_MAPPINGS_LIST_TYPE[]
  module1: REQUEST_GET_MAPPINGS_LIST_TYPE[]
  module2: REQUEST_GET_MAPPINGS_LIST_TYPE[]
  module3: REQUEST_GET_MAPPINGS_LIST_TYPE[]
}

interface FIELDCONF_MODULE_FIELD_DATA_TYPE {
  mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[]
  tmplModuleId: number | null
  selectedIds: (string | number)[]
}

interface FIELDCONF_TABLE_DATA_TYPE {
  moduleAll: FIELDCONF_MODULE_FIELD_DATA_TYPE[]
  module1: FIELDCONF_MODULE_FIELD_DATA_TYPE[]
  module2: FIELDCONF_MODULE_FIELD_DATA_TYPE[]
  module3: FIELDCONF_MODULE_FIELD_DATA_TYPE[]
}

interface Props {
  mainTmplTableData?: REQUEST_GET_FIELD_LIST_TYPE[]
  targetModule?: keyof FIELDCONF_MODULE_DATA_TYPE
  tmplModuleId?: number | null
  tmplId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  mainTmplTableData: () => [],
  targetModule: 'moduleAll',
  tmplModuleId: null,
  tmplId: null
})

const deputyFieldData = ref<FIELDCONF_TABLE_DATA_TYPE>({
  moduleAll: [],
  module1: [],
  module2: [],
  module3: []
})

// const searchName = ref('')
const emits = defineEmits(['delete', 'ok'])
const selectedIds = ref<(string | number)[]>([])
const deputyInfo = ref<REQUEST_DEPUTY_MODULE_FIELD_LIST_TYPE>()

const size = ref<SizeProps>('medium')
const rowSelection: TableRowSelection = reactive({
  showCheckedAll: true,
  onlyCurrent: false,
  type: 'checkbox'
})

const columns_01 = computed<TableColumnData[]>(() => [
  { title: '名称', dataIndex: 'fieldTitle' },
  { title: '编号', dataIndex: 'tmplModuleFieldId' }
])

const columns_02 = computed<TableColumnData[]>(() => [
  { title: '名称', dataIndex: 'fieldName' },
  { title: '编号', dataIndex: 'tmplModuleFieldId' },
  { title: '操作', align: 'center', dataIndex: 'operations', slotName: 'operations' }
])

const handleDelete = (id: number) => {
  let arr = deputyFieldData.value[props.targetModule].find((item) => item.tmplModuleId == props.tmplModuleId)
  if (arr) {
    let rdx = arr.mappings.findIndex((row) => row.tmplModuleFieldId == id)
    let idx = arr.selectedIds.findIndex((sid) => sid == id)
    arr.selectedIds.splice(idx, 1)
    arr.mappings.splice(rdx, 1)
    selectedIds.value = arr.selectedIds
  }
}

const hanldeModuleFieldSave = () => {
  if (deputyInfo.value) {
    deputyInfo.value.systemModules = deputyFieldData.value.module1
    deputyInfo.value.businessModules = deputyFieldData.value.module2
    deputyInfo.value.customModules = deputyFieldData.value.module3
    saveDeputyTmplField(deputyInfo.value).then((res) => {
      Message.success('操作成功')
      if (res) init()
    })
  }
}

const handleSelect = (rowKeys: (string | number)[], rowKey: string | number) => {
  console.log(rowKeys, rowKey)
}

const handleSelectAll = (checked: boolean) => {
  console.log(checked)
}

const handleSelectChange = (rowKeys: (string | number)[]) => {
  let moduleFields = deputyFieldData.value[props.targetModule].find((item) => item.tmplModuleId == props.tmplModuleId)
  if (moduleFields) {
    let addIds = rowKeys.filter((id) => moduleFields && !moduleFields.selectedIds.includes(Number(id)))
    let deleteIds = moduleFields.selectedIds.filter((id) => !rowKeys.includes(id))
    if (addIds.length) {
      let selectFields = props.mainTmplTableData.filter((field) => addIds.includes(field.tmplModuleFieldId))
      selectFields.forEach((rowField) => {
        if (props.tmplId && moduleFields) {
          moduleFields.mappings.push({
            tmplModuleFieldId: rowField.tmplModuleFieldId,
            fieldName: rowField.fieldTitle,
            deputyTmplId: props.tmplId,
            fieldSn: rowField.fieldSn,
            mappingId: null
          })
          moduleFields.selectedIds.push(rowField.tmplModuleFieldId)
        }
      })
    }
    if (deleteIds && deleteIds.length) {
      deleteIds.forEach((id) => {
        handleDelete(Number(id))
      })
    }
  }
}

const removeFiled = () => {
  let arr = deputyFieldData.value[props.targetModule].find((item) => item.tmplModuleId == props.tmplModuleId)
  if (arr) {
    selectedIds.value = []
    arr.selectedIds = []
    arr.mappings = []
  }
}

const setSaveTableFieldData = () => {
  let arr = deputyFieldData.value[props.targetModule].find((item) => item.tmplModuleId == props.tmplModuleId)
  selectedIds.value = arr?.selectedIds || []
}

const getDeputyTmplInfo = (id: number) => {
  getDeputyTmplMappingList(id).then((res) => {
    deputyFieldData.value.moduleAll = res.allModules.map((item) => ({
      mappings: item.mappings,
      tmplModuleId: item.tmplModuleId,
      selectedIds: item.mappings.map((mappingItem) => Number(mappingItem.tmplModuleFieldId))
    }))

    deputyFieldData.value.module1 = res.systemModules.map((item) => ({
      mappings: item.mappings,
      tmplModuleId: item.tmplModuleId,
      selectedIds: item.mappings.map((mappingItem) => Number(mappingItem.tmplModuleFieldId))
    }))
    deputyFieldData.value.module2 = res.businessModules.map((item) => ({
      mappings: item.mappings,
      tmplModuleId: item.tmplModuleId,
      selectedIds: item.mappings.map((mappingItem) => Number(mappingItem.tmplModuleFieldId))
    }))
    deputyFieldData.value.module3 = res.customModules.map((item) => ({
      mappings: item.mappings,
      tmplModuleId: item.tmplModuleId,
      selectedIds: item.mappings.map((mappingItem) => Number(mappingItem.tmplModuleFieldId))
    }))
    deputyInfo.value = res
    emits('ok', res)
  })
}

const init = () => {
  props.tmplId && getDeputyTmplInfo(props.tmplId)
}

init()

const targetDeputyFieldData = computed(() => {
  let arr = deputyFieldData.value[props.targetModule].find((item) => item.tmplModuleId == props.tmplModuleId)
  return arr?.mappings || []
})

watch(
  () => props.tmplModuleId,
  () => {
    setSaveTableFieldData()
  }
)
</script>

<style scoped lang="scss">
.field-config-page {
  padding: 20px;
}
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.field-remove-btn {
  display: inline-block;
  cursor: pointer;
  margin: 8px 0;
}
</style>
