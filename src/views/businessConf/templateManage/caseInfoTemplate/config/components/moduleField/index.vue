<template>
  <div class="template-info-page">
    <a-row v-if="showAdd" style="margin-bottom: 16px">
      <a-col :span="24">
        <a-space>
          <a-button type="primary" @click="moduleFieldAdd">
            <template #icon><icon-plus /> </template> 新建
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-table
      row-key="tmplModuleFieldId"
      :loading="loading"
      :columns="cloneColumns"
      :data="fieldData"
      :bordered="false"
      :pageize="size"
    >
      <template #isValidated="{ record }"> {{ record.isValidated ? '是' : '否' }} </template>
      <template #isRequire="{ record }"> {{ record.isRequire ? '是' : '否' }} </template>
      <template #isApprovalValidated="{ record }"> {{ record.isApprovalValidated ? '是' : '否' }} </template>
      <template #fieldDataType="{ record }">
        {{ fielldTypeObj[record.fieldDataType] || record.fieldDataType }}
      </template>
      <template #operations="{ record }">
        <a-button type="text" @click="handleEdit(record)">编辑</a-button>
        <a-button type="text" @click="handleDelete(record.tmplModuleFieldId)">删除</a-button>
      </template>
    </a-table>
    <a-modal
      v-model:visible="modalVisible"
      :ok-loading="modalLoading"
      width="600px"
      title="模块字段信息"
      ok-text="保存"
      title-align="start"
      :on-before-ok="handleSaveModuleField"
      @cancel="handleCancelModuleField"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 16 }"
      >
        <a-form-item field="fieldTitle" label="字段名称" :max-length="50">
          <a-input v-model="formData.fieldTitle" placeholder="请输入" />
        </a-form-item>

        <a-form-item field="fieldDataType" label="字段类型">
          <a-select
            v-model="formData.fieldDataType"
            :options="fieldDataTypeOptions"
            placeholder="请选择"
            @change="handleFieldDataTypeChange"
          />
        </a-form-item>
        <a-form-item field="isRequire" label="是否必填">
          <a-select v-model="formData.isRequire" :options="isRequireOptions" placeholder="请选择" />
        </a-form-item>
        <a-form-item field="isApprovalValidated" label="结案审核">
          <a-select v-model="formData.isApprovalValidated" :options="dict.isApprovalValidatedOptions" placeholder="请选择" />
        </a-form-item>
        <a-form-item field="isValidated" label="是否效验格式">
          <a-select v-model="formData.isValidated" :options="isValidatedOptions" placeholder="请选择" />
        </a-form-item>
        <a-form-item field="componentRelation" label="编辑方式">
          <a-select v-model="formData.componentRelation" :options="filterPluginOptions" placeholder="请选择" />
        </a-form-item>
        <a-form-item field="readOnlyRoles" label="只读权限">
          <a-select
            v-model="formData.readOnlyRoles"
            :options="roleOptions"
            placeholder="请选择角色"
            allow-clear
            multiple
            :show-search="true"
          />
        </a-form-item>
        <a-form-item v-if="showOption" field="fieldValues" label="预定义取值">
          <a-space direction="vertical" :size="10">
            <div class="options-add-btn" @click="handleAddOption">
              <icon-plus :size="12" />
            </div>
            <a-table
              row-key="uuid"
              :columns="optionColumns"
              :data="optionData"
              :pagination="false"
              :scroll="{ maxHeight: '22vh' }"
            >
              <template #key="{ record }">
                <a-form-item field="key" :validate-status="record.key ? 'success' : 'error'" no-style>
                  <a-input v-model.trim="record.key" size="mini" allow-clear placeholder="请输入" />
                </a-form-item>
              </template>
              <template #value="{ record }">
                <a-form-item field="key" :validate-status="record.value ? 'success' : 'error'" no-style>
                  <a-input v-model.trim="record.value" size="mini" allow-clear placeholder="请输入" />
                </a-form-item>
              </template>
              <template #operations="{ rowIndex }">
                <a-button type="text" size="mini" @click="handleDeleteOption(rowIndex)">删除</a-button>
              </template>
            </a-table>
          </a-space>
        </a-form-item>
        <a-form-item field="fieldDesc" label="备注">
          <a-input v-model="formData.fieldDesc" placeholder="请输入" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { deleteModuleField, saveModuleField } from '@/api/arcoApi/businessConf/caseTemplateManage'
import { getAll } from '@/api/arcoApi/systemManage/roleManage'//

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { SelectInstance } from '@arco-design/web-vue/es/select'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Modal, Message } from '@arco-design/web-vue'

import useLoading from '@/layouts/appArco/hooks/loading'
import { computed, nextTick, ref, watch ,onMounted} from 'vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import { v1 } from 'uuid'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

interface Props {
  fieldData?: REQUEST_GET_FIELD_LIST_TYPE[]
  tmplModuleId: number | null
  showOperation?: boolean
  tmplId: number | null
  showAdd?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showOperation: false,
  fieldData: () => [],
  tmplModuleId: null,
  showAdd: false,
  tmplId: null
})

const emits = defineEmits<{
  (e: 'fieldSave', data: REQUEST_MODULE_FIELD_RESINFO_TYPE): void
  (e: 'delete', data: REQUEST_MODULE_FIELD_RESINFO_TYPE): void
}>()

const { loading } = useLoading(false)

const generateFormModel = () => {
  return {
    tmplModuleId: props.tmplModuleId,
    componentRelation: 'AInput',
    readOnlyRoles: [],
    tmplModuleFieldId: null,
    fieldDataType: 'varchar',
    tmplId: props.tmplId,
    isValidated: 0,
    fieldTitle: '',
    isRequire: 0,
    isApprovalValidated: 0,
    fieldDesc: ''
  }
}

const formRules = ref({
  componentRelation: [{ required: true, message: '请选择关联组件', trigger: 'blur' }],
  fieldDataType: [{ required: true, message: '请选择字段类型', trigger: 'blur' }],
  fieldTitle: [{ required: true, message: '字段名称不能为空', trigger: 'blur' }],
  isValidated: [{ required: true, message: '请选择是否校验', trigger: 'blur' }],
  isRequire: [{ required: true, message: '请选择是否必填', trigger: 'blur' }],
  isApprovalValidated: [{ required: true, message: '请选择是否必填', trigger: 'blur' }]
})
const formData = ref<REQUEST_POST_MODULE_FIELD_SAVE_TYPE>(generateFormModel())
const optionData = ref<{ uuid: number; key: string; value: string }[]>([])
const cloneColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const formRef = ref<FormInstance>()
const modalLoading = ref(false)
const modalVisible = ref(false)

const fieldDataTypeOptions = ref<SelectOptionData[]>(dict.fieldDataTypeOptions)
const isValidatedOptions = ref<SelectOptionData[]>(dict.isValidatedOptions)
const componentOptions = ref<SelectOptionData[]>(dict.componentOptions)
const isRequireOptions = ref<SelectOptionData[]>(dict.isRequireOptions)
const roleOptions = ref<SelectOptionData[]>([])

onMounted(async () => {
  const roleData = await getAll()
  roleOptions.value = roleData.flatMap(group => 
    group.roles.map(role => ({
      value: role.roleName,
      label: role.roleName
    }))
  )
})


const fielldTypeObj = computed(() => {
  return dictEnumValToObject(fieldDataTypeOptions.value)
})

const filterPluginOptions = computed(() => {
  let arr = componentOptions.value
  switch (formData.value.fieldDataType) {
    case 'number':
      return arr.filter((item) => item.value && ['AInputNumber'].includes(item.value as string))
    case 'varchar':
      return arr.filter((item) => item.value && ['AInput', 'ATextarea'].includes(item.value as string))
    case 'datetime':
      return arr.filter((item) => item.value && ['ADatePicker', 'ADateTimePicker'].includes(item.value as string))
    case 'decimal':
      return arr.filter((item) => item.value && ['AInputNumber'].includes(item.value as string))
    case 'dict':
      return arr.filter((item) => item.value && ['ASelect', 'ASelectMultiple'].includes(item.value as string))
    default:
      return arr
  }
})

const showOption = computed(() => {
  return ['ASelect', 'ASelectMultiple'].includes(formData.value.componentRelation)
})

const columns = computed<TableColumnData[]>(() => [
  { width: 120, tooltip: true, ellipsis: true, title: '字段名称', dataIndex: 'fieldTitle' },
  { width: 120, title: '字段类型', dataIndex: 'fieldDataType', slotName: 'fieldDataType' },
  { width: 120, tooltip: true, title: '是否必填', dataIndex: 'isRequire', slotName: 'isRequire' },
  { width: 120, tooltip: true, title: '结案审核', dataIndex: 'isApprovalValidated', slotName: 'isApprovalValidated' },
  { width: 120, tooltip: true, title: '是否效验格式', dataIndex: 'isValidated', slotName: 'isValidated' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建日期', dataIndex: 'createTime' },
  { width: 180, tooltip: true, ellipsis: true, title: '最后一次修改日期', dataIndex: 'updateTime' },
  { width: 120, tooltip: true, ellipsis: true, title: '备注', dataIndex: 'fieldDesc' },
  { width: 180, tooltip: true, ellipsis: true, title: '操作', dataIndex: 'operations', slotName: 'operations' }
])

const optionColumns = computed<TableColumnData[]>(() => [
  // { width: 220, title: '选项名', dataIndex: 'key', slotName: 'key' },
  { width: 220, title: '选项值', dataIndex: 'value', slotName: 'value' },
  { width: 80, align: 'center', title: '操作', dataIndex: 'operations', slotName: 'operations' }
])

const handleDelete = (id: string) => {
  Modal.info({
    title: '删除提示',
    hideCancel: false,
    content: '是否确认删除该模板字段?',
    onOk: () => {
      deleteModuleField(id).then((res) => {
        Message.success('操作成功')
        emits('delete', res)
      })
    },
    onCancel: () => {}
  })
}

const moduleFieldAdd = () => {
  modalVisible.value = true
  nextTick(() => {
    formData.value = generateFormModel()
    optionData.value = []
    formRef.value?.resetFields()
  })
}

const handleEdit = (row: REQUEST_GET_FIELD_LIST_TYPE) => {
  if (row.tmplModuleFieldId) {
    optionData.value = []
    modalVisible.value = true
    let { componentRelation } = row
    nextTick(() => {
      formData.value = {
        componentRelation: componentRelation || 'AInput',
        tmplModuleFieldId: row.tmplModuleFieldId,
        fieldDataType: row.fieldDataType,
        tmplModuleId: row.tmplModuleId,
        isValidated: row.isValidated,
        fieldValues: row.fieldValues,
        fieldTitle: row.fieldTitle,
        isRequire: row.isRequire,
        isApprovalValidated: row.isApprovalValidated,
        fieldDesc: row.fieldDesc,
        fieldUi: row.fieldUi,
        tmplId: row.tmplId,
				readOnlyRoles: row.readOnlyRoles
      }
      // json转换
      if (row.fieldValues) {
        let fieldValues = JSON.parse(row.fieldValues)
        if (fieldValues && Object.prototype.toString.call(fieldValues) === '[object Array]')
          optionData.value = fieldValues
      }
    })
  }
}

const hasDuplicate = (arr: string[]) => {
  return arr.some((item, index) => arr.indexOf(item) !== index)
}

const handleFieldDataTypeChange: SelectInstance['onChange'] = (value) => {
  switch (value) {
    case 'number':
      formData.value.componentRelation = 'AInputNumber'
      break
    case 'varchar':
      formData.value.componentRelation = 'AInput'
      break
    case 'datetime':
      formData.value.componentRelation = 'ADatePicker'
      break
    case 'decimal':
      formData.value.componentRelation = 'AInputNumber'
      break
    case 'dict':
      formData.value.componentRelation = 'ASelect'
      break
    default:
      break
  }
}

const handleAselectComponent = (val: string) => {
  // 当选择了单选或者多选后，提交给后台的数据为：
  // optionData数据不能为空
  if (optionData.value.length === 0) {
    Message.error('选项不能为空')
    return false
  }
  // optionData数据不能出现相同名称
  if (hasDuplicate(optionData.value.map((item) => item.value))) {
    Message.error('选项名不能重复')
    return false
  }
  // fieldValues取值 {type:“collection”,values: 上述多行文本框中的数据}
  // fieldUi, 取值{component:"arco-select",options:{allowcreat:true, multiple: ture或者 false }
  let fieldUi = new Object()
  if (val === 'ASelectMultiple') fieldUi['multiple'] = true
  formData.value.fieldValues = JSON.stringify(optionData.value)
  formData.value.fieldUi = JSON.stringify(fieldUi)
}

const handleEditMethodsChange = () => {
  formData.value.fieldValues = ''
  formData.value.fieldUi = ''
  switch (formData.value.componentRelation) {
    case 'ASelect':
      return handleAselectComponent(formData.value.componentRelation)
    case 'ASelectMultiple':
      return handleAselectComponent(formData.value.componentRelation)
    default:
      break
  }
}

const handleSaveModuleField = async () => {
  let state = handleEditMethodsChange()
  if (state !== false) {
    const res = await formRef.value?.validate()
    if (!res) {
      modalLoading.value = true
      try {
        let res = await saveModuleField(formData.value)
        modalLoading.value = false
        if (res.tmplId) {
          Message.success('操作成功')
          emits('fieldSave', res)
        }
        return true
      } catch (error) {
        modalLoading.value = false
      }
    }
  }

  return false
}

// 选项表
const handleAddOption = () => {
  optionData.value.push({ uuid: v1(), key: '', value: '' })
}
const handleDeleteOption = (index: number) => {
  optionData.value.splice(index, 1)
}

const handleCancelModuleField = () => {}

watch(
  () => props.showOperation,
  (val) => {
    if (val) {
      cloneColumns.value = _.cloneDeep(columns.value)
    } else {
      let _columns: TableColumnData[] = _.cloneDeep(columns.value)
      cloneColumns.value = _columns.filter((item) => item.dataIndex !== 'operations')
    }
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
.template-info-page {
  padding: 20px;
}
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
.options-add-btn {
  margin-top: 5px;
  background-color: rgb(var(--arcoblue-6));
  padding: 2px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
}
</style>
