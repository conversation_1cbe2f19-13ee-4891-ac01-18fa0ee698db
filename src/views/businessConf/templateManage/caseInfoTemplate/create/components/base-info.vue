<template>
  <a-spin :loading="loading">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      class="form"
    >
      <a-form-item field="tmplTitle" label="模板名称" :max-length="50">
        <a-input v-model="formData.tmplTitle" placeholder="请输入" />
      </a-form-item>
      <a-form-item field="tmplType" label="模板类型">
        <a-select
          v-model="formData.tmplType"
          :disabled="disabledSelect"
          :options="tmplTypeOptions"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item v-if="showTmplType" field="parentId" label="基础模板">
        <a-select v-model="formData.parentId" :disabled="disabledSelect" :options="tmplOptions" placeholder="请选择" />
      </a-form-item>
      <a-form-item v-if="!showTmplType" field="entrustsId" label="案源方">
        <a-select
          v-model="formData.entrustsId"
          :disabled="disabledSelect"
          :options="entrustsOptions"
          placeholder="请选择"
          @change="handelEntrusts"
        />
      </a-form-item>
      <a-form-item v-if="!showTmplType" field="natureTitle" label="案由">
        <a-select v-model="formData.natureTitle" :options="natureTitleOptions" placeholder="请选择" />
      </a-form-item>
      <a-form-item field="tmplStatus" label="模板状态">
        <a-switch
          v-model="formData.tmplStatus"
          :default-checked="formData.tmplStatus === 1 ? true : false"
          :checked-value="1"
          :unchecked-value="0"
        ></a-switch>
      </a-form-item>
      <a-form-item field="tmplDesc" label="备注">
        <a-input v-model="formData.tmplDesc" :max-length="250" placeholder="请输入" />
      </a-form-item>
      <a-form-item class="base-form-justify">
        <a-space :size="20">
          <a-button type="primary" @click="onComfirmClick">{{ comfirmText }}</a-button>
          <a-button @click="onCancelClick">取消</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { saveDeputyTemplate, saveMainTemplate } from '@/api/arcoApi/businessConf/caseTemplateManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import { computed, ref } from 'vue'

interface Props {
  natureTitleOptions: SelectOptionData[]
  entrustsOptions: SelectOptionData[]
  tmplOptions: SelectOptionData[]
  disabledSelect?: boolean
  comfirmText?: string
}

interface BASE_TEMPLATE_FORM_TYPE {
  entrustsId: string | number
  parentId: string | number
  id: string | number
  entrustsName: string
  natureTitle: string
  updaterName: string
  tmplStatus: number
  tmplTitle: string
  tmplType: string
  tmplDesc: string
}

const props = withDefaults(defineProps<Props>(), {
  natureTitleOptions: () => [],
  entrustsOptions: () => [],
  disabledSelect: false,
  tmplOptions: () => [],
  comfirmText: '创建'
})

const emits = defineEmits(['comfirm', 'cancel'])

const { loading, setLoading } = useLoading(false)

const formData = ref<BASE_TEMPLATE_FORM_TYPE>({
  entrustsName: '',
  natureTitle: '',
  updaterName: '',
  entrustsId: '',
  tmplStatus: 1,
  tmplTitle: '',
  tmplType: '',
  tmplDesc: '',
  parentId: '',
  id: ''
})
const formRef = ref<FormInstance>()
const formRules = ref({
  tmplType: [{ required: true, message: '请选择模板类型' }],
  parentId: [{ required: true, message: '请选择基础模板' }],
  entrustsId: [{ required: true, message: '请选择案源方' }],
  natureTitle: [{ required: true, message: '请选择案由' }],
  tmplTitle: [{ required: true, message: '请输入' }]
})

const tmplTypeOptions = ref<SelectOptionData[]>(dict.tmplTypeOptions)

const showTmplType = computed(() => {
  return formData.value.tmplType && formData.value.tmplType !== '1'
})

const resetModel = () => {
  formRef.value?.resetFields()
}

const setModel = (model: BASE_TEMPLATE_FORM_TYPE) => {
  if (model) {
    formData.value = model
  }
}

const handelEntrusts = (val: any) => {
  if (val) {
    let entrustsItem = props.entrustsOptions.find((item) => item.value === val)
    if (entrustsItem) {
      formData.value.entrustsName = entrustsItem.label || ''
    }
  } else {
    formData.value.entrustsName = ''
  }
}

const submitForm = async (model: BASE_TEMPLATE_FORM_TYPE) => {
  setLoading(true)
  try {
    if (model) {
      let id: number | null = null
      if (model.tmplType === '1') {
        id = await saveMainTemplate({
          entrustsName: model.entrustsName,
          natureTitle: model.natureTitle,
          entrustsId: model.entrustsId,
          tmplStatus: model.tmplStatus,
          tmplTitle: model.tmplTitle,
          tmplType: model.tmplType,
          tmplDesc: model.tmplDesc,
          tmplId: model.id
        })
      } else {
        id = await saveDeputyTemplate({
          tmplStatus: model.tmplStatus,
          tmplTitle: model.tmplTitle,
          tmplType: model.tmplType,
          tmplDesc: model.tmplDesc,
          tmplId: model.parentId,
          deputyTmplId: model.id
        })
      }
      emits('comfirm', 'submit', { id, tmplType: model.tmplType })
    }
  } catch (err) {
    Message.error('创建模板失败！')
  } finally {
    setLoading(false)
  }
}

const onComfirmClick = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    submitForm({ ...formData.value })
  }
}

const onCancelClick = async () => {
  const res = await formRef.value?.resetFields()
  if (!res) {
    emits('cancel')
  }
}

defineExpose({ resetModel, setModel })
</script>

<style scoped lang="scss">
.form {
  width: 500px;
}

.form-content {
  padding: 8px 50px 0 30px;
}

.base-form-justify {
  :deep(.arco-form-item-content) {
    justify-content: center;
  }
}
</style>
