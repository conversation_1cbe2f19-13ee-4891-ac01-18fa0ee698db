<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    title-align="start"
    modal-class="mdt-modal"
    draggable
    @ok="handleSaveModal"
    @cancel="handleCancelModal"
    @close="handleCancelModal"
  >
    <div class="form-info">
      <a-form ref="baseFormRef" :model="baseInfoForm" label-align="left">
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">基本信息</span>
          </div>

          <a-form-item field="templateName" :rules="[{ required: true, message: '请输入模板名称' }]" label="模板名称">
            <a-input v-model="baseInfoForm.templateName" :max-length="100" placeholder="请输入内容" allow-clear />
          </a-form-item>
          <a-form-item field="txTemplateId" label="模板代码"> {{ baseInfoForm.txTemplateId }}</a-form-item>
          <a-form-item field="smsSign" label="短信签名"> {{ baseInfoForm.smsSign }}</a-form-item>
          <a-form-item field="scene" label="所属场景"> {{ baseInfoForm.scene }}</a-form-item>
          <a-form-item field="channel" label="所属渠道"> {{ baseInfoForm.channel }}</a-form-item>

          <a-form-item field="parameterContent" label="发送内容">
            <a-textarea
              v-model="baseInfoForm.templateContent"
              :auto-size="{ minRows: 4 }"
              :max-length="250"
              placeholder="请输入内容"
              allow-clear
            />
          </a-form-item>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">权限配置</span>
          </div>
          <a-form-item field="departmentIds" label="可见范围">
            <a-table
              row-key="id"
              :pagination="false"
              :columns="departmentColumns"
              :scroll="{ maxHeight: '30vh' }"
              :data="departmentData"
              :bordered="false"
            >
              <template #departmentNames="{ record }">
                <a-select
                  v-if="record.edit"
                  v-model="record.departmentIds"
                  :options="record.options"
                  :max-tag-count="2"
                  multiple
                  placeholder="请选择"
                  allow-clear
                  @change="handleDepartmentSelect(record)"
                />
                <span v-else>{{ record.departmentNames.join(',') }}</span>
              </template>
              <template #operations="{ record }">
                <a-button v-if="record.edit" type="text" @click="record.edit = false">确定</a-button>
                <a-button v-else type="text" @click="record.edit = true">配置</a-button>
              </template>
            </a-table>
          </a-form-item>
          <a-form-item field="roleNames" label="使用角色">
            <a-table
              row-key="id"
              :pagination="false"
              :columns="roleColumns"
              :scroll="{ maxHeight: '30vh' }"
              :data="roleData"
              :bordered="false"
            >
              <template #roleNames="{ record }">
                <a-select
                  v-if="record.edit"
                  v-model="record.roleNames"
                  :options="record.options"
                  :max-tag-count="2"
                  placeholder="请选择"
                  multiple
                  allow-clear
                />
                <span v-else>{{ record.roleNames.join(',') }}</span>
              </template>
              <template #operations="{ record }">
                <a-button v-if="record.edit" type="text" @click="record.edit = false">确定</a-button>
                <a-button v-else type="text" @click="record.edit = true">配置</a-button>
              </template>
            </a-table>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'

import { saveSmsTemplate } from '@/api/arcoApi/businessConf/smsTemplateManage'
import { getAll } from '@/api/arcoApi/systemManage/roleManage'
import { Message } from '@arco-design/web-vue'
import { computed, ref } from 'vue'
import _ from 'lodash'

interface Props {
  width?: string | number
  title?: string
}

interface RenderAuthDepartmentataType {
  options: SelectOptionData[]
  departmentNames: string[]
  departmentIds: string[]
  edit: boolean
  type: string
  id: string
}

interface RenderAuthRoleDataType {
  options: SelectOptionData[]
  roleNames: string[]
  edit: boolean
  type: string
  id: string
}

withDefaults(defineProps<Props>(), {
  title: '短信模板配置',
  width: '700px'
})

const emits = defineEmits<{
  (e: 'cancel'): void
  (e: 'ok'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const generateFormModel = () => {
  return {
    templateContent: '',
    templateParams: '',
    templateStatus: 0,
    templateName: '',
    smsTemplateId: 0,
    txTemplateId: '',
    description: '',
    smsSign: '',
    applyto: '',
    channel: '',
    scene: ''
  }
}

const baseInfoForm = ref<RequestPostSmsTempSaveParamType>(generateFormModel())
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const departmentData = ref<RenderAuthDepartmentataType[]>([
  { id: '2', type: '调解组织', departmentIds: [], departmentNames: [], options: [], edit: false },
  { id: '3', type: '案源方', departmentIds: [], departmentNames: [], options: [], edit: false },
  { id: '1', type: '平台', departmentIds: [], departmentNames: [], options: [], edit: false }
])

const roleData = ref<RenderAuthRoleDataType[]>([
  { id: '2', type: '调解组织角色', roleNames: [], options: [], edit: false },
  { id: '3', type: '案源方角色', roleNames: [], options: [], edit: false },
  { id: '1', type: '平台方角色', roleNames: [], options: [], edit: false }
])

const departmentColumns = computed<TableColumnData[]>(() => [
  { width: 100, title: '类型', dataIndex: 'type' },
  { width: 200, align: 'center', tooltip: true, ellipsis: true, title: '可见部门', slotName: 'departmentNames' },
  { width: 80, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const roleColumns = computed<TableColumnData[]>(() => [
  { width: 100, title: '类型', dataIndex: 'type' },
  { width: 200, align: 'center', tooltip: true, ellipsis: true, title: '可见角色', slotName: 'roleNames' },
  { width: 80, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    baseInfoForm.value.deptIdList = []
    baseInfoForm.value.roleNameList = []
    departmentData.value.forEach(a => {
      baseInfoForm.value.deptIdList = baseInfoForm.value.deptIdList?.concat(a.departmentIds)
    })
    roleData.value.forEach(b => {
      baseInfoForm.value.roleNameList = baseInfoForm.value.roleNameList?.concat(b.roleNames)
    })
    
    saveSmsTemplate(baseInfoForm.value)
      .then(() => {
        Message.success('操作成功')
        visible.value = false
        emits('ok')
      })
      .finally(() => {
        okLoading.value = false
      })
  }
  return false
}

const handleDepartmentSelect = (row: RenderAuthDepartmentataType) => {
  let filterSelects = row.options.filter((item) => item.value && row.departmentIds.includes(item.value as string))
  row.departmentNames = filterSelects.map((item) => item.label || '')
}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setDepartmentOptions = async (deptRes: REQUEST_GET_DEPT_ALLDATA_TYPE[]) => {
  deptRes.forEach((deptItem) => {
    let rowRecord = departmentData.value.find((row) => row.id === deptItem.groupName)
    if (rowRecord) {
      rowRecord.options = deptItem.deptDTOList.map((dept) => ({
        label: dept.deptName,
        value: dept.deptId
      }))
    }
  })
}

const getRoleOptions = async () => {
  let roleRes = await getAll()
  roleRes.forEach((roleItem) => {
    let rowRecord = roleData.value.find((row) => row.id === roleItem.groupName)
    if (rowRecord) {
      rowRecord.options = roleItem.roles.map((role) => ({
        label: role.roleName,
        value: role.roleName
      }))
    }
  })
}

const setFormModel = (modal: RequestPostSmsTempResultInfoType) => {
  let cloneDeepModal = _.cloneDeep(modal)
  for (const key in cloneDeepModal) {
    if (Object.prototype.hasOwnProperty.call(baseInfoForm.value, key)) {
      baseInfoForm.value[key] = cloneDeepModal[key]
    } else if (key === 'deptDTOList') {
      cloneDeepModal[key].forEach((cloneDept) => {
        let targetDept = departmentData.value.find((deptItem) => deptItem.id === cloneDept.companyType)
        if (targetDept) {
          targetDept.departmentNames.push(cloneDept.deptName)
          targetDept.departmentIds.push(cloneDept.deptId)
        }
      })
    } else if (key === 'roleNames') {
      cloneDeepModal[key].forEach((cloneRole) => {
        let targetRole = roleData.value.find((roleRow) =>
          roleRow.options.find((roleItem) => roleItem.label === cloneRole)
        )
        if (targetRole) {
          targetRole.roleNames.push(cloneRole)
        }
      })
    }
  }
}

const initFormModel = () => {
  departmentData.value.forEach((deptItem) => {
    deptItem.departmentNames = []
    deptItem.departmentIds = []
    deptItem.edit = false
  })
  roleData.value.forEach((roleItem) => {
    roleItem.roleNames = []
    roleItem.edit = false
  })
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

getRoleOptions()

defineExpose({ setFormModel, initFormModel, setDepartmentOptions })
</script>

<style lang="scss" scoped></style>
