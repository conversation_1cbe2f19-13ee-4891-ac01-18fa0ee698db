<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :wrapper-col-props="{ span: 16 }"
            :label-col-props="{ span: 8 }"
            :model="searchForm"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="templateName" label="模板名称">
                  <a-input v-model="searchForm.templateName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="templateTypes" label="模板类型">
                  <a-select
                    v-model="searchForm.templateTypes"
                    :options="templateTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="deptIdList" label="可见范围">
                  <a-cascader
                    v-model="searchForm.deptIdList"
                    :options="cascaderOptions"
                    :max-tag-count="1"
                    multiple
                    placeholder="请输入内容"
                  ></a-cascader>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="txTemplateId" label="模板代码">
                  <a-input v-model="searchForm.txTemplateId" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="updaterName" label="修改人">
                  <a-input v-model="searchForm.updaterName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="templateStatus" label="模板状态">
                  <a-select
                    v-model="searchForm.templateStatus"
                    :options="templateStatusOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="smsTemplateId"
        :pageize="size"
        :bordered="false"
        :loading="loading"
        :data="renderData"
        :columns="cloneColumns"
        :pagination="pagination"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #templateStatus="{ record }">
          <a-switch
            v-model="record.templateStatus"
            :checked-value="1"
            :unchecked-value="0"
            @click="handleSwitchStatus(record)"
          ></a-switch>
        </template>
        <template #deptIdList="{ record }">
          <span>
            {{ record.deptDTOList.map((dept) => `${dept.companyName}-${dept.deptName}`).join(',') }}
          </span>
        </template>
        <template #templateType="{ record }">
          <span> {{ templateTypeObj[record.templateType] || record.templateType }} </span>
        </template>
        <template #operations="{ record }">
          <a-button v-auth="[]" type="text" @click="handleConfig(record)">配置</a-button>
        </template>
      </a-table>
    </a-card>
    <SmsConfigModal ref="modalgRef" v-model:visible="modalVisible" :tmpl-id="tmplId" @ok="getTableData" />
  </div>
</template>

<script lang="ts" setup>
import { saveTempStatus, smsTempPage, byIdGetSmstemplate } from '@/api/arcoApi/businessConf/smsTemplateManage'

import type { CascaderOption } from '@arco-design/web-vue/es/cascader/interface'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import SmsConfigModal from './components/sms-config-modal.vue'
import { computed, ref, reactive, watch, nextTick } from 'vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { getAllDept } from '@/api/eleApi/common'
import { Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import { useRouter } from 'vue-router'
import dict from '@/dict/systemManage'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const router = useRouter()

const generateFormModel = () => {
  return {
    templateStatus: null,
    templateTypes: [],
    templateName: '',
    txTemplateId: '',
    deptIdList: [],
    updaterName: ''
  }
}

const { loading, setLoading } = useLoading(true)
// const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('短信模板管理')
// const pageEnName = ref('Caseallocation')

const searchForm = ref<RequestPostSmsTempListsearchParamType>(generateFormModel())
const renderData = ref<RequestGetSmstempListDataType[]>([])
const cascaderOptions = ref<CascaderOption>([
  { label: '调解组织', value: '2', children: [] },
  { label: '案源方', value: '3', children: [] },
  { label: '平台', value: '1', children: [] }
])

const modalgRef = ref<InstanceType<typeof SmsConfigModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const appearanceIds = ref<(string | number)[]>([])
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const modalVisible = ref(false)
const tmplId = ref('')

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const templateStatusOptions = ref<SelectOptionData[]>(dict.templateStatusOptions)
const templateTypeOptions = ref<SelectOptionData[]>(dict.templateTypeOptions)

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 180, tooltip: true, ellipsis: true, fixed: 'left', title: '模板代码', dataIndex: 'txTemplateId' },
  { width: 160, tooltip: true, ellipsis: true, title: '模板名称', dataIndex: 'templateName' },
  { width: 140, tooltip: true, ellipsis: true, title: '短信类型', dataIndex: 'templateType', slotName: 'templateType' },
  { width: 140, tooltip: true, ellipsis: true, title: '短信签名', dataIndex: 'smsSign' },
  { width: 140, tooltip: true, ellipsis: true, title: '所属场景', dataIndex: 'scene', align: 'center' },
  { width: 140, tooltip: true, ellipsis: true, title: '所属渠道', dataIndex: 'channel', align: 'center' },
  { width: 120, tooltip: true, ellipsis: true, title: '可见范围', slotName: 'deptIdList' },
  { width: 180, tooltip: true, ellipsis: true, title: '短信内容', dataIndex: 'templateContent' },
  { width: 120, title: '模板状态', dataIndex: 'templateStatus', slotName: 'templateStatus' },
  { width: 180, tooltip: true, ellipsis: true, title: '更新时间', dataIndex: 'updateTime' },
  { width: 120, tooltip: true, ellipsis: true, title: '更新人', dataIndex: 'updaterName' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const templateTypeObj = computed(() => {
  return dictEnumValToObject(dict.templateTypeOptions)
})

// const handleAdd = () => {}

const handleConfig = async (row: RequestGetSmstempListDataType) => {
  if (row.smsTemplateId) {
    let tmplRes = await byIdGetSmstemplate(row.smsTemplateId)
    modalVisible.value = true
    nextTick(() => {
      modalgRef.value?.setFormModel(tmplRes)
    })
  }
}

const handleSwitchStatus = (row: RequestGetSmstempListDataType) => {
  saveTempStatus({ smsTemplateId: row.smsTemplateId, templateStatus: row.templateStatus }).then(() => {
    Message.success('操作成功')
    getTableData()
  })
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  appearanceIds.value = []
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  appearanceIds.value = []
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await smsTempPage({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getDepartmentOptions = async () => {
  let deptRes = await getAllDept()
  if (deptRes) {
    modalgRef.value?.setDepartmentOptions(deptRes)
    deptRes.forEach((deptItem) => {
      let cascaderRecord = cascaderOptions.value.find((row) => row.value === deptItem.groupName)
      if (cascaderRecord) {
        cascaderRecord.children = deptItem.deptDTOList.map((dept) => ({
          label: dept.deptName,
          value: dept.deptId
        }))
      }
    })
  }
}

getDepartmentOptions()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
