<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
    @ok="okClick"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="baseInfoRules"
        :label-col-props="{ span: 9 }"
        :wrapper-col-props="{ span: 15 }"
        label-align="left"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">基础信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="orgName" label="组织名称">
                <a-input v-model="baseInfoForm.orgName" :max-length="50" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="orgType" label="组织类型">
                <a-tree-select
                  v-model="baseInfoForm.orgType"
                  :data="orgTypeOptions"
                  placeholder="请选择"
                  allow-search
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="buildOrgType" label="设立机构类型">
                <a-select
                  v-model="baseInfoForm.buildOrgType"
                  :options="buildOrgTypeOptions"
                  placeholder="请选择"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="buildTime" label="成立日期">
                <a-date-picker v-model="baseInfoForm.buildTime" style="width: 100%">
                  <template #suffix-icon><icon-schedule /></template>
                </a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="mdtScope" label="调解范围">
                <a-input v-model="baseInfoForm.mdtScope" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="leadBody" label="指导机构">
                <a-input v-model="baseInfoForm.leadBody" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">联系信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="principal" label="联系人">
                <a-input v-model="baseInfoForm.principal" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="contactWay" label="联系电话">
                <a-input v-model="baseInfoForm.contactWay" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="address" label="联系地址">
                <a-input v-model="baseInfoForm.address" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="socialCreditCode" label="社会信用代码">
                <a-input v-model="baseInfoForm.socialCreditCode" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { editOrgInfo, addOrgInfo } from '@/api/arcoApi/businessConf/mediateOrgInfoManage'
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TreeNodeData } from '@arco-design/web-vue/es/tree/interface'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import { ref } from 'vue'
import _ from 'lodash'

interface Props {
  orgTypeOptions?: TreeNodeData[]
  width?: string | number
  title: string
}

withDefaults(defineProps<Props>(), {
  orgTypeOptions: () => [],
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    buildOrgType: '',
    leadBody: '',
    buildTime: '',
    orgName: '',
    mdtScope: '',
    orgType: '',
    orgId: null,
    entrustsList: [],
    socialCreditCode: '',
    address: '',
    contactWay: '',
    principal: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_ORG_SAVE_PARAM_TYPE>(generateFormModel())
const baseInfoRules = ref({
  orgName: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
  orgType: [{ required: true, message: '请选择组织类型', trigger: 'blur' }]
})
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const buildOrgTypeOptions = ref<SelectOptionData[]>(dict.buildOrgTypeOptions)

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    if (baseInfoForm.value.orgId) {
      editOrgInfo(baseInfoForm.value)
        .then(() => {
          Message.success('编辑成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    } else {
      addOrgInfo(baseInfoForm.value)
        .then(() => {
          Message.success('新增成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    }
  }
  return false
}

const okClick = () => {}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_POST_ORG_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
