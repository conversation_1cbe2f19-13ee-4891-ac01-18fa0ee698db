<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 8 }"
            :wrapper-col-props="{ span: 16 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="orgName" label="组织名称">
                  <a-input v-model="searchForm.orgName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="orgType" label="组织类型">
                  <a-tree-select
                    v-model="searchForm.orgType"
                    :data="orgTypeOptions"
                    :tree-check-strictly="true"
                    placeholder="请选择"
                    allow-clear
                    allow-search
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="principal" label="联系人">
                  <a-input v-model="searchForm.principal" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="creatorName" label="创建人">
                  <a-input v-model="searchForm.creatorName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="创建时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="relaEntrustsIdList" label="关联案源方">
                  <a-select
                    v-model="searchForm.relaEntrustsIdList"
                    :options="entrustsOptions"
                    placeholder="请选择"
                    allow-clear
                    multiple
                    :max-tag-count="1"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="24">
          <a-space>
            <a-button v-auth="['mdtOrgSave']" type="primary" @click="handleInfoAdd">
              <template #icon><icon-plus /> </template> 新建
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #mediatorCount="{ record }"> {{ record.mediatorCount || 0 }} </template>
        <template #handleCaseCount="{ record }"> {{ record.handleCaseCount || 0 }} </template>
        <template #operations="{ record }">
          <a-button v-auth="['mdtOrgSave']" type="text" @click="handleEdit(record)">编辑</a-button>
          <a-button v-auth="['mdtOrgDelete']" type="text" @click="handleDelete(record)">删除</a-button>
        </template>
      </a-table>
      <FormInfoModal
        ref="formInfoRef"
        v-model:visible="modalVisible"
        :title="modalTitle"
        :org-type-options="orgTypeOptions"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getOrgPageList, deleteOrg, getOrgDetails } from '@/api/arcoApi/businessConf/mediateOrgInfoManage'
import { getAllEntrusts } from '@/api/arcoApi/businessConf/caseSourceInfoManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'
import type { TreeNodeData } from '@arco-design/web-vue/es/tree/interface'
import { computed, nextTick, ref, reactive, watch } from 'vue'
import FormInfoModal from './components/form-info-modal.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { getTargetDict } from '@/api/eleApi/common'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const generateFormModel = () => {
  return {
    relaEntrustsIdList: [],
    creatorName: '',
    startTime: '',
    principal: '',
    endTime: '',
    orgName: '',
    orgType: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('调解组织')
// const pageEnName = ref('Org')

const searchForm = ref<REQUEST_POST_ORG_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_ORG_LIST_DATA_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof FormInfoModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')
const modalVisible = ref(false)

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 200, tooltip: true, ellipsis: true, fixed: 'left', title: '调解组织名称', dataIndex: 'orgName' },
  { width: 180, tooltip: true, ellipsis: true, title: '调解组织类型', dataIndex: 'orgType' },
  { width: 120, tooltip: true, ellipsis: true, title: '调解范围', dataIndex: 'mdtScope' },
  { width: 120, align: 'center', title: '调解人员数量', dataIndex: 'mediatorCount', slotName: 'mediatorCount' },
  { width: 120, align: 'center', title: '在案办件数量', dataIndex: 'handleCaseCount', slotName: 'handleCaseCount' },
  { width: 120, tooltip: true, ellipsis: true, title: '联系人', dataIndex: 'principal' },
  { width: 140, tooltip: true, ellipsis: true, title: '联系方式', dataIndex: 'contactWay' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建人', dataIndex: 'creatorName' },
  { width: 180, tooltip: true, align: 'center', ellipsis: true, title: '创建日期', dataIndex: 'createTime' },
  { width: 160, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const orgTypeOptions = ref<TreeNodeData[]>([])
const entrustsOptions = ref<SelectOptionData[]>([])
const orgTypeDictDataObj = ref<{ [x: string]: string }>({})

const modalTitle = computed(() => {
  return `${pageZhName.value}信息${modalType.value === 'add' ? '创建' : '编辑'}`
})

const handleInfoAdd = () => {
  modalType.value = 'add'
  modalVisible.value = true
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.startTime = timeArr[0] || ''
    searchForm.value.endTime = timeArr[1] || ''
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
}

const handleDelete = (row: REQUEST_GET_ORG_LIST_DATA_TYPE) => {
  if (row.mediatorCount && row.handleCaseCount) {
    deletePopup()
    return
  }
  Modal.warning({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `是否确认删除该“${pageZhName.value}”`,
    onOk: () => {
      deleteOrg(row.orgId)
        .then(() => {
          Message.success('操作成功')
          getTableData()
        })
        .catch(() => deletePopup())
    },
    onCancel: () => {}
  })
}

const deletePopup = () => {
  Modal.error({
    title: '删除失败',
    content: `该“${pageZhName.value}”下已有“人员”或“案件”，请删除后在进行“${pageZhName.value}”删除`
  })
}

const handleEdit = async (row: REQUEST_GET_ORG_LIST_DATA_TYPE) => {
  if (row.orgId) {
    let orgData = await getOrgDetails(row.orgId)
    if (orgData) {
      modalType.value = 'edit'
      modalVisible.value = true
      nextTick(() => {
        formInfoRef.value?.setFormModel(orgData)
      })
    }
  }
}

const handleComfirm = () => {
  modalVisible.value = false
  getTableData()
}
const handleCancel = () => {
  modalVisible.value = false
}

const getOrgTypeOptions = () => {
  getTargetDict({ type: 'org_type' }).then((res) => {
    res.sysDictDataList.forEach((item) => {
      let { remark, dictTag } = item
      orgTypeDictDataObj.value[dictTag] = remark
      let tarDict = orgTypeOptions.value.find((node) => node.key === remark)
      if (tarDict) {
        tarDict.children?.push({ key: `${remark}/${dictTag}`, title: dictTag })
      } else {
        orgTypeOptions.value.push({
          title: remark,
          key: remark,
          disabled: true,
          children: [{ key: `${remark}/${dictTag}`, title: dictTag }]
        })
      }
    })
  })
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getOrgPageList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getAllEntrustsData = () => {
  getAllEntrusts().then((res) => {
    if (res && res.length) {
      entrustsOptions.value = res.map((item) => ({ label: item.entrustsName, value: item.entrustsId }))
    }
  })
}

getAllEntrustsData()
getOrgTypeOptions()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
