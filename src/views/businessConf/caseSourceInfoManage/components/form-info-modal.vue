<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    :width="width"
    :title="title"
    :ok-loading="okLoading"
    :on-before-ok="handleSaveModal"
    :on-before-cancel="handleCancelModal"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="form-info">
      <a-form
        ref="baseFormRef"
        :model="baseInfoForm"
        :rules="baseInfoRules"
        :label-col-props="{ span: 10 }"
        :wrapper-col-props="{ span: 14 }"
        label-align="left"
      >
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">基础信息</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="entrustsName" label="案源方名称">
                <a-input v-model="baseInfoForm.entrustsName" :max-length="50" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="entrustsType" label="案源方类型">
                <a-select
                  v-model="baseInfoForm.entrustsType"
                  :options="entrustsTypeOptions"
                  placeholder="请选择"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="contactsName" label="联系人">
                <a-input v-model="baseInfoForm.contactsName" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="contactWay" label="联系电话">
                <a-input v-model="baseInfoForm.contactWay" placeholder="请输入内容" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                :label-col-props="{ span: 5 }"
                :wrapper-col-props="{ span: 19 }"
                field="entrustsAddress"
                label="联系地址"
              >
                <a-textarea
                  v-model="baseInfoForm.entrustsAddress"
                  :auto-size="{ minRows: 4 }"
                  style="margin-left: -5px"
                  placeholder="请输入内容"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="base-form-box mdt-form-content">
          <div class="mdt-form-header">
            <div class="mdt-form-split"></div>
            <span class="mdt-form-title">关联调解组织配置</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item
                field="entrustsDeptOrgBOList"
                :label-col-props="{ span: 5 }"
                :wrapper-col-props="{ span: 19 }"
              >
                <a-table
                  row-key="docTmplId"
                  :pagination="false"
                  :columns="columns"
                  :style="{ height: '28vh' }"
                  :scroll="{ maxHeight: '28vh' }"
                  :data="renderData"
                  :bordered="false"
                >
                  <template #org="{ record }">
                    <a-select
                      v-if="record.showSelect"
                      v-model="record.sysMdtOrgIdList"
                      :options="mdtOrgsOptions"
                      :max-tag-count="1"
                      placeholder="请选择"
                      allow-clear
                      multiple
                    />
                    <span v-else>
                      {{ record.sysMdtOrgDTOList.map((item) => item.orgName).join('、') }}
                    </span>
                  </template>
                  <template #operations="{ record }">
                    <a-button type="text" @click="handleOrgConfig(record)">
                      {{ record.showSelect ? '确定' : '配置' }}
                    </a-button>
                  </template>
                </a-table>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { editEntrustsInfo, addEntrustsInfo, getDeptInfoById } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { Message } from '@arco-design/web-vue'
import dict from '@/dict/businessConf'
import { computed, ref } from 'vue'
import _ from 'lodash'

interface Props {
  mdtOrgsOptions?: SelectOptionData[]
  width?: string | number
  title: string
}

interface RENDER_BYIDDEPT_DATA_TYPE extends REQUEST_GET_ENTRUSTS_BYIDDEPT_DATA_TYPE {
  sysMdtOrgIdList: (string | number)[]
  showSelect: boolean
}

withDefaults(defineProps<Props>(), {
  mdtOrgsOptions: () => [],
  width: '700px',
  title: ''
})

const emits = defineEmits<{
  (e: 'comfirm'): void
  (e: 'cancel'): void
}>()

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })

const generateFormModel = () => {
  return {
    closeCaseAuditFlag: false,
    contactWay: '',
    contactsName: '',
    entrustsAddress: '',
    entrustsId: null,
    entrustsName: '',
    entrustsType: '',
    entrustsDeptOrgBOList: [],
    timeLimit: 30,
    timeLimitStatus: true,
    entrustsCode: ''
  }
}

const baseInfoForm = ref<REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE>(generateFormModel())
const entrustsTypeOptions = ref<SelectOptionData[]>(dict.entrustsTypeOptions)
const renderData = ref<RENDER_BYIDDEPT_DATA_TYPE[]>([])
const baseFormRef = ref<FormInstance>()
const okLoading = ref<boolean>(false)

const baseInfoRules = ref({
  entrustsCode: [{ match: /^[A-Z]{1,50}$/, message: '请输入大写拼音字母，最多50字符' }],
  entrustsName: [{ required: true, message: '请输入案源方名称', trigger: 'blur' }],
  entrustsType: [{ required: true, message: '请选择案源方类型', trigger: 'blur' }]
})
const columns = computed<TableColumnData[]>(() => [
  { width: 100, tooltip: true, ellipsis: true, title: '部门名称', dataIndex: 'deptName' },
  { width: 200, tooltip: true, ellipsis: true, title: '关联调解组织', dataIndex: 'sysMdtOrgDTOList', slotName: 'org' },
  { width: 80, fixed: 'right', title: '操作', dataIndex: 'operations', align: 'center', slotName: 'operations' }
])

const getEntrustsDeptList = () => {
  if (!baseInfoForm.value.entrustsId) return
  getDeptInfoById(baseInfoForm.value.entrustsId).then((res) => {
    if (res && res.length > 0) {
      renderData.value = res.map((item) => {
        return {
          sysMdtOrgIdList: item.sysMdtOrgDTOList.map((item) => item.orgId) || [],
          showSelect: false,
          ...item
        }
      })
    }
  })
}

const handleOrgConfig = (record: RENDER_BYIDDEPT_DATA_TYPE) => {
  record.showSelect = !record.showSelect
}

const handleSaveModal = async () => {
  const res = await baseFormRef.value?.validate()
  if (!res) {
    okLoading.value = true
    baseInfoForm.value.entrustsDeptOrgBOList = renderData.value.map((item) => {
      return {
        sysMdtOrgIdList: item.sysMdtOrgIdList,
        deptName: item.deptName,
        deptId: item.deptId
      }
    })
    if (baseInfoForm.value.entrustsId) {
      editEntrustsInfo(baseInfoForm.value)
        .then(() => {
          Message.success('编辑成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    } else {
      addEntrustsInfo(baseInfoForm.value)
        .then(() => {
          Message.success('新增成功')
          emits('comfirm')
          initFormModel()
        })
        .finally(() => {
          okLoading.value = false
        })
    }
  }
  return false
}

const handleCancelModal = () => {
  initFormModel()
  return true
}

const setFormModel = (modal: REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE) => {
  baseInfoForm.value = _.cloneDeep(modal)
  renderData.value = []
  getEntrustsDeptList()
}

const initFormModel = () => {
  baseInfoForm.value = generateFormModel()
  baseFormRef.value?.resetFields()
}

defineExpose({ setFormModel, initFormModel })
</script>

<style lang="scss" scoped></style>
