<template>
  <div class="template-list-container common-page">
    <a-card class="template-list-card" :bordered="false" :title="lisPageTitle">
      <template #title>
        <span class="bold">{{ lisPageTitle }}</span>
      </template>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchForm"
            :label-col-props="{ span: 8 }"
            :wrapper-col-props="{ span: 16 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="entrustsName" label="案源方名称">
                  <a-input v-model="searchForm.entrustsName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="entrustsType" label="案源方类型">
                  <a-select
                    v-model="searchForm.entrustsType"
                    :options="entrustsTypeOptions"
                    placeholder="请选择"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="contactsName" label="联系人">
                  <a-input v-model="searchForm.contactsName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="creatorName" label="创建人">
                  <a-input v-model="searchForm.creatorName" placeholder="请输入内容" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="allTime" label="创建时间">
                  <a-range-picker v-model="searchForm.allTime" style="width: 100%" @change="handlePickerSelect">
                    <template #suffix-icon><icon-schedule /></template>
                  </a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="relaMdtOrgIdList" label="关联调解组织">
                  <a-select
                    v-model="searchForm.relaMdtOrgIdList"
                    :options="mdtOrgsOptions"
                    placeholder="请选择"
                    allow-clear
                    multiple
                    :max-tag-count="1"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon><icon-search /></template>查询
            </a-button>
            <a-button @click="reset">
              <template #icon><icon-refresh /></template>重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0" />
      <a-row v-auth="['entrustSave']" style="margin-bottom: 16px">
        <a-col :span="24">
          <a-space>
            <a-button type="primary" @click="handleInfoAdd">
              <template #icon><icon-plus /> </template> 新建
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="cloneColumns"
        :data="renderData"
        :bordered="false"
        :pageize="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #entrustsType="{ record }">
          {{ entrustsTypeObj[record.entrustsType] || record.entrustsType }}
        </template>
        <template #timeLimitStatus="{ record }">
          <a-switch v-model="record.timeLimitStatus" readonly></a-switch>
        </template>
        <template #timeLimit="{ record }">
          <span style="color: #bd3124">{{ record.timeLimit || '-- --' }}天</span>
        </template>
        <template #operations="{ record }">
          <a-button v-auth="['entrustSave']" type="text" @click="handleEdit(record)">编辑</a-button>
          <a-button v-auth="['entrustDelete']" type="text" @click="handleDelete(record)">删除</a-button>
        </template>
      </a-table>
      <FormInfoModal
        ref="formInfoRef"
        v-model:visible="modalVisible"
        :mdt-orgs-options="mdtOrgsOptions"
        :title="modalTitle"
        @comfirm="handleComfirm"
        @cancel="handleCancel"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getEntrustsList, deleteEntrusts, getEntrustsDetails } from '@/api/arcoApi/businessConf/caseSourceInfoManage'
import { getAllMdtOrg } from '@/api/arcoApi/businessConf/mediateOrgInfoManage'

import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface'

import { computed, nextTick, ref, reactive, watch } from 'vue'
import FormInfoModal from './components/form-info-modal.vue'
import useLoading from '@/layouts/appArco/hooks/loading'
import { Modal, Message } from '@arco-design/web-vue'
import { dictEnumValToObject } from '@/utils'
import dict from '@/dict/businessConf'
import _ from 'lodash'

type SizeProps = 'mini' | 'small' | 'medium' | 'large'
type Column = TableColumnData & { checked?: true }

const generateFormModel = () => {
  return {
    contactsName: '',
    creatorName: '',
    endTime: '',
    entrustsName: '',
    entrustsType: '',
    relaMdtOrgIdList: [],
    startTime: '',
    allTime: []
  }
}

const { loading, setLoading } = useLoading(true)
const modalType = ref<'add' | 'edit'>('add')
const pageZhName = ref('案源方')
// const pageEnName = ref('Entrusts')

const searchForm = ref<REQUEST_POST_ENTRUSTS_LISTSEARCH_PARAM_TYPE>(generateFormModel())
const renderData = ref<REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE[]>([])
const formInfoRef = ref<InstanceType<typeof FormInfoModal>>()
const lisPageTitle = ref<string>(`${pageZhName.value}查询`)
const cloneColumns = ref<Column[]>([])
const showColumns = ref<Column[]>([])
const size = ref<SizeProps>('medium')

const modalVisible = ref(false)

const basePagination = {
  pageSizeOptions: [10, 20, 30, 50, 100],
  showPageSize: true,
  showJumper: true,
  showTotal: true,
  pageSize: 10,
  current: 1,
  total: 0
}

const entrustsTypeOptions = ref<SelectOptionData[]>(dict.entrustsTypeOptions)
const mdtOrgsOptions = ref<SelectOptionData[]>([])
const pagination = reactive({ ...basePagination })

const columns = computed<TableColumnData[]>(() => [
  { width: 220, tooltip: true, ellipsis: true, fixed: 'left', title: '案源方名称', dataIndex: 'entrustsName' },
  { width: 140, title: '案源方类型', dataIndex: 'entrustsType', slotName: 'entrustsType' },
  { width: 120, tooltip: true, ellipsis: true, title: '案件前缀', dataIndex: 'entrustsCode' },
  { width: 120, tooltip: true, ellipsis: true, title: '联系人', dataIndex: 'contactsName' },
  { width: 120, tooltip: true, ellipsis: true, title: '联系方式', dataIndex: 'contactWay' },
  { width: 150, tooltip: true, ellipsis: true, title: '案源方联系地址', dataIndex: 'entrustsAddress' },
  { width: 120, tooltip: true, ellipsis: true, title: '创建人', dataIndex: 'creatorName' },
  { width: 200, tooltip: true, ellipsis: true, title: '关联调解组织', dataIndex: 'relaMdtOrg' },
  { width: 180, tooltip: true, ellipsis: true, title: '创建时间', dataIndex: 'createTime', align: 'center' },
  { width: 120, align: 'center', title: '调解时限状态', dataIndex: 'timeLimitStatus', slotName: 'timeLimitStatus' },
  { width: 140, align: 'center', title: '调解时限', dataIndex: 'timeLimit', slotName: 'timeLimit' },
  { width: 180, align: 'center', title: '操作', dataIndex: 'operations', fixed: 'right', slotName: 'operations' }
])

const modalTitle = computed(() => {
  return `${pageZhName.value}信息${modalType.value === 'add' ? '创建' : '编辑'}`
})

const entrustsTypeObj = computed(() => {
  return dictEnumValToObject(entrustsTypeOptions.value)
})

const handleInfoAdd = () => {
  modalType.value = 'add'
  modalVisible.value = true
}

const search = () => {
  pagination.current = 1
  getTableData({ ...pagination, ...searchForm.value })
}

const reset = () => {
  searchForm.value = generateFormModel()
}

const onPageChange = (current: number) => {
  getTableData({ ...pagination, current })
}

const onPageSizeChange = (pageSize: number) => {
  pagination.current = 1
  getTableData({ ...pagination, pageSize })
}

const handlePickerSelect = (timeArr: any) => {
  if (timeArr && timeArr.length) {
    searchForm.value.startTime = timeArr[0] || ''
    searchForm.value.endTime = timeArr[1] || ''
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
}

const handleDelete = (row: REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE) => {
  Modal.warning({
    title: '提示',
    hideCancel: false,
    alignCenter: true,
    content: `是否确认删除该“${pageZhName.value}”`,
    onOk: () => {
      deleteEntrusts(row.entrustsId)
        .then(() => {
          Message.success('操作成功')
          getTableData()
        })
        .catch(() => {
          Modal.error({
            title: '删除失败',
            content: `该“${pageZhName.value}”下已有“人员”或“案件”，请删除后在进行“${pageZhName.value}”删除`
          })
        })
    },
    onCancel: () => {}
  })
}

const handleEdit = async (row: REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE) => {
  if (row.entrustsId) {
    let entrustsData = await getEntrustsDetails(row.entrustsId)
    if (entrustsData) {
      modalType.value = 'edit'
      modalVisible.value = true
      nextTick(() => {
        formInfoRef.value?.setFormModel(entrustsData)
      })
    }
  }
}

const handleComfirm = () => {
  modalVisible.value = false
  getTableData()
}
const handleCancel = () => {
  modalVisible.value = false
}

const getTableData = async (pageInfo = { current: 1, pageSize: 10 }) => {
  setLoading(true)
  try {
    const data = await getEntrustsList({
      pageInfo: { size: pageInfo.pageSize, pageNumber: pageInfo.current },
      param: searchForm.value
    })
    pagination.pageSize = pageInfo.pageSize
    pagination.current = pageInfo.current
    pagination.total = data.total
    renderData.value = data.list
  } catch (err) {
  } finally {
    setLoading(false)
  }
}

const getAllMdtOrgData = () => {
  getAllMdtOrg().then((res) => {
    if (res && res.length) {
      mdtOrgsOptions.value = res.map((item) => ({ label: item.orgName, value: item.orgId }))
    }
  })
}

getAllMdtOrgData()
getTableData()

watch(
  () => columns.value,
  (val) => {
    cloneColumns.value = _.cloneDeep(val)
    cloneColumns.value.forEach((item) => {
      item.checked = true
    })
    showColumns.value = _.cloneDeep(cloneColumns.value)
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}
</style>
