/**
 * 位置链接生成工具 - 百度地图版本
 */

/**
 * 生成百度地图标记链接
 * @param longitude 经度
 * @param latitude 纬度
 * @param title 标题，默认为"登录位置"
 * @param content 内容，默认为"上次登录"
 * @param coordType 坐标类型，默认为"wgs84"
 * @returns 百度地图标记链接
 */
export function generateBaiduMapLink(
  longitude: string | number,
  latitude: string | number,
  title: string = '登录位置',
  content: string = '上次登录',
  coordType: string = 'wgs84'
): string {
  if (!longitude || !latitude) {
    return ''
  }

  // 构建百度地图标记URL，按照百度地图API规范
  // 注意：百度地图API的location参数格式是 纬度,经度
  const url = `http://api.map.baidu.com/marker?location=${latitude},${longitude}&title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}&output=html&coord_type=${coordType}&src=webapp.location.app`

  return url
}

/**
 * 格式化坐标显示文本
 * @param longitude 经度
 * @param latitude 纬度
 * @param precision 精度，默认6位小数
 * @returns 格式化的坐标文本
 */
export function formatCoordinateText(
  longitude: string | number, 
  latitude: string | number, 
  precision: number = 6
): string {
  if (!longitude || !latitude) {
    return '未知位置'
  }

  const lng = typeof longitude === 'string' ? parseFloat(longitude) : longitude
  const lat = typeof latitude === 'string' ? parseFloat(latitude) : latitude

  if (isNaN(lng) || isNaN(lat)) {
    return '无效坐标'
  }

  return `${lng.toFixed(precision)},${lat.toFixed(precision)}`
}

/**
 * 验证坐标是否有效
 * @param longitude 经度
 * @param latitude 纬度
 * @returns 是否为有效坐标
 */
export function isValidCoordinate(longitude: string | number, latitude: string | number): boolean {
  if (!longitude || !latitude) {
    return false
  }

  const lng = typeof longitude === 'string' ? parseFloat(longitude) : longitude
  const lat = typeof latitude === 'string' ? parseFloat(latitude) : latitude

  if (isNaN(lng) || isNaN(lat)) {
    return false
  }

  // 检查经纬度范围
  return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90
}

/**
 * 获取位置显示组件的props
 * @param longitude 经度
 * @param latitude 纬度
 * @param title 标题
 * @param content 内容
 * @param coordType 坐标类型
 * @returns 位置组件props
 */
export function getLocationLinkProps(
  longitude: string | number,
  latitude: string | number,
  title: string = '登录位置',
  content: string = '上次登录',
  coordType: string = 'wgs84'
) {
  const isValid = isValidCoordinate(longitude, latitude)

  if (!isValid) {
    return {
      text: '未知位置',
      href: '',
      disabled: true
    }
  }

  return {
    text: formatCoordinateText(longitude, latitude),
    href: generateBaiduMapLink(longitude, latitude, title, content, coordType),
    disabled: false
  }
}
