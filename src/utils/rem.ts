// rem等比适配配置文件

// 基准大小
const baseSize = 16
// 设置 rem 函数
function refreshRem() {
  // 当前页面宽度相对于 1920宽的缩放比例，可根据自己需要修改。
  const scale = document.documentElement.clientWidth / 1920
  // 设置页面根节点字体大小（“Math.min(scale, 2)” 指最高放大比例为2，可根据实际业务需求调整）
  document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px'
}
// 初始化
refreshRem()

let tid: NodeJS.Timeout

window.addEventListener(
  'resize',
  function () {
    clearTimeout(tid)
    tid = setTimeout(refreshRem, 300)
  },
  false
)

window.addEventListener(
  'pageshow',
  function (e) {
    if (e.persisted) {
      // 页面从浏览器的缓存中读取
      clearTimeout(tid)
      tid = setTimeout(refreshRem, 300)
    }
  },
  false
)
