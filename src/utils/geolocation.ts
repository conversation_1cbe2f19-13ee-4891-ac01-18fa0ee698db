/**
 * 浏览器定位工具 + 百度地图显示
 */

// 定位结果接口
export interface LocationResult {
  latitude: number
  longitude: number
  accuracy?: number
  address?: string
  source: 'browser'
  success: boolean
  message?: string
}

// 定位配置
const GEOLOCATION_CONFIG = {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 60000
}

// 浏览器原生定位
function getBrowserLocation(): Promise<LocationResult> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      resolve({
        latitude: 0,
        longitude: 0,
        source: 'browser',
        success: false,
        message: '浏览器不支持地理定位'
      })
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords
        resolve({
          latitude,
          longitude,
          accuracy,
          source: 'browser',
          success: true
        })
      },
      (error) => {
        console.error('浏览器定位失败:', error.message)
        resolve({
          latitude: 0,
          longitude: 0,
          source: 'browser',
          success: false,
          message: `浏览器定位失败: ${error.message}`
        })
      },
      {
        enableHighAccuracy: GEOLOCATION_CONFIG.enableHighAccuracy,
        timeout: GEOLOCATION_CONFIG.timeout,
        maximumAge: GEOLOCATION_CONFIG.maximumAge
      }
    )
  })
}

// 主要的定位函数 - 浏览器原生定位
export async function getCurrentLocation(): Promise<LocationResult> {
  try {
    // 获取浏览器原生定位（WGS84坐标系）
    const browserLocation = await getBrowserLocation()

    if (!browserLocation.success) {
      return browserLocation
    }

    return {
      latitude: browserLocation.latitude,
      longitude: browserLocation.longitude,
      accuracy: browserLocation.accuracy,
      source: 'browser',
      success: true,
      message: '浏览器定位成功，使用WGS84坐标系'
    }
  } catch (error) {
    console.error('定位失败:', error)
    return {
      latitude: 0,
      longitude: 0,
      source: 'browser',
      success: false,
      message: `定位失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}




// 格式化坐标为字符串
export function formatCoordinates(location: LocationResult): string {
  if (!location.success) {
    return ''
  }
  return `${location.longitude},${location.latitude}`
}