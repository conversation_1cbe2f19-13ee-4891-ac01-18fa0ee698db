import { Tab<PERSON><PERSON> } from '@/settings'

const copyTabKey = `${TabKey}-copy`

export function getMemory() {
  return sessionStorage.getItem(TabKey)
}

export function setMemory(tabList: OBJ_KEY_STR_ANY_TYPE[]) {
  return sessionStorage.setItem(TabKey, JSON.stringify(tabList))
}

export function removeMemory() {
  sessionStorage.removeItem(TabKey)
}

export function getCopyTab() {
  return localStorage.getItem(copyTabKey)
}

export function setCopyTab(tabList: OBJ_KEY_STR_ANY_TYPE) {
  return localStorage.setItem(copyTabKey, JSON.stringify(tabList))
}

export function removeCopyTab() {
  localStorage.removeItem(copyTabKey)
}
