function isMobile() {
  // 判断是否移动设备
  return (
    typeof window.orientation !== 'undefined' ||
    navigator.userAgent.indexOf('IEMobile') !== -1 ||
    navigator.userAgent.indexOf('iPhone') !== -1 ||
    (navigator.userAgent.indexOf('Android') !== -1 && navigator.userAgent.indexOf('Mobile') !== -1) ||
    navigator.userAgent.indexOf('BlackBerry') !== -1 ||
    navigator.userAgent.indexOf('Opera Mini') !== -1
  )
}

function resizeWindow() {
  let appDom = document.querySelector('#app') as HTMLDivElement
  if (appDom) {
    // let width = 1920
    // let height = 1080
    appDom.style.transformOrigin = 'top left'
    // 获取元素与视图宽高的比例
    // let scaleX = window.innerWidth / width
    // let scaleY = window.innerHeight / height
    // 设置缩放比例（取最小值）
    // let scale = Math.min(scaleX, scaleY)

    // let translateX = (window.innerWidth - width * scale) / 2
    // let translateY = (window.innerHeight - height * scale) / 2
    // 设置平移的距离和缩放比例
    // appDom.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`
    // appDom.style.width = window.innerWidth / scale + 'px'
    // appDom.style.height = window.innerHeight / scale + 'px'

    let scale = window.screen.width / 1920
    appDom.style.transform = `translate(0, 0) scale(${scale})`
    appDom.style.width = window.innerWidth / scale + 'px'
    appDom.style.height = window.innerHeight / scale + 'px'

    // let s = window.screen.width / 1920
    // document.body.style.transformOrigin = '0 0'
    // document.body.style.transform = `scale(${s}, ${s})`
    // document.body.style.width = window.innerWidth / s + 'px'
    // document.body.style.height = window.innerHeight / s + 'px'
  }
}

window.onresize = function () {
  !isMobile() && resizeWindow()
}

!isMobile() && resizeWindow()
