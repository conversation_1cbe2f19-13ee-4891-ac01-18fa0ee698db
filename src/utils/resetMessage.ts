import type { MessageHandler, MessageOptions, messageType } from 'element-plus/lib/components/message/src/message'

import { ElMessage } from 'element-plus'

let messageIns: MessageHandler

const resetMessage: any = (options: MessageOptions) => {
  if (messageIns) {
    messageIns.close()
  }
  messageIns = ElMessage(options)
}

const messageTypes: messageType[] = ['error', 'success', 'info', 'warning']

messageTypes.forEach((type) => {
  resetMessage[type] = (options: MessageOptions) => {
    if (typeof options === 'string') {
      options = { message: options }
    }
    options.type = type
    return resetMessage(options)
  }
})

export const message = resetMessage
