/**
 * Created by PanJiaChen on 16/11/18.
 */
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface'

// 格式化树数据结构
export const formatData = function (menuList: OBJ_KEY_STR_ANY_TYPE[]) {
  for (let item of menuList) {
    if (!item.children?.length) {
      delete item.children
    } else {
      formatData(item.children)
    }
  }
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele: HTMLElement, cls: string) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string) {
  if (!hasClass(ele, cls)) {
    ele.className += ' ' + cls
  }
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 将对象转换成select options 形式
 * @param obj 传入对象
 * @returns []
 */
export function toOptions(obj: OBJ_KEY_STR_ANY_TYPE) {
  return Object.keys(obj).map((item) => {
    return { value: item, label: obj[item] }
  })
}

// 二进制转base64
export function arrayBufferToBase64(buffer: ArrayBuffer) {
  var binary: string = ''
  var bytes = new Uint8Array(buffer)
  var len = bytes.byteLength
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return window.btoa(binary)
}

// 去除对象中空属性
export function removeNullParam(obj: any, removeKey?: string) {
  Object.keys(obj).forEach((key) => {
    let value = obj[key]
    if (key === removeKey) {
      delete obj[key]
    } else if (typeof value === 'undefined' || value === '' || value === null) {
      delete obj[key]
    } else if (typeof value === 'object' && Array.isArray(value) && !value.length) {
      delete obj[key]
    } else if (typeof value === 'object' && !Object.keys(value).length) {
      delete obj[key]
    } else if (Object.prototype.toString.call(value) === '[object Object]') {
      removeNullParam(obj[key])
    }
  })
  return obj
}

// 字典枚举值对象化
export function dictEnumValToObject(arr: SelectOptionData[], prefix?: string) {
  let mapObj = arr.reduce((row, reduceItem) => {
    let targetItem = reduceItem as { label: string; value: string }
    if (reduceItem.value || reduceItem.value === 0) {
      row[targetItem.value] = prefix ? `${prefix}-${targetItem.label}` : targetItem.label
      if (reduceItem.children) {
        let chidrenRow = dictEnumValToObject(reduceItem.children, row[targetItem.value])
        row = Object.assign(chidrenRow, row)
      }
    }
    return row
  }, {})
  return mapObj
}
