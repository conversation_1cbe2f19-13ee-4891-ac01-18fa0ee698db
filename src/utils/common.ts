import '@scss/index.scss'

import { customComponents } from '@/components'
import arcomain from '@/layouts/appArco/main'
import directives from '@/directives'
import '@/assets/iconfont/iconfont'
import mitt from '@/utils/eventBus'
import proto from '@/utils/proto'
import filters from '@/filters'
import i18n from '@/locale'
import '@/utils/rem'
import IconFont from '@/utils/iconFont'

import type { App } from 'vue'
import { useDictStore } from '@arco/store'
import DictDataSelect from '@/components/dictDataSelect/index.vue'
export default {
  install(app: App) {
    customComponents.forEach((component) => {
      if (component.name && component.module) app.use(component.module)
    })
    // app.use(customComponents)
    app.use(directives) // 注册全局指令
    app.use(arcomain) // arco
    app.use(filters) // 注册全局过滤器
    app.use(proto) // 拓展原型
    app.use(i18n) // 国际化
		app.component('IconFont', IconFont)
		// 注册字典
		app.component('DictDataSelect',DictDataSelect);

    app.config.globalProperties.productionTip = false
    app.config.globalProperties.$bus = mitt
    app.config.globalProperties.$useDict = useDictStore()
  }
}
