import type { AxiosResponse } from 'axios'

export default {
  /**
   * 下载文件
   * @param url
   * @param paramTemp
   */
  download(url: string, paramTemp: OBJ_KEY_STR_ANY_TYPE) {
    let param = { ...paramTemp }
    param.doPage = false
    let formObj = param
    if (formObj) {
      let exportFrames = document.getElementById('export-frame')
      if (exportFrames) {
        let exportFrame = document.createElement('iframe')
        exportFrame.name = 'export-frame'
        exportFrame.hidden = true
        exportFrame.id = 'export-frame'
        document.body.appendChild(exportFrame)
      }
      let formStr = document.createElement('form')
      formStr.method = 'post'
      formStr.hidden = true
      formStr.action = url
      formStr.target = 'export-frame'
      for (let key in formObj) {
        if (formObj[key] === null || formObj[key] === undefined) {
          continue
        }
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = formObj[key]
        formStr.appendChild(input)
      }
      let $form = document.body.appendChild(formStr)
      $form.submit()
      $form.remove()
    }
  },
  blobExport(res: AxiosResponse) {
    let fileName = res.headers['content-disposition']?.split('=')
    fileName = decodeURIComponent(fileName[fileName.length - 1].replace(/"/g, ''))
    const blob = new Blob([res.data], { type: res.headers['content-type'] }) // 构造一个blob对象来处理数据 /* endings: 'native' */
    // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
    // IE10以上支持blob但是依然不支持download
    if ('download' in document.createElement('a')) {
      // 支持a标签download的浏览器
      const link = document.createElement('a') // 创建a标签
      link.download = fileName // a标签添加属性
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click() // 执行下载
      URL.revokeObjectURL(link.href) // 释放url
      document.body.removeChild(link) // 释放标签
    } else {
      // 其他浏览器
      navigator.msSaveBlob(blob, fileName)
    }
  }
}
