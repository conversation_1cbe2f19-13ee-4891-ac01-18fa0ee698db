
import { loading } from '@arco/utils/loading'
import _ from 'lodash'

let needLoadingRequestCount = 0
let serviceLoading = new loading()

function startLoading() {
  serviceLoading.show()
}

function endLoading() {
  serviceLoading.hide()
}

const tryCloseLoading = () => {
  if (needLoadingRequestCount === 0) {
    endLoading()
  }
}

export function showFullScreenLoading() {
  if (needLoadingRequestCount === 0) {
    startLoading()
  }
  needLoadingRequestCount++
}

export function tryHideFullScreenLoading() {
  if (needLoadingRequestCount <= 0) {
    return
  }
  needLoadingRequestCount--
  if (needLoadingRequestCount === 0) {
    _.debounce(tryCloseLoading, 200)()
  }
}
