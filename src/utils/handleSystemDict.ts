import { getDictList } from '@/api/eleApi/common'
import { setDict } from '@/utils/dict'

const updateDictList = (dictList: COMMOM_LISTALL_DICTS_TYPE = []) => {
  var dict: OBJ_KEY_STR_ANY_TYPE = {}
  for (var i = 0; i < dictList.length; i++) {
    dict[dictList[i].dictType] = {}
    for (var u = 0; u < dictList[i].sysDictDataList.length; u++) {
      dict[dictList[i].dictType][dictList[i].sysDictDataList[u].dictKey] = dictList[i].sysDictDataList[u].dictTag
    }
  }
  setDict(dict)
}

export const initSystemDict = () => {
  getDictList().then((res) => updateDictList(res))
}
