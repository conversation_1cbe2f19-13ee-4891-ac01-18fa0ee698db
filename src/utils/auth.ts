import { To<PERSON><PERSON><PERSON>, tokenLocalStorageExpires } from '@/settings'

export function getToken() {
  return getLocalStorage(TokenKey)
}

export function setToken(token: string) {
  return setLocalStorage(TokenKey, token, tokenLocalStorageExpires + 'd')
}

export function removeToken() {
  deleteLocalStorage(TokenKey)
}

export function setCookies(name: string, value: string, time: string) {
  var strSec = getSecond(time)
  var exp = new Date()
  exp.setTime(exp.getTime() + strSec * 1)
  document.cookie = name + '=' + escape(value) + ';expires=' + exp.toUTCString()
}

function getSecond(str: string) {
  var str1 = str.substring(0, str.length - 1)
  var str2 = str.substring(str.length - 1)
  var num1: number = parseInt(str1, 10)
  if (str2 === 's') {
    return num1 * 1000
  } else if (str2 === 'm') {
    return num1 * 60 * 1000
  } else if (str2 === 'h') {
    return num1 * 60 * 60 * 1000
  } else if (str2 === 'd') {
    return num1 * 24 * 60 * 60 * 1000
  } else if (str2 === 'M') {
    return num1 * 30 * 24 * 60 * 60 * 1000
  } else {
    return 0
  }
}

// localStorage
export function setLocalStorage(
  key: string,
  value: string | boolean | number | object,
  time = tokenLocalStorageExpires + 'd'
) {
  if (time) {
    var strSec = getSecond(time)
    var expires = new Date().getTime() + strSec * 1
    let params = { data: value, expires }
    localStorage.setItem(key, JSON.stringify(params))
  } else {
    let params = { data: value }
    localStorage.setItem(key, JSON.stringify(params))
  }
}

export function getLocalStorage(key: string) {
  var data = localStorage.getItem(key)
  var dataObj = data ? JSON.parse(data) : ''
  if (dataObj && (!dataObj.expires || dataObj.expires - new Date().getTime() > 0)) {
    return dataObj.data
  } else {
    if (dataObj && dataObj.data) {
      deleteLocalStorage(key)
    }
    return ''
  }
}

export function deleteLocalStorage(key: string) {
  localStorage.removeItem(key)
}
