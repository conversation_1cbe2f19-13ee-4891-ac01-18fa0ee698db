/**
 * axios封装
 * 请求拦截、响应拦截、错误统一处理
 */
import { tryHideFullScreenLoading, showFullScreenLoading } from './loading'
import { TokenKeyOfHeader, timeout } from '@/settings'
import { useUserStore } from '@/layouts/appArco/store'
import { getToken, setToken } from '@/utils/auth'
import { Message } from '@arco-design/web-vue'
import type { AxiosResponse } from 'axios'
import axios from 'axios'

const tokenKey = TokenKeyOfHeader
const CancelToken = axios.CancelToken
let source = CancelToken.source()

/**
 * 提示函数
 * 禁止点击蒙层、显示5秒后关闭
 */
const messageTip = (info: string | OBJ_KEY_STR_ANY_TYPE) => {
  if (typeof info === 'string') {
    Message.error({ content: info, duration: 5000, closable: true })
  } else {
    Message.error({ content: info.message || info.msg, duration: 5000, closable: true })
  }
}

// 创建axios实例
var instance = axios.create({ timeout })
// 设置post请求头、设置put请求头
instance.defaults.headers.post['Content-Type'] = instance.defaults.headers.put['Content-Type'] =
  'application/json;charset:utf8'
/**
 * 请求拦截器
 * 每次请求前，如果存在token则在请求头中携带token
 */
instance.interceptors.request.use(
  (config) => {
    // 登录流程控制中，根据本地是否存在token判断用户的登录情况
    // 但是即使token存在，也有可能token是过期的，所以在每次的请求头中携带token
    // 后台根据携带的token判断用户的登录情况，并返回给我们对应的状态码
    // 而后我们可以在响应拦截器中，根据状态码进行一些统一的操作。
    const token = getToken()
    token && (config.headers[tokenKey] = token)
    config.cancelToken = source.token
    if (!config.hideLoading) {
      showFullScreenLoading()
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  // 请求成功
  async (res) => {
    if (!res.config.hideLoading) {
      tryHideFullScreenLoading()
    }
    if (res.status === 200) {
      return successHandle(res)
    } else {
      return Promise.reject(res)
    }
  },

  // 请求失败
  (error) => {
    tryHideFullScreenLoading()
    // 如果是取消请求
    if (axios.isCancel(error)) {
      return new Promise(() => {})
    }

    const { response } = error
    if (response) {
      // 请求已发出，但是不在2xx的范围
      errorHandle(response.status, response.data)
      return Promise.reject(response)
    } else {
      // 处理断网的情况
      // eg:请求超时或断网时，更新state的network状态
      // network状态在app.vue中控制着一个全局的断网提示组件的显示隐藏
      // 关于断网组件中的刷新重新获取数据，会在断网组件中说明
      if( error.code === 'ECONNABORTED') {
        messageTip('请求已超时,请重试!')
      } else if (!window.navigator.onLine) {
        // store.commit('changeNetwork', false)
      }
      return Promise.reject(error)
    }
  }
)

/**
 * 请求200成功后的统一处理
 * @param {Object} res 响应数据
 */
const successHandle = (res: AxiosResponse) => {
  let { result, message, code, success } = res.data
  let newToken = res.headers[tokenKey.toLowerCase()]
  if (newToken) {
    setToken(res.token || newToken)
  }
  // 文件的情况
  if (code === undefined) {
    // 后台成功响应，但是存在问题 二进制转json
    if (res.data.type === 'application/json') {
      let reader = new FileReader()
      reader.readAsText(res.data, 'utf-8')
      reader.addEventListener('loadend', () => {
        if (typeof reader.result === 'string') {
          let res = JSON.parse(reader.result)
          messageTip(res.message)
          return Promise.reject(res.message)
        } else {
          return Promise.reject(reader.result)
        }
      })
    } else {
      return Promise.resolve(res)
    }
  }

  if (code !== undefined) {
    if (code === 0) {
      // 成功响应，成功返回
      if (success === false) {
        return Promise.resolve(res.data)
      }
      return Promise.resolve(result)
    } else {
      // 后台成功响应，但是存在问题
      messageTip(message)
      return Promise.reject(message)
    }
  }
}

/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败的状态码
 */
const errorHandle = async (status: number, other: OBJ_KEY_STR_ANY_TYPE) => {
  if ([401, 403, 502].includes(status)) {
    source.cancel()
    source = CancelToken.source()
  }

  // 状态码判断
  let timer: NodeJS.Timeout
  switch (status) {
    case 401:
      messageTip('登录验证已失效，即将返回登录页面...')
      timer = setTimeout(() => {
        clearTimeout(timer)
        const userStore = useUserStore()
        userStore.logoutCallBack()
      }, 2500)
      break
    case 403:
      messageTip('没有进行该操作的权限')
      break
    case 404:
      messageTip('请求的资源不存在')
      break
    case 405:
      messageTip('请求方法不允许')
      break
    case 413:
      messageTip('上传的文件过大')
      break
    case 502:
      messageTip('当前服务不可用，请稍后重试')
      break
    default:
      messageTip(other)
  }
}
export default instance
