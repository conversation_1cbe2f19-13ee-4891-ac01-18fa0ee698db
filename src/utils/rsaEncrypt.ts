import { JSEncrypt } from 'jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLgeLP6dAUwwJp6UzrinVWEFaAQNBjdWCsmUkrIR2LC1OLQCeojbhO8nKoOmLF7RLHhELB9gmFYzP91wWhUxuYXhjTISvpeGbgaJUwc3AC3eOpJ0Fa2tg+6+xiK5Q3tUSTFFFpFwewxrCYYbqeJyFxcngGZISC9H4HFNDgcFO3bwIDAQAB'

const privateKey = ''

// 加密
export function encrypt(encryptCode: string) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥

  return encryptor.encrypt(encryptCode) // 对需要加密的数据进行加密
}

// 解密
export function decrypt(decryptCode: string) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey)

  return encryptor.decrypt(decryptCode)
}
