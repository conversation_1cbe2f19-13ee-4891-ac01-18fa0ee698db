/**
 * Vue 原型方法和属性 拓展
 */
import { phoneReg, emailReg, phoneAndTelReg, pwdReg } from '@/assets/ts/regexp'
import { getDict, dictListPrivate } from '@/utils/dict'
import identityTest from '@/utils/identityTest'
import { preview } from '@/api/commonApi/file'
import { Message } from '@arco-design/web-vue'
import { useAppStore } from '@arco/store'
import _ from 'lodash'

import type { App } from 'vue'

export const getRequest = function () {
  let url = window.location.search // 获取url中"?"符后的字串
  let theRequest: OBJ_KEY_STR_STR_TYPE = {}
  if (url.indexOf('?') !== -1) {
    let str = url.substr(1)
    let strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1])
    }
  }
  return theRequest
}

// 获取文件file对象
export const getFileObj = function (path: string) {
  if (!path) {
    return null
  }
  let ganIndex = path.lastIndexOf('/')
  let fileName = path.substring(ganIndex + 1, path.length)
  let folderName = path.substring(0, ganIndex + 1)

  return { path: folderName, fileName }
}

export const getPreviewPath = (path: string) => {
  if (!path) {
    return ''
  }
  let fileObject = getFileObj(path)
  if (fileObject) {
    return preview(fileObject)
  }
}

function toDecimal(val: number, digit = 2) {
  if (isNaN(val)) {
    return 0
  }
  var num = Math.round(val * Math.pow(10, digit)) / Math.pow(10, digit)
  if (num % 1 === 0) {
    return num.toString()
  } else {
    return num.toFixed(digit)
  }
}

function banBackSpace(e: any) {
  var ev = e || window.event
  // 各种浏览器下获取事件对象
  var obj = ev.relatedTarget || ev.srcElement || ev.target || ev.currentTarget
  // 按下Backspace键
  if (ev.keyCode === 8) {
    var tagName = obj.nodeName // 标签名称
    // 如果标签不是input或者textarea则阻止Backspace
    if (tagName !== 'INPUT' && tagName !== 'TEXTAREA') {
      return stopIt(ev)
    }
    var tagType = obj.type.toUpperCase() // 标签类型
    // input标签除了下面几种类型，全部阻止Backspace
    if (tagName === 'INPUT' && tagType !== 'TEXT' && tagType !== 'TEXTAREA' && tagType !== 'PASSWORD') {
      return stopIt(ev)
    }
    // input或者textarea输入框如果不可编辑则阻止Backspace
    if ((tagName === 'INPUT' || tagName === 'TEXTAREA') && (obj.readOnly === true || obj.disabled === true)) {
      return stopIt(ev)
    }
  }
}

function stopIt(ev: Event) {
  if (ev.preventDefault) {
    // preventDefault()方法阻止元素发生默认的行为
    ev.preventDefault()
  }
  if (ev.returnValue) {
    // IE浏览器下用window.event.returnValue = false;实现阻止元素发生默认的行为
    ev.returnValue = false
  }
  return false
}

function keyEvent() {
  // 实现对字符码的截获，keypress中屏蔽了这些功能按键
  document.onkeypress = banBackSpace
  // 对功能按键的获取
  document.onkeydown = banBackSpace
}

export const noop = () => {}

export const acWarningMess = function (message = '') {
  Message.warning({ closable: true, content: message })
}
export const acInfoMess = function (message = '') {
  Message.info({ closable: true, content: message })
}
export const acSuccessMess = function (message = '') {
  Message.success({ closable: true, content: message })
}
export const acErrorMess = function (message = '') {
  Message.error({ closable: true, content: message })
}

// 手机正则校验
export const validateMobiles = (_: any, value: string, callback: Function) => {
  if (value) {
    if (!phoneReg.test(value)) {
      callback(new Error('请输入正确的手机号码'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 联系电话校验
export const validatePhoneAndTel = (_: any, value: string, callback: Function) => {
  if (value) {
    if (!phoneAndTelReg.test(value)) {
      callback(new Error('请输入正确的联系电话'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 身份证校验
export const validateIdCard = (_: any, value: string, callback: Function) => {
  let mess = identityTest.test(value)
  if (mess) {
    callback(new Error(mess))
  } else {
    callback()
  }
}

// 正数校验
export const validatePlusNum = (_: any, value: number, callback: Function) => {
  if (isNaN(value)) {
    callback(new Error('非法输入'))
    return
  }
  if (value < 0) {
    callback(new Error('不能小于0'))
  } else {
    callback()
  }
}

// 密码校验 ：密码长度最少8位 大写字母，小写字母，数字，特殊符号必须四选三
export const validatePwd = (_: any, value: string, callback: Function) => {
  if (!pwdReg.test(value)) {
    callback(new Error('长度最少8位;大写、小写字母、数字、特殊符号四选三'))
  } else {
    callback()
  }
}

// 邮箱校验
export const validateEmails = (_: any, value: string, callback: Function) => {
  if (!emailReg.test(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

// 时间日期格式化
export const datetimeFormat = function (date: string, fmt = 'yyyy-MM-dd hh:mm') {
  if (!date) {
    return ''
  }
  let _date = new Date(date)
  let o: { [x: string]: number } = {
    'M+': _date.getMonth() + 1, // 月份
    'd+': _date.getDate(), // 日
    'h+': _date.getHours(), // 小时
    'm+': _date.getMinutes(), // 分
    's+': _date.getSeconds(), // 秒
    'q+': Math.floor(_date.getMonth() / 3) + 1, // 季度
    S: _date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (_date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k].toString() : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fmt
}

// 日期加减
export const addOrReduceDate = function (type: string, date: string, val: string | number) {
  var nowDate: Date | null = null
  var strDate: number | string | null = null
  var num = typeof val === 'string' ? parseInt(val, 10) : val
  var seperator1 = '-'
  var seperator2 = ':'
  if (date == '') {
    nowDate = new Date()
  } else {
    nowDate = new Date(date)
  }

  if (type === 'Y') {
    nowDate.setFullYear(nowDate.getFullYear() + num)
  }
  if (type === 'M') {
    nowDate.setMonth(nowDate.getMonth() + num)
  }
  if (type === 'D') {
    nowDate.setDate(nowDate.getDate() + num)
  }
  if (type === 'A') {
    nowDate.setFullYear(nowDate.getFullYear() + num)
    nowDate.setMonth(nowDate.getMonth() + num)
    nowDate.setDate(nowDate.getDate() + num)
  }
  var year = nowDate.getFullYear() // 年
  var month: string | number = nowDate.getMonth() + 1 // 月
  strDate = nowDate.getDate() // 日
  var hours = nowDate.getHours() // 时
  var minutes = nowDate.getMinutes() // 分
  var seconds: string | number = nowDate.getSeconds() // 秒
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  if (seconds >= 0 && seconds <= 9) {
    seconds = '0' + seconds
  }
  var dateStr =
    year + seperator1 + month + seperator1 + strDate + ' ' + hours + seperator2 + minutes + seperator2 + seconds
  return dateStr
}

// 金额处理
export const _toCurrencyString = function (val: number, digit = 2) {
  if (isNaN(val)) {
    return val
  }
  let str = toDecimal(val, digit)
  return typeof str === 'string' ? str.replace(/\d(?=(?:\d{3})+\b)/g, '$&,') : str
}

// 脱敏
export const dzPhone = function (phone: string) {
  if (!phone) return phone
  const appStore = useAppStore()
  const paramValue = appStore.systemParam.isPhoneSign
  if ( paramValue === 1 && phone.length > 7) {
    let phoneList = phone.split('')
    phoneList.map((_, idx) => {
      if ([3, 4, 5, 6].indexOf(idx) !== -1) {
        phoneList[idx] = '*'
      }
    })
    const newPhone = phoneList.join('')
    return newPhone
  }
  return phone
}

// 万元处理
export const _toTenThousand = function (val: number, dight = 4, need = '万元') {
  if (isNaN(val)) {
    return '0 元'
  }
  if (need === '元') {
    return `${_toCurrencyString(val)} 元`
  } else if (need === '万元') {
    if (parseFloat(val.toString()) < Math.pow(10, 4)) {
      return `${_toCurrencyString(val)} 元`
    } else {
      let num = Math.round(val) / Math.pow(10, 4)
      let numstr = num.toFixed(dight)
      let arr = numstr.split('.')
      let str = _toCurrencyString(Number(arr[0]), dight)
      numstr = `${str}.${arr[1]}`
      return `${numstr} 万元`
    }
  } else {
    if (parseFloat(val.toString()) < Math.pow(10, 4)) {
      return `${_toCurrencyString(val)} 元`
    } else if (parseFloat(val.toString()) < Math.pow(10, 8)) {
      let num = Math.round(val) / Math.pow(10, 4)
      let numstr = num.toFixed(dight)
      let arr = numstr.split('.')
      let str = _toCurrencyString(Number(arr[0]), dight)
      numstr = `${str}.${arr[1]}`
      return `${numstr} 万元`
    } else {
      let num = Math.round(val) / Math.pow(10, 8)
      let numstr = num.toFixed(dight)
      let arr = numstr.split('.')
      let str = _toCurrencyString(Number(arr[0]), dight)
      numstr = `${str}.${arr[1]}`
      return `${numstr} 亿元`
    }
  }
}

export const isEmptyObject = (target: object) => {
  return _.isEmpty(target)
}

export const hasClass = function (ele: HTMLElement, cls: string) {
  if (cls.replace(/\s/g, '').length === 0) {
    return false
  }
  return new RegExp(' ' + cls + ' ').test(' ' + ele.className + ' ')
}

/**
 * 获取字典
 * @param {*} type
 */
function getDictByType(type: string) {
  if (type.indexOf('$') === 0) {
    return dictListPrivate[type] || {}
  }
  return getDict()?.[type] || {}
}

/**
 * 获取字典数组
 * @param {*} type
 */
export const getDictList = function getDictList(type: string) {
  var list: { key: number | string; value: string }[] = []
  var obj = getDictByType(type)
  Object.keys(obj).forEach((item) => {
    list.push({ key: isNaN(parseInt(item, 10)) ? item : parseInt(item, 10), value: obj[item] })
  })
  if (type === 'db_type') {
    list.sort((a, b) => a.value.toLowerCase().localeCompare(b.value.toLowerCase()))
  }
  return list
}

// 获取图片路径
export const getImgFilePath = (filePath: string) => {
  if (filePath) {
    let index = filePath.lastIndexOf('/')
    let fileFolder = filePath.substring(0, index + 1)
    let fileName = filePath.substring(index + 1, filePath.length)
    let resultPath = preview({ fileFolder, fileName })
    return resultPath
  } else {
    return ''
  }
}
// 禁止复制(禁止选中)
export const preventCopy = (copyPermission: boolean) => {
  if (!copyPermission) {
    document.onselectstart = function () {
      return false
    }
  } else {
    return
  }
}

const protoProperties = {
  validatePhoneAndTel: validatePhoneAndTel,
  _toCurrencyString: _toCurrencyString,
  validatePlusNum: validatePlusNum,
  validateMobiles: validateMobiles,
  addOrReduceDate: addOrReduceDate,
  validateIdCard: validateIdCard,
  validateEmails: validateEmails,
  datetimeFormat: datetimeFormat,
  getImgFilePath: getImgFilePath,
  _toTenThousand: _toTenThousand,
  isEmptyObject: isEmptyObject,
  acWarningMess: acWarningMess,
  acSuccessMess: acInfoMess,
  preventCopy: preventCopy,
  validatePwd: validatePwd,
  acErrorMess: acErrorMess,
  acInfoMess: acInfoMess,
  getRequest: getRequest,
  $dictList: getDictList,
  $dict: getDictByType,
  keyEvent: keyEvent,
  hasClass: hasClass,
  dzPhone: dzPhone,
  noop: noop
}

export type PROTO_FUNCTION_TYPE = typeof protoProperties

const install = function (app: App) {
  for (const [key, func] of Object.entries(protoProperties)) {
    app.config.globalProperties[key] = func
  }
}

export default { install }
