import { DictKey } from '@/settings'

export function getDict() {
  let dictStorage = localStorage.getItem(DictKey)
  return dictStorage ? JSON.parse(dictStorage) : ''
}

export function setDict(dict: OBJ_KEY_STR_ANY_TYPE) {
  return localStorage.setItem(DictKey, JSON.stringify(dict))
}

export function removeDict() {
  localStorage.removeItem(DictKey)
}

// 前端私有字典对象，字典名称以 $ 开头
export const dictListPrivate: { [x: string]: { [x: number]: string | number | boolean } } = {
  // 消息类型
  $message_time_type: {
    1: '今日',
    2: '本周',
    3: '本月',
    4: '最近一月',
    5: '最近三月',
    6: '最近半年',
    7: '最近一年',
    91: '全部'
  }
}
