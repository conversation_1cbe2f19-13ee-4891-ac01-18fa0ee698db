import { RouteRecordRaw, RouteRecordName } from 'vue-router'
import appClientMenus from '@arco/router/app-menus'
import { DirectiveBinding, computed } from 'vue'

import { useAppStore } from '@arco/store'
import router from '@arco/router'

function findMenuOpenKeys(menus: RouteRecordRaw[], target: RouteRecordName) {
  let result = {} as RouteRecordRaw
  let isFind = false
  const backtrack = (item: RouteRecordRaw, keys: string[]) => {
    if (item.name === target) {
      isFind = true
      result = item
      return
    }
    if (item.children?.length) {
      item.children.forEach((el) => {
        backtrack(el, [...keys, el.name as string])
      })
    }
  }
  menus.forEach((el) => {
    if (isFind) return
    backtrack(el, [el.name as string])
  })
  return result
}

/**
 * @description: 效验用户权限
 */
function checkAuth(el: HTMLElement, binding: DirectiveBinding) {
 
  const appStore = useAppStore()

  const appRoute = computed(() => (appStore.menuFromServer ? appStore.appAsyncMenus : appClientMenus))

  const currentRoute = router.currentRoute.value
  const routeName = currentRoute.name
  const { value } = binding

  if(!routeName) {
    el.parentNode?.removeChild(el)
    return
  }
  const { children } = findMenuOpenKeys(appRoute.value as RouteRecordRaw[], routeName)
  if(children && children.length) {
    const menus_auth = children.map(item => item.name)
    // console.log(`当前页面操作权限: [${menus_auth}]`)
    // console.log(`当前绑定操作权限: ${value}`)
    if (Array.isArray(value)) {
      if (value.length > 0) {
        const hasPermission = menus_auth.filter(auth => value.includes(auth)) 
        if (hasPermission.length === 0 && el.parentNode) {
          el.parentNode.removeChild(el)
        }
      }
    } else if(Object.prototype.toString.call(value) === '[object String]') {
      if (!menus_auth.includes(value)) {
        el.parentNode?.removeChild(el)
      }
    }
  }
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkAuth(el, binding)
  }
  // updated(el: HTMLElement, binding: DirectiveBinding) {
  //   checkAuth(el, binding)
  // }
}
