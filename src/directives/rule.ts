import { useUserStore } from '@arco/store'

import { DirectiveBinding } from 'vue'

function checkRule(el: HTMLElement, binding: DirectiveBinding) {
	const { value } = binding

	if (!value || value.length <=0) {
		el.parentNode?.removeChild(el)
		return
	}

	const userStore = useUserStore()
	const currentUserRuleStr = userStore.userInfo.roleNames
	const currentUserRules = currentUserRuleStr.split(',')

	if (!currentUserRules || currentUserRules.length <= 0) {
		el.parentNode?.removeChild(el)
		return
	}

	if (Array.isArray(value)) {
		if (value.length > 0) {
			const hasRules = currentUserRules.filter((auth) => value.includes(auth))
			if (hasRules.length === 0 && el.parentNode) {
				el.parentNode.removeChild(el)
			}
		}
	} else if (Object.prototype.toString.call(value) === '[object String]') {
		if (!currentUserRules.includes(value)) {
			el.parentNode?.removeChild(el)
		}
	}
}

export function hasRule(value: string | string[] | undefined): boolean {

	if (!value || value.length <=0) {
		return false
 	}

	const userStore = useUserStore()
	const currentUserRuleStr = userStore.userInfo.roleNames
	const currentUserRules = currentUserRuleStr.split(',')
	if (!currentUserRules || currentUserRules.length <= 0){
		return false
	}

	if(!Array.isArray(value)){
		value = [value]
	}
	return value.some(item => currentUserRules.includes(item))
}



export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkRule(el, binding)
  }
}
