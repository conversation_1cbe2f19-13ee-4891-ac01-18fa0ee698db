<template>
  <a-layout class="layout" :class="{ mobile: appStore.hideMenu }">
    <div v-if="navbar" class="layout-navbar">
      <NavBar :sys-name="systemName" />
    </div>
    <a-layout>
      <a-layout>
        <a-layout-sider
          v-if="renderMenu"
          v-show="!hideMenu"
          class="layout-sider"
          breakpoint="xl"
          :collapsible="true"
          :width="menuWidth"
          :style="{ paddingTop: navbar ? '60px' : '' }"
          :hide-trigger="true"
          :collapsed="collapsed"
          @collapse="setCollapsed"
        >
          <div class="menu-wrapper">
            <Menu />
          </div>
        </a-layout-sider>
        <a-drawer
          v-if="hideMenu"
          :visible="drawerVisible"
          placement="left"
          :footer="false"
          mask-closable
          :closable="false"
          @cancel="drawerCancel"
        >
          <Menu />
        </a-drawer>
        <a-layout class="page-layout" :style="paddingStyle">
          <TabBar v-if="appStore.tabBar" />
          <a-layout-content class="page-layout-content">
            <!-- <Breadcrumb v-if="appStore.breadcrumb" :items="breadcrumbRoute" /> -->
            <a-config-provider :update-at-scroll="true" :scroll-to-close="true">
              <PageLayout class="page-content mdt-common-scrollbar" />
            </a-config-provider>
            <global-setting />
          </a-layout-content>
          <!-- <Footer v-if="footer" :sys-name="systemName" /> -->
        </a-layout>
      </a-layout>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import GlobalSetting from './components/global-setting/index.vue'

// import Breadcrumb from './components/breadcrumb/index.vue'
// import Footer from './components/footer/index.vue'
import TabBar from './components/tab-bar/index.vue'
import NavBar from './components/navbar/index.vue'
import Menu from './components/menu/index.vue'

import { ref, computed, watch, provide, onMounted } from 'vue'

import { useAppStore, useUserStore } from './store'
import { useRouter, useRoute } from 'vue-router'
import { getSysInfo } from '@/api/commonApi/vis'
import { getImgFilePath } from '@/utils/proto'

import usePermission from './hooks/permission'
import useResponsive from './hooks/responsive'
import useVisibility from './hooks/visibility'
import PageLayout from './page-layout.vue'

useVisibility()
useResponsive(true)
const route = useRoute()
const router = useRouter()

const appStore = useAppStore()
const userStore = useUserStore()

const isInit = ref(false)
const navbarHeight = `60px`
const drawerVisible = ref(false)
const permission = usePermission()
const systemName = ref('一站式智慧调解管理平台')

// const footer = computed(() => appStore.footer)
const navbar = computed(() => appStore.navbar)
const hideMenu = computed(() => appStore.hideMenu)
const collapsed = computed(() => appStore.menuCollapse)
const renderMenu = computed(() => appStore.menu && !appStore.topMenu)
const menuWidth = computed(() => (appStore.menuCollapse ? 48 : appStore.menuWidth))
// const breadcrumbRoute = computed(() => route.matched.map((ref) => ref.meta.title || ''))

const paddingStyle = computed(() => {
  const paddingLeft = renderMenu.value && !hideMenu.value ? { paddingLeft: `${menuWidth.value}px` } : {}
  const paddingTop = navbar.value ? { paddingTop: navbarHeight } : {}
  return { ...paddingLeft, ...paddingTop }
})

const setCollapsed = (val: boolean) => {
  if (isInit.value) appStore.updateSettings({ menuCollapse: val })
}

const getSysNameInfo = () => {
  if (appStore.loginPageConfig) {
    const { navigationName, tabName, tabPath } = appStore.loginPageConfig
    systemName.value = navigationName
    document.title = tabName
    // 重新设置favicon图标
    if (tabPath) {
      const link = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
      link.href = getImgFilePath(tabPath)
    }
  } else {
    getSysInfo().then(({ sctName }) => {
      systemName.value = sctName
      document.title = sctName
    })
  }
}

const drawerCancel = () => {
  drawerVisible.value = false
}

provide('toggleDrawerMenu', () => {
  drawerVisible.value = !drawerVisible.value
})

watch(
  () => userStore.userInfo.roleNames,
  (roleValue) => {
    if (roleValue && !permission.accessRouter(route)) router.push({ name: '404' })
  }
)

onMounted(() => {
  getSysNameInfo()
  // 初始化判定
  isInit.value = true
})
</script>

<style scoped lang="scss">
$layout-max-width: 1100px;
$nav-size-height: 60px;

.layout {
  width: 100%;
  height: 100%;
}

.layout-navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: $nav-size-height;
}

.layout-sider {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  height: 100%;
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    display: block;
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
    content: '';
  }

  > :deep(.arco-layout-sider-children) {
    overflow-y: hidden;
  }
}

.menu-wrapper {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  :deep(.arco-menu) {
    ::-webkit-scrollbar {
      width: 12px;
      height: 4px;
    }

    ::-webkit-scrollbar-thumb {
      border: 4px solid transparent;
      background-clip: padding-box;
      border-radius: 7px;
      background-color: var(--color-text-4);
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-text-3);
    }
  }
}

.page-content {
  height: 100%;
  overflow-y: scroll;
  background: var(--color-bg-1);
  transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.page-layout-content {
  padding: 5px 20px;
  background: var(--color-neutral-2);
}
</style>
