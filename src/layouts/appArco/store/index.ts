import useTabBarStore from './modules/tab-bar'
import useStructStore from './modules/struct'
import useDictStore from './modules/dict'
import useTcccStore from './modules/tccc'
import useUserStore from './modules/user'
import useAppStore from './modules/app'


import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { createPinia } from 'pinia'

const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)

export { useAppStore, useTcccStore, useUserStore, useTabBarStore, useStructStore,useDictStore}

export default pinia
