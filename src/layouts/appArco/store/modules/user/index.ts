import type { ACCOUNT_API_USERINFO_TYPE } from '@/api/commonApi/account/types/account'
import { login as userLogin, LoginData } from '@arco/api/user'
import { removeRouteListener } from '@arco/utils/route-listener'
import { getUserInfo } from '@/api/commonApi/account/index'
import { setToken, clearToken } from '@arco/utils/auth'
import { getSystemParam } from '@/api/commonApi/config'
import { remove } from '@arco/utils/tccc'
import { defineStore } from 'pinia'
import router from '@arco/router'

import useTabBarStore from '../tab-bar'
import useAppStore from '../app'
import { useDictStore } from '@arco/store'
import type { RELEVANCE_USER_INFO } from '@/api/arcoApi/systemManage/types/relevanceUserInfo'

const useUserStore = defineStore('user', {
  state: (): ACCOUNT_API_USERINFO_TYPE => ({
    permissions: [],
    userInfo: {
      pwdUpdateTime: null,
      landLineNumber: '',
      employeeMobile: '',
      employeeEmail: '',
      employeeName: '',
      companyName: '',
      companyType: '',
      isFirstLogin: 0,
      employeeId: '',
      agentFlag: '0',
      loginName: '',
      roleNames: '',
      accountId: '',
      copyFlag: '',
      deptName: ''
    },
		setting: {
			aiSwitch:false,
			settingId: 0,
			companyId: 0
		},
		relevanceUserInfoList: [] as RELEVANCE_USER_INFO[]
  }),

  getters: {
    getUserInfo(state: ACCOUNT_API_USERINFO_TYPE): ACCOUNT_API_USERINFO_TYPE {
      return { ...state }
    },
		getUserRoleList(state: ACCOUNT_API_USERINFO_TYPE):string[]{
			if(!state.userInfo){
				return []
			}
			if(!state.userInfo.roleNames){
				return []
			}
			return state.userInfo.roleNames.split(',')
		}
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.userInfo.roleNames = this.userInfo.roleNames === 'user' ? 'admin' : 'user'
        resolve(this.userInfo.roleNames)
      })
    },
    setInfo(partial: Partial<ACCOUNT_API_USERINFO_TYPE>) {
      this.$patch(partial)
    },
    resetInfo() {
      remove()

      const tabBarStore = useTabBarStore()
      const appStore = useAppStore()
      clearToken()

      this.$reset()
      appStore.$reset()
      tabBarStore.$reset()
      removeRouteListener()
      // 清理本地登录名缓存
      localStorage.removeItem('loginName')
      //清理自定义查询的表单缓存
      sessionStorage.removeItem(`caseCustomSearchFormSession`)
    },
    async info() {
      // 获取用户信息
      const userInfo = await getUserInfo()
      this.setInfo(userInfo)
      // 获取电话脱敏信息
      const sysInfo = await getSystemParam()
      if (sysInfo) {
        const appStore = useAppStore()
        appStore.setSystemParam(sysInfo)
      }
			//初始化字典数据
			await useDictStore().initializeDictData()
    },

    // Login
    async login(loginForm: LoginData) {
      try {
        const res = await userLogin(loginForm)
        setToken(res.data.token)
      } catch (err) {
        clearToken()
        throw err
      }
    },

    backLogin() {
      const { query, name, params } = router.currentRoute.value
      const courtName = localStorage.getItem('court')
      if (courtName) {
        router.replace({ name: `courtLogin`, params: { ...params, court: courtName } })
      } else {
        router.replace({ name: 'login', query: { ...query, redirect: name as string } })
      }
    },

    logoutCallBack(path?: string) {
      this.resetInfo()
      const { query, name } = router.currentRoute.value
      query['redirect'] = name as string
      path ? router.push({ name: path, query }) : this.backLogin()
    },
    // Logout
    async logout(path?: string) {
      try {
        // 调用退出接口
        // await userLogout()
      } finally {
        this.logoutCallBack(path)
      }
    }
  }
})

export default useUserStore
