import { listAllTreeData } from '@/api/arcoApi/systemManage/dictManage'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { DictTypeEnum } from '@/dict/systemManage.ts'

const useDictStore = defineStore('dict', () => {
	// 缓存所有字典数据
	const allDictData = ref<REQUEST_GET_DICT_LIST_TYPE[]>([])

	//构建一个map，存储type和字典list
	const dictMap = ref<Record<string, REQUEST_GET_DICT_VALUE_LIST_TYPE[]>>({})

	// 初始化字典数据
	const initializeDictData = async () => {
		try {
			allDictData.value = await listAllTreeData()
			allDictData.value.forEach(dict => {
				dictMap.value[dict.dictType] = dict.sysDictDataList
			})
			console.log(dictMap.value)
		} catch (error) {
			console.error('Failed to initialize dictionary data:', error)
		}
	}

	// 根据 dictType 获取字典数据
	const listTreeDictDataByType = (dictType: DictTypeEnum): REQUEST_GET_DICT_VALUE_LIST_TYPE[] => {
		if (allDictData.value.length === 0) {
			console.warn('Dictionary data is not initialized. Please call initializeDictData first.')
			return []
		}
		if (!(dictType in dictMap.value)) {
			console.warn(`Dictionary data for type ${dictType} not found`)
			return []
		}

		return dictMap.value[dictType] || []
	}




	//根据dictType 和dict value获取字典tag
	const getDictTagByTypeAndKey = (dictType: DictTypeEnum, key: number): string => {
		let item = getDictDataByTypeAndKey(dictType, key)
		if(!item){
			return ''
		}
		return item.dictTag;
	}

	//根据dictType 和dict value获取字典Reamrk
	const getDictRemarkByTypeAndKey = (dictType: DictTypeEnum, key: number): string => {
		let item = getDictDataByTypeAndKey(dictType, key)
		if(!item){
			return ''
		}
		return item.remark;
	}


	//根据dictType 和dict value获取字典tag
	const getDictDataByTypeAndKey = (dictType: DictTypeEnum, key: number): REQUEST_GET_DICT_VALUE_LIST_TYPE|null => {
		let list: REQUEST_GET_DICT_VALUE_LIST_TYPE[] = listTreeDictDataByType(dictType)
		let item = findDictDataByTreeDataList(list, key)
		return item;
	}

	const findDictDataByTreeDataList = (list: REQUEST_GET_DICT_VALUE_LIST_TYPE[], key: number): REQUEST_GET_DICT_VALUE_LIST_TYPE|null => {
		for (const item of list) {
			if(item.dictKey == key){
				return item;
			}
			if(item.children && item.children.length > 0){
				let findItem = findDictDataByTreeDataList(item.children, key)
				if(findItem){
					return findItem;
				}
			}
		}
		return null
	}

	//获取该节点所有的子孙节点
	const getOffspringKeys = (dictType: DictTypeEnum, key: number): number[] => {
		let list: REQUEST_GET_DICT_VALUE_LIST_TYPE[] = listTreeDictDataByType(dictType)
		const path = findPathByTreeDataList(list, key);

		if (path.length === 0) {
			return [];
		}

		let thisItem = path[path.length - 1];
		let result: number[] = [key];

		findAllChildrenKeys(thisItem.children,result);

		return result;
	}

	const findAllChildrenKeys = (list: REQUEST_GET_DICT_VALUE_LIST_TYPE[],result: number[]) =>{
		if(!list || list.length === 0){
			return;
		}
		list.forEach(item=>{
			result.push(item.dictKey);
			findAllChildrenKeys(item.children,result);
		})
	}

	// 修改后的 findTagByTreeDataList 方法
	const findPathByTreeDataList = (list: REQUEST_GET_DICT_VALUE_LIST_TYPE[], key: number): REQUEST_GET_DICT_VALUE_LIST_TYPE[] => {
		for (const item of list) {
			if (item.dictKey == key) {
				return [item]; // 找到目标节点，返回包含自身的数组
			}

			if (item.children && item.children.length > 0) {
				const path = findPathByTreeDataList(item.children, key);
				if (path.length > 0) {
					// 在路径数组前面添加当前父节点
					return [item, ...path];
				}
			}
		}
		return []; // 未找到目标节点
	}


	return {
		initializeDictData,
		listTreeDictDataByType,
		allDictData,
		dictMap,
		getDictTagByTypeAndKey,
		getDictRemarkByTypeAndKey,
		getOffspringKeys
	}
})


export default useDictStore
