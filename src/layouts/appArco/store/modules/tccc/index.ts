import { TcccState } from './types'
import { defineStore } from 'pinia'

const useTcccStore = defineStore('tccc', {
  state: (): TcccState => ({
    caseEventParam: {
      fromCaseDetails: false,
      calleeName: '',
      caseId: '',
			litigantIdList: []
    }
  }),

  getters: {
    getCaseEventParam(): TcccState['caseEventParam'] {
      return this.caseEventParam
    }
  },

  actions: {
    setTcccState(param: TcccState['caseEventParam']) {
      this.caseEventParam = param
    },
    resetState() {
      // 重置state数据
      this.caseEventParam = {
        fromCaseDetails: false,
        calleeName: '',
        caseId: '',
				litigantIdList: []
      }
    }
  }
})

export default useTcccStore
