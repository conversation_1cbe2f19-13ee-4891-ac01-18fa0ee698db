export interface AppState {
  loginPageConfig: REQUEST_POST_LOGINAPPEARANCE_RESULT_TYPE | null
  pwRegExpConf: { pwRegExp: string; tipText: string }
  systemParam: REQUEST_GET_APP_SYSTEM_PARAM_TYPE
  serverMenu: REQUEST_GET_ROLE_AUTH_DATA_TYPE[]
  globalSettings: boolean
  menuFromServer: boolean
  [key: string]: unknown
  menuCollapse: boolean
  breadcrumb: boolean
  colorWeak: boolean
  themeColor: string
  hideMenu: boolean
  menuWidth: number
  topMenu: boolean
  navbar: boolean
  footer: boolean
  tabBar: boolean
  device: string
  theme: string
  menu: boolean
}
