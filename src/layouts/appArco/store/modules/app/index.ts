import { handlePassWordRegExp } from '@/layouts/appArco/utils/handlePwRegExp'
import { getLoginAppearanceByAppearancePath } from '@/api/commonApi/vis'
import defaultSettings from '@arco/config/settings.json'
import { newListUserMenu } from '@/api/commonApi/nav'
import useTabBarStore from '../tab-bar'
import { defineStore } from 'pinia'
import { AppState } from './types'
import router from '@arco/router'

const useAppStore = defineStore('app', {
  state: (): AppState => ({ ...defaultSettings }),
  getters: {
    appCurrentSetting(state: AppState): AppState {
      return { ...state }
    },
    appDevice(state: AppState) {
      return state.device
    },
    appAsyncMenus(state: AppState) {
      return state.serverMenu
    }
  },

  actions: {
    // Update app settings
    updateSettings(partial: Partial<AppState>) {
      // @ts-ignore-next-line
      this.$patch(partial)
    },

    // Change theme color
    toggleTheme(dark: boolean) {
      if (dark) {
        this.theme = 'dark'
        document.body.setAttribute('arco-theme', 'dark')
      } else {
        this.theme = 'light'
        document.body.removeAttribute('arco-theme')
      }
    },
    toggleDevice(device: string) {
      this.device = device
    },
    toggleMenu(value: boolean) {
      this.hideMenu = value
    },
    setSystemParam(conf: REQUEST_GET_APP_SYSTEM_PARAM_TYPE) {
      for (const key in conf) {
        if (Object.prototype.hasOwnProperty.call(this.systemParam, key)) {
          if (Object.prototype.toString.call(this.systemParam[key]) === '[object Number]') {
            this.systemParam[key] = Number(conf[key])
          } else {
            this.systemParam[key] = conf[key]
          }
        }
      }
      if (conf.isPasswordSetting) {
        this.pwRegExpConf = handlePassWordRegExp(conf.passwordContent, conf.passwordSize)
      } else {
        this.pwRegExpConf = { pwRegExp: '^[\\s\\S]{6,}$', tipText: '请输入最小6位数组合密码' }
      }
    },
    async getCustomLoginPage() {
      const court = localStorage.getItem('court')
      if (court) {
        const res = await getLoginAppearanceByAppearancePath(court)
        if (res && res.appearanceEnableStatus === 1) {
          this.$patch({ loginPageConfig: res })
        }
      }
    },

    async fetchServerMenuConfig() {
      try {
        const { hamMenus } = await newListUserMenu()
        if (hamMenus && hamMenus.length) {
          const tabBarStore = useTabBarStore()
          const allRoute = router.getRoutes()
          // 处理汉堡菜单-平铺数据
          const handleMapHamMenus = (data: typeof hamMenus, menus: typeof hamMenus) => {
            menus.forEach((menuItem) => {
              // 过滤-只获取列表页面
              if (menuItem.menuType === 1) {
                data.push({ ...menuItem, children: [] })
              }
              if (menuItem.children && menuItem.children.length) {
                handleMapHamMenus(data, menuItem.children)
              }
            })
          }
          let currentRoute: typeof hamMenus = []
          handleMapHamMenus(currentRoute, hamMenus)
          // 遍历列表页面父级ID
          let pIds = currentRoute.map((route) => route.parentMenuId)
          // 获取首个列表页面（非操作、非第三方）
          const filterRoutes = currentRoute.filter((cm) => !pIds.includes(cm.menuId))
          const targetRoute = allRoute.find((route) => route.name === filterRoutes[0].name)
          if (targetRoute) {
            let { meta, name, path } = targetRoute
            tabBarStore.initTabList({ title: meta.title || '', name: name as string, fullPath: path })
          }
          this.serverMenu = hamMenus
        }
      } catch (error) {}
    },
    clearServerMenu() {
      this.serverMenu = []
    }
  },
  persist: { key: 'appState', paths: ['serverMenu'] }
})

export default useAppStore
