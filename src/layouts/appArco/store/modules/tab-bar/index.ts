import type { RouteLocationNormalized } from 'vue-router'
import { DEFAULT_ROUTE } from '@arco/router/constants'
import { TabBarState, TagProps } from './types'
import { defineStore } from 'pinia'

const formatTag = (route: RouteLocationNormalized): TagProps => {
  const { name, meta, fullPath, query, params } = route
  let multipageId = meta.multipage ? params[meta.multipage] || query[meta.multipage] : ''
  return {
    multipageId: multipageId ? multipageId as string : '',
    keepAlive: meta.keepAlive,
    name: name as string,
    title: meta.title,
    fullPath,
    params,
    query
  }
}

const useTabBarStore = defineStore('tabBar', {
  state: (): TabBarState => ({
    baseRoute: DEFAULT_ROUTE,
    cacheTabList: [],
    tagList: []
  }),

  getters: {
    getTabList(): TagProps[] {
      return this.tagList
    },
    getCacheList(): string[] {
      return Array.from(this.cacheTabList)
    }
  },

  actions: {
    initTabList(route: TagProps) {
      if (route) {
        this.cacheTabList.push(route.name as string)
        this.baseRoute = route
        this.tagList = [route]
      }
    },
    addTabList(route: RouteLocationNormalized) {
      if (this.baseRoute.name === route.name) return

      this.tagList.push(formatTag(route))
      if (!route.meta.keepAlive) {
        this.cacheTabList.push(route.name as string)
      }
    },
    updateTagParams(name: string, params: any) {
      if (name) {
        let currentRoute = this.tagList.find((routeItem) => routeItem.name === name)
        if (currentRoute) {
          currentRoute.params = params
        }
      }
    },
    updateTagFieldData(fullPath: string, field: string, data: any) {
      if (fullPath) {
        let currentRoute = this.tagList.find((routeItem) => routeItem.fullPath === fullPath)
        if (currentRoute && Object.prototype.hasOwnProperty.call(currentRoute, field)) {
          currentRoute[field] = data
        }
      }
    },
    deleteTag(idx: number, tag: TagProps) {
      this.tagList.splice(idx, 1)
      let tagInx = this.cacheTabList.findIndex((tagName) => tagName === tag.name)
      if (tagInx) this.cacheTabList.splice(tagInx, 1)
    },
    addCache(name: string) {
      if (name) this.cacheTabList.push(name)
    },
    deleteCache(tag: TagProps) {
      let tagInx = this.cacheTabList.findIndex((tagName) => tagName === tag.name)
      if (tagInx) this.cacheTabList.splice(tagInx, 1)
    },
    freshTabList(tags: TagProps[]) {
      this.tagList = tags
      this.cacheTabList = []
      // 要先判断keepAlive
      this.tagList
        .filter((el) => !el.keepAlive)
        .map((el) => el.name)
        .forEach((name) => this.cacheTabList.push(name))
    },
    resetTabList() {
      this.cacheTabList = []
      if (this.baseRoute) {
        this.cacheTabList.push(this.baseRoute.name)
        this.tagList = [this.baseRoute]
      } else {
        this.tagList = []
      }
    }
  },
  persist: { key: 'tabBarState' }
})

export default useTabBarStore
