import {getMediateStructure} from '@/api/eleApi/system/structManage.ts'
import { defineStore } from 'pinia'
import { StructState } from './types'
import type { TreeNodeData } from '@arco-design/web-vue/es/tree/interface'
import useUserStore from '../user'

const useStructStore = defineStore('struct', {
  state: (): StructState => ({
		orgDeptList: undefined,
		orgDeptTree:[]
  }),

  getters: {
    getDeptEmployeeTree(state: StructState): TreeNodeData[] {
      return state.orgDeptTree
    }
  },
  actions: {
    async refreshDeptEmployee() {
			this.orgDeptList = []
			this.orgDeptList = await getMediateStructure();

			const userStore = useUserStore()
			const currentAccountId = userStore.userInfo.accountId

			let orgTree:TreeNodeData[] = [];

			// 用于检查当前用户是否已存在于组织架构中
			let currentUserExists = false

			for (let org of this.orgDeptList) {

				let deptTree:TreeNodeData[] = [];

				for (let dept of org.deptEmployeeDTOList){
					let employeeList:TreeNodeData[] = []
					for (const employee of dept.employeeDTOList) {
						// 检查是否是当前用户
						if (employee.accountId.toString() === currentAccountId) {
							currentUserExists = true
						}

						const items = {
							title:employee.employeeName,
							key:employee.accountId,
							// arco bug，单选tree必须用value才能双向绑定
							value:employee.accountId
						}
						employeeList.push(items)
					}
					deptTree.push({
						selectable:false,
						title:dept.deptName,
						children:employeeList
					})
				}

				orgTree.push({
					selectable:false,
					title:org.companyName,
					children:deptTree
				})

			}

			// 如果当前用户不存在于组织架构中，在最外层添加自己的账号
			if (!currentUserExists && currentAccountId && userStore.userInfo.employeeName) {
				orgTree.unshift({
					title: userStore.userInfo.employeeName,
					key: currentAccountId,
					value: currentAccountId
				})
			}

			this.orgDeptTree = orgTree;
    },
		async refreshDeptEmployeeoOlyNull() {
			if (!this.orgDeptList){
				await this.refreshDeptEmployee()
			}
		}
  }
})

export default useStructStore
