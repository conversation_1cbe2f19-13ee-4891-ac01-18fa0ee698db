interface PASSWORD_REGEXP_TYPE {
  pwRegExpArr: string[]
  pwRegExp: string
  tipText: string
}

export const handlePassWordRegExp = (content: (string | number)[], size: string | number) => {
  let regExp: PASSWORD_REGEXP_TYPE = { pwRegExpArr: [], pwRegExp: '', tipText: '请输入:' }
  for (let index = 0; index < content.length; index++) {
    const num = Number(content[index])
    switch (num) {
      case 1:
        regExp.pwRegExpArr.push(`A-Z`)
        regExp.tipText += `[大写]`
        break
      case 2:
        regExp.pwRegExpArr.push(`a-z`)
        regExp.tipText += `[小写]`
        break
      case 3:
        regExp.pwRegExpArr.push("~!@#$%^&*()<>{}·_?,.;'`")
        regExp.tipText += `[特殊符号]`
        break
      case 4:
        regExp.pwRegExpArr.push(`0-9`)
        regExp.tipText += `[数字]`
        break
      default:
        break
    }
  }

  if (regExp.pwRegExpArr.length) {
    let newArr1 = regExp.pwRegExpArr.map((s) => `(?=.*[${s}])`)
    let arrStr1 = regExp.pwRegExpArr.join('')
    let arrStr2 = newArr1.join('')
    regExp.pwRegExp = `^${arrStr2}[${arrStr1}]`
    regExp.pwRegExp += `{${size},}$`
  } else {
    regExp.pwRegExp = `^[\\s\\S]{6,}$`
  }
  regExp.tipText += `最小${size}位数组合密码`
  return regExp
}
