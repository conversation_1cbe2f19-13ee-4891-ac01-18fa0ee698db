import { nextTick } from 'vue'

/**
 * @description: 生成loading
 */
export function createLoadingDom(el: HTMLElement, value: boolean) {

  // 判断value是否是布尔值
  if (typeof value !== 'boolean') {
    throw new Error('v-loading value must be a boolean')
  }

  // 查询el下是否已创建global-loading元素
  if (el.querySelector('#global-loading')) {
    // 如果value值为false,移除loading
    if (!value) {
      el.removeChild(el.querySelector('#global-loading') as HTMLElement)
    }
    return
  }

  if (!value) {
    return
  }

  // 创建最外层的 div 元素
  const globalLoadingDiv = document.createElement('div')
  globalLoadingDiv.className = 'global-loading'
  globalLoadingDiv.id = 'global-loading'

  // 创建 loading-wrapper 的 div 元素
  const loadingWrapperDiv = document.createElement('div')
  loadingWrapperDiv.className = 'loading-wrapper'

  // 创建 loading 的 div 元素
  const loadingDiv = document.createElement('div')
  loadingDiv.className = 'loading'

  // 创建内部的 div 元素
  const innerDiv = document.createElement('div')

  // 创建三个 span 元素
  for (let i = 0; i < 3; i++) {
    const span = document.createElement('span')
    innerDiv.appendChild(span)
  }

  // 组装结构
  loadingDiv.appendChild(innerDiv)
  loadingWrapperDiv.appendChild(loadingDiv)

  // 创建 loading-tips 的 div 元素
  const loadingTipsDiv = document.createElement('div')
  loadingTipsDiv.className = 'loading-tips'
  loadingWrapperDiv.appendChild(loadingTipsDiv)

  // 最终组装
  globalLoadingDiv.appendChild(loadingWrapperDiv)

  // 插入到目标节点中
  el.insertBefore(globalLoadingDiv, el.childNodes[0])
}

export class loading {
  show() {
    const bodys: HTMLElement = document.body
    createLoadingDom(bodys, true)
  }
  hide() {
    nextTick(() => {
      setTimeout(() => {
        const el = document.querySelector(`.global-loading`)
        el && el.parentNode?.removeChild(el)
      }, 500)
    })
  }
}