import { tcccDomID } from '@/settings'

const isTccc = () => {
  //根据标签和id查询dom
  const tcccDom = document.getElementById(tcccDomID)
  return !!tcccDom
}

// 获取座席状态
const getStatus = () => {
  return window.tccc.Agent.getStatus()
}

// 检测当前浏览器是否支持 TCCC Web SDK 支持 Chrome 56、Edge 80以上的浏览器。
const isBrowserSupported = (): boolean => {
  return window.tccc.Agent.isBrowserSupported()
}

// 返回麦克风设备列表: Promise<MediaDeviceInfo []>
const getMicrophones = () => {
  return window.tccc.Agent.getMicrophones()
}

// 返回扬声器设备列表: Promise<MediaDeviceInfo []>
const getSpeakers = () => {
  return window.tccc.Agent.getSpeakers()
}

// UI（用户界面相关接口函数）
// 隐藏 SDK 所有 UI
const hide = () => {
  window.tccc.UI.hide()
}

// 显示 SDK 所有 UI
const show = () => {
  window.tccc.UI.show()
}

// 显示浮动按钮
const showfloatButton = () => {
  window.tccc.UI.showfloatButton()
}

// 隐藏浮动按钮
const hidefloatButton = () => {
  window.tccc.UI.hidefloatButton()
}

// 显示工作台
const showWorkbench = () => {
  window.tccc.UI.showWorkbench()
}

// 隐藏工作台
const hideWorkbench = () => {
  window.tccc.UI.hideWorkbench()
}

// 显示通话条
const showNotificationBar = () => {
  window.tccc.UI.showNotificationBar()
}

// 隐藏通话条
const hideNotificationBar = () => {
  window.tccc.UI.hideNotificationBar()
}

// 清除腾讯云sdk
const remove = async () => {
  const tcccSdkDom = document.getElementById('tcccSdkDomRoot')
  const tcccDom = document.getElementById(tcccDomID)
  if (tcccDom && tcccSdkDom) {
    offline()

    tcccDom.removeEventListener('load', () => {})
    tcccSdkDom.remove()
    tcccDom.remove()
    window.tccc = {}
  }
}

// 上线
const online = () => {
  window.tccc.Agent.online()
}

// 下线
const offline = () => {
  window.tccc.Agent.offline()
}

const eventMethod = {
  calloutAccepted: '外呼对方接听事件',
  statusChanged: '座席状态变更事件',
  autoTransfer: '会话超时转接事件',
  sessionEnded: '会话结束事件',
  kickedOut: '座席被踢下线事件',
  userAccessed: '座席接入会话',
  callOuted: '外呼成功事件',
  transfer: '会话转接事件',
  ready: '坐席初始化成功',
  callIn: '会话呼入',
  asr: '语音识别事件'
}

export {
  showNotificationBar,
  hideNotificationBar,
  isBrowserSupported,
  showfloatButton,
  hidefloatButton,
  getMicrophones,
  showWorkbench,
  hideWorkbench,
  eventMethod,
  getSpeakers,
  getStatus,
  offline,
  online,
  isTccc,
  remove,
  hide,
  show
}
