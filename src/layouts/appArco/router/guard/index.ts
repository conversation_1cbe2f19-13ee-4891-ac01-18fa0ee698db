import { setRouteEmitter } from '@arco/utils/route-listener'
import setupUserLoginInfoGuard from './userLoginInfo'
import setupPermissionGuard from './permission'
import type { Router } from 'vue-router'

function setupPageGuard(router: Router) {
  router.beforeEach(async (to) => {
    setRouteEmitter(to)
  })
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router)
  setupUserLoginInfoGuard(router)
  setupPermissionGuard(router)
}
