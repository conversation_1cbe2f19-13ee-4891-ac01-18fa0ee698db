import type { Router, LocationQueryRaw } from 'vue-router'
import NProgress from 'nprogress' // progress bar

import { WHITE_LIST } from '@arco/router/constants'
import { useUserStore, useAppStore } from '@arco/store'
import { isLogin } from '@arco/utils/auth'

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start()
    const userStore = useUserStore()
    const appStore = useAppStore()
    if (isLogin()) {
      if (userStore.userInfo.roleNames) {
        next()
      } else {
        try {
          await appStore.getCustomLoginPage()
          await userStore.info()
          next()
        } catch (error) {
          await userStore.resetInfo()
          next({ name: 'login', query: { redirect: to.name, ...to.query } as LocationQueryRaw })
        }
      }
    } else {
      if (WHITE_LIST.find((el) => el.name === to.name)) {
        next()
        return
      }
      next({ name: 'login', query: { redirect: to.name, ...to.query } as LocationQueryRaw })
    }
  })
}
