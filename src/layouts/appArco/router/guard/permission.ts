import type { Router } from 'vue-router'

import { useUserStore, useAppStore, useTabBarStore } from '@arco/store'
import { WHITE_LIST, NOT_FOUND, LOGINS } from '@arco/router/constants'
import usePermission from '@arco/hooks/permission'
import { appRoutes } from '@arco/router/routes'
import { getToken } from '@/utils/auth'
import NProgress from 'nprogress'

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const tabBarStore = useTabBarStore()
    const Permission = usePermission()
    const userStore = useUserStore()
    const appStore = useAppStore()

    const destination = Permission.findFirstPermissionRoute(appRoutes, userStore.userInfo.roleNames) || NOT_FOUND
    const permissionsAllow = Permission.accessRouter(to)
    const token = getToken()
    if (appStore.menuFromServer && token) {
      // 针对来自服务端的菜单配置进行处理
      // Handle routing configuration from the server

      // 根据需要自行完善来源于服务端的菜单配置的permission逻辑
      // Refine the permission logic from the server's menu configuration as needed
      if (!appStore.serverMenu.length && !WHITE_LIST.find((el) => el.name === to.name)) {
        await appStore.fetchServerMenuConfig()
      }
      let exist = false
      const serverMenuConfig = [...appStore.serverMenu, ...WHITE_LIST]
      while (serverMenuConfig.length && !exist) {
        const element = serverMenuConfig.shift()
        if (element?.name === to.name || WHITE_LIST.find((routeItem) => routeItem.name === to.name)) exist = true
        if (element?.children) {
          serverMenuConfig.push(...element.children)
        }
      }
      if (exist && permissionsAllow) {
        const isToLogin = LOGINS.includes(to.name as string)
        if (isToLogin && appStore.serverMenu.length < 1) {
          userStore.logoutCallBack()
          next()
        } else {
          isToLogin ? next(destination) : next()
        }
      } else {
        if (LOGINS.includes(from.name as string) && to.name === 'desktop') {
          if (tabBarStore.baseRoute.name != 'desktop') next(tabBarStore.baseRoute.fullPath)
          else next(NOT_FOUND)
        } else {
          next(NOT_FOUND)
        }
      }
    } else {
      permissionsAllow ? next() : next(destination)
    }
    NProgress.done()
  })
}
