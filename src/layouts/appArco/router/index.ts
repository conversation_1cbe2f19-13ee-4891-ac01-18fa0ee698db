import { createRouter, createWebHistory } from 'vue-router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'

import { REDIRECT_MAIN, NOT_FOUND_ROUTE, NOT_PERMISSION, NOT_SERVER_ERROR } from './routes/base'
import createRouteGuard from './guard'
import { appRoutes } from './routes'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', redirect: 'login' },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/index.vue'),
      meta: { title: '登录', requiresAuth: false }
    },
    {
      path: '/login/:court',
      name: 'courtLogin',
      component: () => import('@/views/login/index.vue'),
      meta: { title: '登录', requiresAuth: false }
    },
    ...appRoutes,
    REDIRECT_MAIN,
    NOT_PERMISSION,
    NOT_FOUND_ROUTE,
    NOT_SERVER_ERROR
  ],
  scrollBehavior() {
    return { top: 0 }
  }
})

createRouteGuard(router)

export default router
