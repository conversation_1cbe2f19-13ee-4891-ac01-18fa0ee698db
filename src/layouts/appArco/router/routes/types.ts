import type { RouteMeta, NavigationGuard } from 'vue-router'
import { defineComponent } from 'vue'

export type Component<T = any> =
  | ReturnType<typeof defineComponent>
  | (() => Promise<typeof import('*.vue')>)
  | (() => Promise<T>)

export interface AppRouteRecordRaw {
  beforeEnter?: NavigationGuard | NavigationGuard[]
  children?: AppRouteRecordRaw[]
  component: Component | string
  props?: Record<string, any>
  alias?: string | string[]
  name?: string | symbol
  redirect?: string
  fullPath?: string
  meta?: RouteMeta
  path: string
}
