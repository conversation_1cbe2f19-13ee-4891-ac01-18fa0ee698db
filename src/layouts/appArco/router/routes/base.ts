import type { RouteRecordRaw } from 'vue-router'
import { REDIRECT_ROUTE_NAME } from '../constants'

export const DEFAULT_LAYOUT = () => import('@/layouts/appArco/index.vue')

export const REDIRECT_MAIN: RouteRecordRaw = {
  path: '/redirect',
  name: 'redirectWrapper',
  component: DEFAULT_LAYOUT,
  meta: { requiresAuth: true, hideInMenu: true },
  children: [
    {
      path: '/redirect/:path',
      name: REDIRECT_ROUTE_NAME,
      component: () => import('@/views/common/redirect/index.vue'),
      meta: { requiresAuth: true, hideInMenu: true }
    }
  ]
}

export const NOT_PERMISSION: RouteRecordRaw = {
  path: '/403',
  name: '403',
  component: () => import('@/views/common/403/index.vue')
}

export const NOT_SERVER_ERROR: RouteRecordRaw = {
  path: '/500',
  name: '500',
  component: () => import('@/views/common/500/index.vue')
}

export const NOT_FOUND_ROUTE: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  name: '404',
  component: () => import('@/views/common/404/index.vue')
}
