import { AppRouteRecordRaw } from '../types'
import { DEFAULT_LAYOUT } from '../base'

const CASEMANAGE: AppRouteRecordRaw = {
  path: '/case',
  name: 'case',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'mdtmenu.list.case',
    requiresAuth: true,
    icon: 'icon-file',
    title: '案件管理',
    order: 2
  },
  children: [
    {
      path: 'caseManage',
      name: 'caseManage',
      component: () => import('@/views/case/caseManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.caseManage',
        requiresAuth: true,
        title: '案件列表',
        roles: ['*']
      }
    },
    {
      path: 'caseAuditManage',
      name: 'caseAuditManage',
      component: () => import('@/views/case/caseAuditManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.caseAuditManage',
        requiresAuth: true,
        title: '案件审核管理',
        roles: ['*']
      }
    },
    {
      path: 'approvalRecord',
      name: 'approvalRecord',
      component: () => import('@/views/case/approvalRecord/index.vue'),
      meta: {
        locale: 'mdtmenu.list.approvalRecord',
        requiresAuth: true,
        title: '结案审核',
        roles: ['*']
      }
    },
    {
      path: 'caseDetails/:caseId',
      name: 'caseDetails',
      component: () => import('@/views/case/caseDetails/index.vue'),
      meta: {
        locale: 'mdtmenu.list.caseDetails',
        hideInMenu: true,
        title: '案件详情',
        multipage: 'caseNo',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'caseCreate',
      name: 'caseCreate',
      component: () => import('@/views/case/caseCreate/index.vue'),
      meta: {
        locale: 'mdtmenu.list.caseCreate',
        hideInMenu: true,
        title: '创建案件',
        requiresAuth: true,
        roles: ['*']
      }
    }
  ]
}

export default CASEMANAGE
