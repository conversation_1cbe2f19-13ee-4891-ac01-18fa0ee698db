import { AppRouteRecordRaw } from '../types'
import { DEFAULT_LAYOUT } from '../base'

const BUSINSESSCONF: AppRouteRecordRaw = {
  path: '/businsess',
  name: 'businsess',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.result',
    icon: 'icon-check-circle',
    title: '业务管理',
    requiresAuth: true,
    order: 4
  },
  children: [
    {
      path: 'caseSourceInfoManage',
      name: 'caseSourceInfoManage',
      component: () => import('@/views/businessConf/caseSourceInfoManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.caseSourceInfoManage',
        title: '案源方信息管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'mediateOrgInfoManage',
      name: 'mediateOrgInfoManage',
      component: () => import('@/views/businessConf/mediateOrgInfoManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.mediateOrgInfoManage',
        title: '调解组织信息管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'templateManage',
      name: 'templateManage',
      component: '',
      meta: {
        locale: 'mdtmenu.list.templateManage',
        title: '模板管理',
        requiresAuth: true,
        roles: ['*']
      },
      children: [
        // 案件信息模板管理
        {
          component: () => import('@/views/businessConf/templateManage/caseInfoTemplate/list/index.vue'),
          path: 'caseInfoTemplate',
          meta: {
            locale: 'mdtmenu.list.caseInfoTemplate',
            requiresAuth: true,
            title: '案件信息模板管理',
            roles: ['*']
          },
          name: 'caseInfoTemplate'
        },
        {
          component: () => import('@/views/businessConf/templateManage/caseInfoTemplate/create/index.vue'),
          path: 'templateCreate',
          meta: {
            hideInMenu: true,
            locale: 'mdtmenu.list.templateCreate',
            requiresAuth: true,
            title: '创建模板',
            roles: ['*']
          },
          name: 'templateCreate'
        },
        {
          component: () => import('@/views/businessConf/templateManage/caseInfoTemplate/config/index.vue'),
          path: 'templateConf/:tmplId',
          meta: {
            requiresAuth: true,
            locale: 'mdtmenu.list.templateConf',
            multipage: 'tmplId',
            title: '模板配置',
            hideInMenu: true
          },
          name: 'templateConf'
        },
        // 文书模板管理
        {
          component: () => import('@/views/businessConf/templateManage/documentTemplate/list/index.vue'),
          path: 'documentTemplateList',
          meta: {
            locale: 'mdtmenu.list.documentTemplateList',
            requiresAuth: true,
            title: '文书模板管理',
            roles: ['*']
          },
          name: 'documentTemplateList'
        },
        {
          component: () => import('@/views/businessConf/templateManage/documentTemplate/create/index.vue'),
          path: 'documentTemplateCreate',
          meta: {
            hideInMenu: true,
            locale: 'mdtmenu.list.documentTemplateCreate',
            requiresAuth: true,
            title: '创建文书模板',
            roles: ['*']
          },
          name: 'documentTemplateCreate'
        },
        {
          component: () => import('@/views/businessConf/templateManage/documentTemplate/config/index.vue'),
          path: 'documentTemplateConfig/:tmplId',
          meta: {
            requiresAuth: true,
            locale: 'mdtmenu.list.documentTemplateConfig',
            multipage: 'tmplId',
            title: '文书模板配置',
            hideInMenu: true
          },
          name: 'documentTemplateConfig'
        },
        {
          path: 'smsTemplate',
          name: 'smsTemplate',
          component: () => import('@/views/businessConf/templateManage/smsTemplate/index.vue'),
          meta: {
            locale: 'mdtmenu.list.smsTemplate',
            title: '短信模板管理',
            requiresAuth: true,
            roles: ['*']
          }
        }
      ]
    },
		{
			path: 'flowManage',
			name: 'flowManage',
			component: () => import('@/views/businessConf/flowManage/index.vue'),
			meta: {
				locale: 'mdtmenu.list.flowManage',
				title: '任务流程管理',
				requiresAuth: true,
				roles: ['*']
			}
		},
		{
			component: () => import('@/views/businessConf/flowManage/create/index.vue'),
			path: 'workflowCreate',
			meta: {
				hideInMenu: true,
				locale: 'mdtmenu.list.workflowCreate',
				requiresAuth: true,
				title: '创建任务流程',
				roles: ['*']
			},
			name: 'workflowCreate'
		},
		{
			component: () => import('@/views/businessConf/flowManage/config/index.vue'),
			path: 'workflowConfig/:workflowId',
			meta: {
				hideInMenu: true,
				requiresAuth: true,
				locale: 'mdtmenu.list.workflowConfig',
				title: '任务流程配置',
				roles: ['*']
			},
			name: 'workflowConfig'
		},
  ]
}

export default BUSINSESSCONF
