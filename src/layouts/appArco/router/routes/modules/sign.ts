import { AppRouteRecordRaw } from '../types'

const SIGN: AppRouteRecordRaw = {
  path: '/sign',
  name: 'sign',
  redirect: '/sign/desktop',
  component: '',
  meta: {
    locale: 'mdtmenu.list.sign',
    icon: 'icon-sign',
    title: '电子签名',
    order: 1
  },
  children: [
    {
      path: 'customSignCallback',
      component: () => import('@/views/sign/customSignCallback/index.vue'),
      name: 'customSignCallback',
      meta: { title: '签名回调', isTab: true, requiresAuth: true, roles: ['*'] }
    },
    {
      path: 'customSign',
      component: () => import('@/views/sign/customSign/index.vue'),
      name: 'customSign',
      meta: { title: '签名确认', isTab: true, requiresAuth: true, roles: ['*'] }
    }
  ]
}

export default SIGN
