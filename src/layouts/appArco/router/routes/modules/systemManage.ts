import { AppRouteRecordRaw } from '../types'
import { DEFAULT_LAYOUT } from '../base'

const SYSTEMMANAGE: AppRouteRecordRaw = {
  path: '/system',
  name: 'system',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'mdtmenu.list.system',
    icon: 'icon-user',
    requiresAuth: true,
    title: '系统管理',
    order: 6
  },
  children: [
    {
      path: 'employeeAndDepartment',
      name: 'employeeAndDepartment',
      component: () => import('@/views/system/employeeAndDepartment/index.vue'),
      meta: {
        locale: 'mdtmenu.list.employeeAndDepartment',
        title: '成员和部门管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'roleManage',
      name: 'roleManage',
      component: () => import('@/views/system/roleManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.roleManage',
        requiresAuth: true,
        title: '角色管理',
        roles: ['*']
      }
    },
    {
      path: 'dictManage',
      name: 'dictManage',
      component: () => import('@/views/system/dictManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.dictManage',
        title: '系统字典管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'businessTypeManage',
      name: 'businessTypeManage',
      component: () => import('@/views/system/businessTypeManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.dictManage',
        title: '业务类型管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'seatManage',
      name: 'seatManage',
      component: () => import('@/views/system/seatManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.seatManage',
        title: '坐席管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'loginPageManage',
      name: 'loginPageManage',
      component: () => import('@/views/system/loginPageManage/index.vue'),
      meta: {
        locale: 'mdtmenu.list.loginPageManage',
        title: '登录页管理',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'loginPageConf',
      name: 'loginPageConf',
      component: () => import('@/views/system/loginPageConf/index.vue'),
      meta: {
        locale: 'mdtmenu.list.loginPageConf',
        title: '登录页配置',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'systemConf',
      name: 'systemConf',
      component: () => import('@/views/system/systemConf/index.vue'),
      meta: {
        locale: 'mdtmenu.config.systemConf',
        title: '系统配置',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'operationLog',
      name: 'operationLog',
      component: () => import('@/views/system/operationLog/index.vue'),
      meta: {
        locale: 'mdtmenu.list.operationLog',
        title: '操作日志',
        requiresAuth: true,
        roles: ['*']
      }
    },
    {
      path: 'loginLog',
      name: 'loginLog',
      component: () => import('@/views/system/loginLog/index.vue'),
      meta: {
        locale: 'mdtmenu.list.loginLog',
        title: '登录日志',
        requiresAuth: true,
        roles: ['*']
      }
    }
  ]
}

export default SYSTEMMANAGE
