import { AppRouteRecordRaw } from '../types'
import { DEFAULT_LAYOUT } from '../base'

const DASHBOARD: AppRouteRecordRaw = {
  path: '/dashboard',
  name: 'dashboard',
  redirect: '/dashboard/desktop',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'mdtmenu.list.dashboard',
    // hideChildrenInMenu: true,
    icon: 'icon-dashboard',
    requiresAuth: true,
    title: '工作台',
    order: 1
  },
  children: [
    {
      path: 'desktop',
      component: () => import('@/views/dashboard/index.vue'),
      name: 'desktop',
      meta: { title: '工作看板', isTab: true, requiresAuth: true, roles: ['*'] }
    }
  ]
}

export default DASHBOARD
