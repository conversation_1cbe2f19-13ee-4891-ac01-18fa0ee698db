import { AppRouteRecordRaw } from '../types'
import { DEFAULT_LAYOUT } from '../base'

const MEDIATEMANAGE: AppRouteRecordRaw = {
  path: '/mediateManage',
  name: 'mediateManage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'mdtmenu.list.mediateManage',
    requiresAuth: true,
    icon: 'icon-file',
    title: '调解管理',
    order: 3
  },
  children: [
    {
      path: 'jobLog',
      name: 'jobLog',
      component: '',
      meta: {
        locale: 'mdtmenu.list.jobLog',
        requiresAuth: true,
        title: '作业日志',
        roles: ['*']
      },
      children: [
        {
          path: 'callRecord',
          name: 'callRecord',
          component: () => import('@/views/mediateManage/jobLog/callRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.callRecord',
            requiresAuth: true,
            title: '呼叫记录',
            roles: ['*']
          }
        },
        {
          path: 'smsSendRecord',
          name: 'smsSendRecord',
          component: () => import('@/views/mediateManage/jobLog/smsSendRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.smsSendRecord',
            requiresAuth: true,
            title: '短信记录',
            roles: ['*']
          }
        },
        {
          path: 'callRepairRecord',
          name: 'callRepairRecord',
          component: () => import('@/views/mediateManage/jobLog/callRepairRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.callRepairRecord',
            requiresAuth: true,
            title: '失联修复记录',
            roles: ['*']
          }
        },
        {
          path: 'caseReceptionRecord',
          name: 'caseReceptionRecord',
          component: () => import('@/views/mediateManage/jobLog/caseReceptionRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.caseReceptionRecord',
            requiresAuth: true,
            title: '案件接收记录',
            roles: ['*']
          }
        },
        {
          path: 'caseAllocationRecord',
          name: 'caseAllocationRecord',
          component: () => import('@/views/mediateManage/jobLog/caseAllocationRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.caseAllocationRecord',
            requiresAuth: true,
            title: '案件分派记录',
            roles: ['*']
          }
        },
        {
          path: 'numberDetectRecord',
          name: 'numberDetectRecord',
          component: () => import('@/views/mediateManage/jobLog/numberDetectRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.numberDetectRecord',
            requiresAuth: true,
            title: '号码检测记录',
            roles: ['*']
          }
        },
        {
          path: 'videoMediateRecord',
          name: 'videoMediateRecord',
          component: () => import('@/views/mediateManage/jobLog/videoMediateRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.videoMediateRecord',
            requiresAuth: true,
            title: '视频调解记录',
            roles: ['*']
          }
        },
				{
          path: 'caseImportRecord',
          name: 'caseImportRecord',
          component: () => import('@/views/mediateManage/jobLog/caseImportRecord/index.vue'),
          meta: {
            locale: 'mdtmenu.list.caseImportRecord',
            requiresAuth: true,
            title: '案件导入记录',
            roles: ['*']
          }
        }
      ]
    },
    {
      path: 'jobAnalysis',
      name: 'jobAnalysis',
      component: '',
      meta: {
        locale: 'mdtmenu.list.jobAnalysis',
        requiresAuth: true,
        title: '作业分析',
        roles: ['*']
      },
      children: [
        {
          path: 'callStatisticsReport',
          name: 'callStatisticsReport',
          component: () => import('@/views/mediateManage/jobAnalysis/callStatisticsReport/index.vue'),
          meta: {
            locale: 'mdtmenu.list.callStatisticsReport',
            requiresAuth: true,
            title: '呼叫统计报表',
            roles: ['*']
          }
        },
        {
          path: 'mediateJobReport',
          name: 'mediateJobReport',
          component: () => import('@/views/mediateManage/jobAnalysis/mediateJobReport/index.vue'),
          meta: {
            locale: 'mdtmenu.list.mediateJobReport',
            requiresAuth: true,
            title: '案件分析报表',
            roles: ['*']
          }
        }]
    }
  ]
}

export default MEDIATEMANAGE
