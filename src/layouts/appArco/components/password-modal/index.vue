<template>
  <a-modal
    ref="modalRef"
    v-model:visible="visible"
    :width="width"
    :title="title"
    :footer="false"
    :mask-closable="false"
    :closable="false"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="container">
      <div class="content">
        <a-form ref="formRef" class="form" :model="formData" :rules="formRules">
          <a-list :bordered="false">
            <a-list-item>
              <a-form-item label="当前密码" field="oldLoginPwd">
                <a-input-password v-model="formData.oldLoginPwd" placeholder="请输入当前密码"></a-input-password>
              </a-form-item>
            </a-list-item>
            <a-list-item>
              <a-form-item label="新的密码" field="newLoginPwd">
                <a-input-password v-model="formData.newLoginPwd" placeholder="请输入新的密码"></a-input-password>
              </a-form-item>
            </a-list-item>
            <a-list-item>
              <a-form-item label="确认密码" field="confirmPassword">
                <a-input-password v-model="formData.confirmPassword" placeholder="请输入"></a-input-password>
              </a-form-item>
            </a-list-item>
            <a-list-item>
              <div class="security-opreation">
                <a-space>
                  <a-button type="secondary" @click="reset"> 重置 </a-button>
                  <a-button :loading="loading" type="primary" @click="validate"> 保存 </a-button>
                </a-space>
              </div>
            </a-list-item>
          </a-list>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { resetPwd } from '@/api/commonApi/account/index'
import { useAppStore, useUserStore } from '@arco/store'
import { Message } from '@arco-design/web-vue'
import { ref, computed } from 'vue'

interface Props {
  width?: string | number
  title?: string
}

withDefaults(defineProps<Props>(), {
  width: '500px',
  title: ''
})

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const userStore = useUserStore()
const appStore = useAppStore()

const formRef = ref<FormInstance>()
const loading = ref(false)
const formData = ref({
  confirmPassword: '',
  oldLoginPwd: '',
  newLoginPwd: ''
})

const pwValidatRegExp = computed(() => new RegExp(appStore.pwRegExpConf.pwRegExp))
const pwValidatRegTip = computed(() => appStore.pwRegExpConf.tipText)

const confirmPassworddValid = (value: string, cb: Function) => {
  if (formData.value.newLoginPwd) {
    if (value === formData.value.newLoginPwd) cb()
    else cb('两次输入的密码不一致')
  } else cb()
}

const newLoginPwdValid = (value: string, cb: Function) => {
  if (formData.value.confirmPassword) {
    if (value === formData.value.confirmPassword) cb()
    else cb('两次输入的密码不一致')
  } else cb()
}

const validatePwd = (value: string, cb: Function) => {
  if (!pwValidatRegExp.value.test(value)) cb(pwValidatRegTip.value)
  else cb()
}

const formRules = ref({
  oldLoginPwd: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
  newLoginPwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { validator: newLoginPwdValid, trigger: 'blur' },
    { validator: validatePwd, trigger: 'change' }
  ],
  confirmPassword: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { validator: confirmPassworddValid, trigger: 'blur' }
  ]
})
const reset = async () => {
  await formRef.value?.resetFields()
}

const validate = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    loading.value = true
    resetPwd(formData.value)
      .then(() => {
        Message.success('密码修改成功，请重新登录！')
        visible.value = false
        userStore.logout()
      })
      .finally(() => {
        loading.value = false
      })
  }
}
</script>

<style scoped lang="scss">
.security-opreation {
  width: 100%;
  text-align: center;
}
</style>
