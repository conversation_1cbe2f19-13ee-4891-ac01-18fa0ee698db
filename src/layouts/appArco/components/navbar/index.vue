<template>
  <div class="navbar">
    <div class="left-side">
      <a-space>
        <img alt="logo" class="xc_logo" :src="sysLogo" />
        <a-typography-title
          :style="{
            margin: 0,
            fontSize: '20px',
            maxWidth: '280px',
            fontWeight: '600',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden'
          }"
          class="elli"
          :heading="5"
        >
          {{ sysName }}
        </a-typography-title>
        <icon-menu-fold
          v-if="!topMenu && appStore.device === 'mobile'"
          style="font-size: 22px; cursor: pointer"
          @click="toggleDrawerMenu"
        />
      </a-space>
    </div>
    <div class="center-side">
      <Menu v-if="topMenu" />
    </div>
    <ul class="right-side">
      <li>
        <a-tooltip :content="theme === 'light' ? '点击切换为暗黑模式' : '点击切换为亮色模式'">
          <a-button class="nav-btn" type="outline" :shape="'circle'" @click="handleToggleTheme">
            <template #icon>
              <icon-moon-fill v-if="theme === 'dark'" />
              <icon-sun-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>

        <a-popover
          trigger="click"
          :content-style="{ marginRight: '20px', width: '400px'}"
          content-class="message-popover"
        >
					<a-tooltip content="消息通知">
						<div class="message-box-trigger">
							<a-badge :count="unreadMessageCount">
								<a-button class="nav-btn" type="outline" :shape="'circle'" >
									<icon-notification />
								</a-button>
							</a-badge>
						</div>
					</a-tooltip>
          <template #content>
            <message-box @countUnRead="fetchUnread" />
          </template>
        </a-popover>
      </li>
      <li>
        <a-tooltip :content="isFullscreen ? '点击退出全屏模式' : '点击切换全屏模式'">
          <a-button class="nav-btn" type="outline" :shape="'circle'" @click="toggleFullScreen">
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-dropdown trigger="click">
          <a-space  justify="center" style="cursor: pointer">
						<a-avatar :size="32">
							<img alt="avatar" :src="userImg" />
						</a-avatar>
						<span>{{userStore.userInfo.employeeName}}</span>
					</a-space>
          <template #content>
            <a-doption>
              <a-space @click="() => (visible = true)">
                <icon-user />
                <span> 用户中心 </span>
              </a-space>
            </a-doption>
						<a-dsubmenu class="user-change-account" trigger="hover" v-if="showChangeAccount">
							<template #default>
								<icon-user />
								<span> &nbsp;切换账号 </span>
							</template>
							<template #content>
								<div class="user-change-container-title">选择切换账户</div>
								<a-doption class="user-change-list" style="max-width: 380px;min-width:320px;padding: 7px 14px;display:flow" v-for="item of relevanceUserInfoList" :key="item.userInfoDTO.accountId">
									<div class="user-change-container" @click="changeAccount(item)">
										<div class="user-change-container-left">
											<img style="width: 35px;height: 35px;" :src="userImg" />
										</div>
										<div class="user-change-container-right">
											<span><strong>{{item.userInfoDTO.employeeName}}</strong> &nbsp;&nbsp; <span :title="item.roleNames">{{item.roleNames}}</span></span>
											<span>账号归属：{{item.userInfoDTO.companyRemark}}</span>
											<span>{{item.userInfoDTO.companyName}} {{item.deptName}}</span>
										</div>
									</div>
								</a-doption>
							</template>
						</a-dsubmenu>
            <a-doption>
              <a-space @click="handleLogout">
                <icon-export />
                <span> 登出登录 </span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
    <user-center ref="usercenterRef" v-model:visible="visible" title="用户中心"></user-center>
    <password-modal ref="passwordRef" v-model:visible="passwordVisible" title="密码修改"></password-modal>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import PasswordModal from '@arco/components/password-modal/index.vue'
import UserCenter from '@arco/components/user-center/index.vue'
import MessageBox from '@arco/components/message-box/index.vue'
import Menu from '@arco/components/menu/index.vue'
import { queryUnreadCount } from '@arco/api/message'
import { replaceToken } from '@/api/commonApi/account/changeAccount'
import { computed, ref, inject, onMounted, onBeforeUnmount } from 'vue'
import { useDark, useToggle, useFullscreen } from '@vueuse/core'
import { Modal, ModalReturn } from '@arco-design/web-vue'
import defaultLogoPath from '@/assets/images/xc_logo.png'
import defaultUserImg from '@/assets/images/no-face.jpg'
import { useAppStore, useUserStore} from '@arco/store'
import { getImgFilePath } from '@/utils/proto'
import useTccc from '@arco/hooks/tccc'
import useUser from '@arco/hooks/user'
import dayjs from 'dayjs'
import { setLocalStorage, setToken } from '@/utils/auth'
import { setUserInfoMitter } from '@arco/utils/userinfo-listener.ts'
import useTabBarStore from '../../store/modules/tab-bar'
interface Props {
  sysName: string
}

withDefaults(defineProps<Props>(), { sysName: '' })

const { isFullscreen, toggle: toggleFullScreen } = useFullscreen()
const userStore = useUserStore()
const appStore = useAppStore()
const router = useRouter();
const tabBarStore = useTabBarStore();
const { initTccc } = useTccc()
const { logout } = useUser()

const passwordTip = ref<ModalReturn>()
const passwordVisible = ref(false)
const visible = ref(false)
const refBtn = ref()
const relevanceUserInfoList = computed(()=>userStore.relevanceUserInfoList)
const userImg = computed(() => userStore.userInfo.userImg || defaultUserImg)
const agentFlag = computed(() => userStore.userInfo.agentFlag || '0')
const topMenu = computed(() => appStore.topMenu && appStore.menu)
const theme = computed(() => appStore.theme)
const unreadMessageCount = ref(0);
let pollingTimer: ReturnType<typeof setInterval> | null = null;

const showChangeAccount = computed(()=>{
	return userStore.relevanceUserInfoList.length!= null && userStore.relevanceUserInfoList.length>1;
})

const fetchUnread = async () => {
  try {
    const count = await queryUnreadCount();
    unreadMessageCount.value = count;
  } catch (err) {
    console.error('获取未读消息失败:', err);
  }
};

/**
 * 切换账户
 * @param item
 */
function changeAccount(item) {
	replaceToken(item.userInfoDTO.employeeId).then((token : any)=>{
		setToken(token);
    userStore.info().then(()=>{
      // 缓存用户信息
      setLocalStorage('loginName', userStore.userInfo.loginName)
      setUserInfoMitter(userStore.userInfo.loginName);
      // 重置tab页
      tabBarStore.resetTabList();
      // 请求服务端菜单权限
      appStore.fetchServerMenuConfig();
      // 跳转到工作看板页面，并强制刷新
      router.push("/dashboard/desktop").then(()=>{
        router.replace({
          path: '/dashboard/desktop',
          query: { _t: Date.now() } // 添加时间戳参数
        });
      });
    });
	})
}

const startPolling = () => {
  fetchUnread();

  pollingTimer = setInterval(() => {
    fetchUnread();
  }, 300000);
};

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

const sysLogo = computed(() => {
  const { loginPageConfig } = appStore
  if (loginPageConfig && loginPageConfig.navigationPath) {
    return getImgFilePath(loginPageConfig.navigationPath)
  }
  return defaultLogoPath
})

const needUpdatePassWord = computed(() => {
  const { regularPasswordUpdate, updateCycle } = appStore.systemParam
  const { pwdUpdateTime } = userStore.userInfo
  if (pwdUpdateTime && regularPasswordUpdate) {
    let diffrence = dayjs().diff(dayjs(pwdUpdateTime))
    return diffrence > 60 * 60 * 24 * 1000 * updateCycle
  }
  return false
})

const firstLoginPassWordUpdate = computed(() => {
  const { passwordModifyFirstTime } = appStore.systemParam
  const { isFirstLogin } = userStore.userInfo
  return passwordModifyFirstTime && isFirstLogin
})

const handlePassWordUpdatedModal = (type: 1 | 2) => {
  let tipText = {
    1: '您的账号为新登录用户，为了您的账号安全，请及时修改密码！',
    2: '您的账号长时间未修改密码，为了您的账号安全，请及时更换密码！'
  }
  passwordTip.value = Modal.warning({
    okText: type === 1 ? '立即修改' : '立即更换',
    content: tipText[type],
    cancelText: '暂不需要',
    hideCancel: false,
    title: '温馨提示',
    width: '350px',
    onOk: async () => {
      passwordVisible.value = true
    }
  })
}

const isDark = useDark({
  selector: 'body',
  attribute: 'arco-theme',
  valueDark: 'dark',
  valueLight: 'light',
  storageKey: 'arco-theme',
  onChanged(dark: boolean) {
    appStore.toggleTheme(dark)
  }
})

const toggleTheme = useToggle(isDark)

const handleToggleTheme = () => toggleTheme()

const handleLogout = () => logout()

const setPopoverVisible = () => {
  const event = new MouseEvent('click', { view: window, bubbles: true, cancelable: true })
  refBtn.value.dispatchEvent(event)
}

const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void

onMounted(() => {
  startPolling();
  if (firstLoginPassWordUpdate.value) handlePassWordUpdatedModal(1)
  if (needUpdatePassWord.value) handlePassWordUpdatedModal(2)
  if (agentFlag.value === '1') initTccc()
})

onBeforeUnmount(() => {
  stopPolling();
  passwordTip.value?.close()
})

</script>

<style scoped lang="scss">
.user-change-list:first-of-type .user-change-container{
	border: 1px solid rgb(var(--primary-6));
}
.user-change-container{
	display: flex;
	height: 100%;
	border: 1px solid #ccc;
	border-radius: 5px;
}
.user-change-container-title{
		width: 100%;height: 25px;
		text-align: center;
		line-height: 25px;
		font-size: 14px;
}
.user-change-container-left {
	width: 50px;
	align-items: center;
	justify-content: center;
	display: flex;
	padding: 0 8px;
}
.user-change-container-right {
	line-height: normal;
	flex: 1; /* 占满剩余空间 */
	padding-top: 10px;
	padding-bottom: 10px;
	font-size: 14px;
	color: var(--color-text-1);
	white-space: nowrap; /* 不换行 */
	overflow: hidden; /* 超出部分隐藏 */
	text-overflow: ellipsis; /* 省略号 */
}
.user-change-container-right>span:first-child{
  color: rgb(var(--primary-6));
}
.user-change-container-right>span{
	display: block;
	white-space: nowrap; /* 不换行 */
	overflow: hidden; /* 超出部分隐藏 */
	text-overflow: ellipsis; /* 省略号 */
	padding: 3px 0px;
}
.navbar {
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
}

.left-side {
  display: flex;
  align-items: center;
  padding-left: 20px;
  overflow: hidden;
}

.xc_logo {
  padding: 15px;
  height: 60px;
  object-fit: contain;
}

.center-side {
  flex: 1;
}

.right-side {
  display: flex;
  padding-right: 20px;
  list-style: none;
  :deep(.locale-select) {
    border-radius: 20px;
  }
  li {
    display: flex;
    align-items: center;
    padding: 0 10px;
  }

  a {
    color: var(--color-text-1);
    text-decoration: none;
  }
  .nav-btn {
    border-color: rgb(var(--gray-2));
    color: rgb(var(--gray-8));
    font-size: 16px;
  }
  .trigger-btn,
  .ref-btn {
    position: absolute;
    bottom: 14px;
  }
  .trigger-btn {
    margin-left: 14px;
  }
}
</style>

<style lang="scss">
.user-change-account .arco-dropdown-list-wrapper{
	max-height: 500px;
}
.message-popover {
  .arco-popover-content {
    margin-top: 0;
  }
}
</style>
