<template>
  <a-layout-footer class="footer-copyright footer" :style="curStyle">
    <div class="md-copyright-text h-v-c">
      <a target="_blank" class="mr16" :style="curStyle" :href="info.beianUrl">
        <img class="md-report-img" src="@/assets/images/beian.png" />
        <!-- {{ info.beianContent }} -->
      </a>
      {{ info.banquanContent }}&nbsp;
      <a :style="curStyle" :href="info.icpUrl" target="_blank">{{ info.icpContent }}</a>
    </div>
  </a-layout-footer>
</template>

<script lang="ts">
import { getBeiAnInfo } from '@/api/commonApi/vis'
export default {
  name: 'CopyRight',
  props: {
    color: { type: String, default: '#888' }
  },
  data() {
    return {
      info: {
        banquanContent: '',
        beianContent: '',
        beianUrl: '',
        icpContent: '',
        icpUrl: ''
      }
    }
  },
  computed: {
    curStyle() {
      return { color: this.color }
    }
  },
  created() {
    // 根据接口获取备案信息
    this.getBeiAnInfo()
  },
  methods: {
    getBeiAnInfo() {
      getBeiAnInfo().then((res = {}) => {
        this.info = Object.assign({}, this.info, res)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$report-img-size: 16px;
.footer-copyright {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  text-align: center;
  background-color: transparent;
  a {
    color: var(--color-text-2);
  }
}
.md {
  &-copyright-text {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    font-size: 12px;
    line-height: $report-img-size;
  }
  &-report-img {
    vertical-align: text-bottom;
    width: $report-img-size;
    height: $report-img-size;
    margin-right: 5px;
  }
}
</style>
