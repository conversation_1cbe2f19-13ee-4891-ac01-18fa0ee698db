<script lang="tsx">
import { useRoute, useRouter, RouteRecordRaw } from 'vue-router'
import { defineComponent, ref, h, compile, computed } from 'vue'
import { listenerRouteChange } from '@arco/utils/route-listener'
import { Menu, SubMenu, MenuItem } from '@arco-design/web-vue'
import { openWindow, regexUrl } from '@arco/utils/path'
import type { RouteMeta } from 'vue-router'
import useMenuTree from './use-menu-tree'
import { useAppStore } from '@arco/store'
import { getToken } from '@/utils/auth.ts'

export default defineComponent({
  emit: ['collapse'],
  setup() {

    const route = useRoute()
    const router = useRouter()
    const appStore = useAppStore()
    const { menuTree } = useMenuTree()

    const collapsed = computed({
      get() {
        if (appStore.device === 'desktop') return appStore.menuCollapse
        return false
      },
      set(value: boolean) {
        appStore.updateSettings({ menuCollapse: value })
      }
    })

    const topMenu = computed(() => appStore.topMenu)
    const openKeys = ref<string[]>([])
    const selectedKey = ref<string[]>([])

    const goto = (item: RouteRecordRaw) => {
      // Open external link
      if (regexUrl.test(item.path)) {
				let pathUrl = item.path;
				const tokenHolder = "${token}"
				//如果存在占位符就替换
				if(pathUrl.indexOf(tokenHolder) > -1){
					pathUrl = pathUrl.replaceAll(tokenHolder, getToken())
				}
        openWindow(pathUrl)
        selectedKey.value = [item.name as string]
        return
      }
      // Eliminate external link side effects
      const { hideInMenu, activeMenu } = item.meta as RouteMeta
      if (route.name === item.name && !hideInMenu && !activeMenu) {
        selectedKey.value = [item.name as string]
        return
      }
      // Trigger router change
      router.push({ name: item.name })

    }

    const findMenuOpenKeys = (target: string) => {
      const result: string[] = []
      let isFind = false
      const backtrack = (item: RouteRecordRaw, keys: string[]) => {
        if (item.name === target) {
          isFind = true
          result.push(...keys)
          return
        }
        if (item.children?.length) {
          item.children.forEach((el) => {
            backtrack(el, [...keys, el.name as string])
          })
        }
      }
      menuTree.value.forEach((el: RouteRecordRaw) => {
        if (isFind) return // Performance optimization
        backtrack(el, [el.name as string])
      })
      return result
    }

    listenerRouteChange((newRoute) => {
      const { requiresAuth, activeMenu, hideInMenu } = newRoute.meta
      if (requiresAuth && (!hideInMenu || activeMenu)) {
        const menuOpenKeys = findMenuOpenKeys((activeMenu || newRoute.name) as string)
        const keySet = new Set([...menuOpenKeys, ...openKeys.value])
        openKeys.value = [...keySet]
        selectedKey.value = [activeMenu || menuOpenKeys[menuOpenKeys.length - 1]]
      }
    }, true)

    const setCollapse = (val: boolean) => {
      if (appStore.device === 'desktop') appStore.updateSettings({ menuCollapse: val })
    }

    const renderSubMenu = () => {
      function travel(_route?: RouteRecordRaw[], nodes = []) {
        if (_route) {
          _route.forEach((element) => {
            // This is demo, modify nodes as needed
            const icon = element?.meta?.icon ? () => h(compile(`<${element?.meta?.icon} />`)) : null
            const node =
              element?.children && element?.children.length !== 0
                ? h(
                    SubMenu,
                    { key: element?.name },
                    {
                      // title: () => t(element?.meta?.locale || '')
                      default: () => travel(element?.children),
                      title: () => element?.meta?.title || '',
                      icon
                    }
                  )
                : h(
                    MenuItem,
                    {
                      onClick: () => goto(element),
                      key: element?.name
                    },
                    {
                      // default: () => t(element?.meta?.locale || ''),
                      default: () => element?.meta?.title || '',
                      icon: icon
                    }
                  )
            nodes.push(node as never)
          })
        }
        return nodes
      }
      let travelMenus = travel(menuTree.value)
      return travelMenus
    }

    return () =>
      h(
        Menu,
        {
          mode: topMenu.value ? 'horizontal' : 'vertical',
          collapsed: collapsed.value,
          openKeys: openKeys.value,
          selectedKeys: selectedKey.value,
          showCollapseButton: appStore.device !== 'mobile',
          autoOpen: false,
          autoOpenSelected: true,
          levelIndent: 34,
          style: { height: '100%', width: '100%' },
          onCollapse: setCollapse,
          ['onUpdate:openKeys']: (vals: string[]) => {
            openKeys.value = vals
          },
          ['onUpdate:selectedKeys']: (vals: string[]) => {
            selectedKey.value = vals
          }
        },
        {
          default: () => renderSubMenu()
        }
      )
  }
})
</script>

<style lang="scss" scoped>
:deep(.arco-menu-inner) {
  .arco-menu-inline-header {
    display: flex;
    align-items: center;
  }
  .arco-icon {
    &:not(.arco-icon-down) {
      font-size: 18px;
    }
  }
}
</style>
