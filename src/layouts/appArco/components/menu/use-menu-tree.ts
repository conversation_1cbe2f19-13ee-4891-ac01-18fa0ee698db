import { RouteRecordRaw, RouteRecordNormalized } from 'vue-router'
import appClientMenus from '@arco/router/app-menus'
import usePermission from '@arco/hooks/permission'
import { useAppStore } from '@arco/store'

import { computed } from 'vue'
import _ from 'lodash'

export default function useMenuTree() {
  const permission = usePermission()
  const appStore = useAppStore()
  
  const appRoute = computed(() => {
    if (appStore.menuFromServer) {
      return appStore.appAsyncMenus
    }
    return appClientMenus
  })

  // function handleRouteLocale(routes: typeof appRoute.value, type: 'zh' | 'en') {
  //   let locale: { [key: string]: string } = {}
  //   routes.forEach((route) => {
  //     let key = `mdtmenu.list.${String(route.name)}`
  //     let title: string = type === 'zh' ? route.meta?.title ?? '未命名菜单' : String(route.name) ?? '未命名菜单'
  //     locale[key] = title
  //     if (route.children && route.children.length) {
  //       let childrenLocale = handleRouteLocale(route.children, type)
  //       locale = Object.assign(childrenLocale, locale)
  //     }
  //   })
  //   return locale
  // }

  // console.log(handleRouteLocale(appRoute.value, 'en'))

  const menuTree = computed(() => {
    const copyRouter = _.cloneDeep(appRoute.value) as RouteRecordNormalized[]
    // 后端已处理排序
    copyRouter.sort((a: RouteRecordNormalized, b: RouteRecordNormalized) => {
      return (a.meta.order || 0) - (b.meta.order || 0)
    })
    
    function travel(_routes: RouteRecordRaw[], layer: number) {
      if (!_routes) return null
      let filterRouters = _routes.filter(routeItem => !routeItem.meta?.hideInMenu)
      const collector: any = filterRouters.map((element) => {
        // no access
        if (!permission.accessRouter(element)) {
          return null
        }

        if (element.meta?.hideChildrenInMenu || !element.children || !element.children.length) {
          element.children = []
          return element
        }

        element.children = element.children.filter((x) => x.meta?.hideInMenu !== true)

        const subItem = travel(element.children, layer + 1)

        if (subItem.length) {
          element.children = subItem
          return element
        }
        // the else logic
        if (layer > 1) {
          element.children = subItem
          return element
        }

        return element
      })
      return collector
    }
    let travelMenus = travel(copyRouter, 0)
    return travelMenus
  })

  return { menuTree }
}
