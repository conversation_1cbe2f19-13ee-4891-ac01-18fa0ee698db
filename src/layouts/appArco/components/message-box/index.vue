<template>
    <a-tabs v-model:active-key="tabActiveKey" type="rounded" destroy-on-hide>
      <a-tab-pane v-for="item in tabList" :key="item.key">
        <template #title>
          <span> {{ item.title }} </span>
        </template>
        <div class="scroll-container">
					<a-spin v-if="loading" :loading="loading" tip="加载中" style="width: 100%;margin-top: 10px" />
					<a-result v-else-if="!renderList.length" status="404">
						<template #subtitle> 暂无内容 </template>
					</a-result>
          <List
						v-else
            :render-list="renderList"
            :scroll="{ y: 400 }"
            @item-click="handleItemClick"
          />
        </div>
      </a-tab-pane>
       <template #extra>
        <a-button type="text" @click="handleReadAll">已读全部</a-button>
      </template>
    </a-tabs>
</template>

<script lang="ts" setup>
import {listMsg,updateAllToReaded, setMessageStatus, MessageRecord } from '@arco/api/message'
import { ref, reactive, toRefs, computed, onMounted, watch } from 'vue'
import useLoading from '@arco/hooks/loading'
import List from './list.vue'
import { Message } from '@arco-design/web-vue'

interface TabItem {
  avatar?: string
  title: string
  key: string
}

function transformRawData(rawData: any[]): MessageRecord[] {
  return rawData.map(item => ({
    id: item.msgId,    
    type: item.msgType,
    title: item.msgTitle,
    content: item.msgContent,
    time: item.createTm,           
    status: item.readed ? 1 : 0,  
    messageType: item.msgModule,
    readed: item.readed,
    avatar: item.avatar || '',
    subTitle: item.subTitle || ''
  }))
}

const { loading, setLoading } = useLoading(false)

const tabActiveKey = ref<0|1>(0)
const tabList = [
	{
		title: '未读消息',
		key: 0
	},
	{
		title: '已读消息',
		key: 1
	}
]

const emit = defineEmits(['countUnRead'])

const renderList = ref<Array<MessageRecord>>([])

function queryDataList() {
	setLoading(true)
	listMsg(tabActiveKey.value).then((res) => {
		renderList.value = transformRawData(res)
		setLoading(false)
	})
}

watch(tabActiveKey,() => {
	queryDataList()
},{
	immediate: true
})


const handleItemClick = (item: MessageRecord) => {
	setLoading(true)

	setMessageStatus(item.id, true).then((res) => {
		queryDataList()
		//提醒操作成功
		Message.success('操作成功')
		setLoading(false)
		emit('countUnRead')
	})
}
const handleReadAll=()=>{
	setLoading(true)

	updateAllToReaded().then(() => {
		queryDataList()
		//提醒操作成功
		Message.success('操作成功')
		setLoading(false)
		emit('countUnRead')
	})
}

</script>

<style scoped lang="scss">
:deep(.arco-popover-popup-content) {
  padding: 0;
}

:deep(.arco-list-item-meta) {
  align-items: flex-start;
}

:deep(.arco-tabs-nav) {
  padding: 14px 0 12px 16px;
  border-bottom: 1px solid var(--color-neutral-3);
}

:deep(.arco-tabs-content) {
  padding-top: 0;
  .arco-result-subtitle {
    color: rgb(var(--gray-6));
  }
}

.scroll-container {
  max-height: 400px;
  overflow-y: auto;
}
</style>
