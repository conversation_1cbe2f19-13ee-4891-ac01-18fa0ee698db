<template>
  <a-input-number
    v-if="type === 'number'"
    :style="{ width: '80px' }"
    :default-value="defaultValue as number"
    @change="handleChange"
  />
  <a-switch v-else :default-checked="defaultValue as boolean" @change="handleChange" />
</template>

<script lang="ts" setup>
const props = defineProps({
  defaultValue: { type: [String, Boolean, Number], default: '' },
  type: { type: String, default: '' },
  name: { type: String, default: '' }
})

const emit = defineEmits(['inputChange'])

const handleChange = (value: unknown) => {
  emit('inputChange', { value, key: props.name })
}
</script>
