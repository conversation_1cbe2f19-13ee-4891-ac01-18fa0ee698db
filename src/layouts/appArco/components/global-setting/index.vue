<template>
  <div v-if="!appStore.navbar" class="fixed-settings" @click="setVisible">
    <a-button type="primary">
      <template #icon>
        <icon-settings />
      </template>
    </a-button>
  </div>
  <a-drawer
    :width="300"
    unmount-on-close
    :visible="visible"
    cancel-text="关闭"
    ok-text="保存"
    @ok="copySettings"
    @cancel="cancel"
  >
    <template #title> 系统配置 </template>
    <Block :options="contentOpts" title="内容设置" />
    <Block :options="othersOpts" title="其他设置" />
    <a-alert>
      配置之后仅作用于该系统，点击下方的 "保存" 按钮，将信息保存至 数据库 中。
    </a-alert>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Message } from '@arco-design/web-vue'
import { useClipboard } from '@vueuse/core'
import { useAppStore } from '@arco/store'
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

import Block from './block.vue'

const emit = defineEmits(['cancel'])

const { t } = useI18n()
const appStore = useAppStore()
const { copy } = useClipboard()
const visible = computed(() => appStore.globalSettings)
const contentOpts = computed(() => [
  { name: '导航栏', key: 'navbar', defaultVal: appStore.navbar },
  { name: '菜单栏', key: 'menu', defaultVal: appStore.menu },
  { name: '顶部菜单栏', key: 'topMenu', defaultVal: appStore.topMenu },
  { name: '底部', key: 'footer', defaultVal: appStore.footer },
  { name: '多页签', key: 'tabBar', defaultVal: appStore.tabBar },
  { name: '面包屑', key: 'breadcrumb', defaultVal: appStore.breadcrumb },
  // { name: '菜单来源后台', key: 'menuFromServer', defaultVal: appStore.menuFromServer },
  { name: '菜单宽度 (px)', key: 'menuWidth', defaultVal: appStore.menuWidth, type: 'number' }
])
const othersOpts = computed(() => [
  { name: '色弱模式', key: 'colorWeak', defaultVal: appStore.colorWeak }
])

const cancel = () => {
  appStore.updateSettings({ globalSettings: false })
  emit('cancel')
}

const copySettings = async () => {
  const text = JSON.stringify(appStore.$state, null, 2)
  await copy(text)
  Message.success(t('settings.copySettings.message'))
}

const setVisible = () => {
  appStore.updateSettings({ globalSettings: true })
}
</script>

<style scoped lang="scss">
.fixed-settings {
  position: fixed;
  top: 280px;
  right: 0;
  z-index: 10;

  svg {
    font-size: 18px;
    vertical-align: -4px;
  }
}
</style>
