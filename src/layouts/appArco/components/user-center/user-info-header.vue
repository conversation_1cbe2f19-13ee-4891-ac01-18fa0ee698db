<template>
  <div class="header">
    <a-space :size="12" direction="vertical" align="center">
      <a-avatar :size="64">
        <template #trigger-icon>
          <icon-camera />
        </template>
        <img :src="userImg" />
      </a-avatar>
      <a-typography-title :heading="6" style="margin: 0">
        {{ userInfo.loginName }}
      </a-typography-title>
      <div class="user-msg">
        <a-space :size="18">
          <div>
            <icon-home />
            <a-typography-text>{{ userInfo.deptName || '(暂无)' }}</a-typography-text>
          </div>
          <div>
            <icon-email />
            <a-typography-text>
              {{ userInfo.employeeEmail || '(暂无)' }}
            </a-typography-text>
          </div>
          <div>
            <icon-phone />
            <a-typography-text>{{ userInfo.employeeMobile || '(暂无)' }}</a-typography-text>
          </div>
        </a-space>
      </div>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
import defaultUserImg from '@/assets/images/no-face.jpg'
import { useUserStore } from '@arco/store'
import { computed } from 'vue'

const userStore = useUserStore()

const userImg = computed(() => userStore.userInfo.userImg || defaultUserImg)
const userInfo = computed(() => userStore.userInfo)
</script>

<style scoped lang="less">
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 204px;
  color: var(--gray-10);
  background: url('@/assets/images/user_bg.png') center center no-repeat;
  background-size: cover;
  border-radius: 4px;

  :deep(.arco-avatar-trigger-icon-button) {
    color: rgb(var(--arcoblue-6));

    :deep(.arco-icon) {
      vertical-align: -1px;
    }
  }
  .user-msg {
    .arco-icon {
      color: rgb(var(--gray-10));
    }
    .arco-typography {
      margin-left: 6px;
    }
  }
}
</style>
