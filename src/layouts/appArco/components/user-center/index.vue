<template>
  <a-modal
    ref="modalRef"
    v-model:visible="visible"
    :width="width"
    :title="title"
    :mask-closable="maskClosable"
    :footer="false"
    title-align="start"
    modal-class="mdt-modal"
  >
    <div class="container">
      <UserInfoHeader />
      <div class="content">
        <a-row class="wrapper">
          <a-col :span="24">
            <a-tabs default-active-key="1" type="rounded">
              <a-tab-pane key="1" title="基础信息">
                <BasicInformation />
              </a-tab-pane>
              <a-tab-pane key="2" title="安全设置">
                <SecuritySettings @update-success="updatePassWord" />
              </a-tab-pane>
            </a-tabs>
          </a-col>
        </a-row>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import BasicInformation from './basic-information.vue'
import SecuritySettings from './security-settings.vue'
import UserInfoHeader from './user-info-header.vue'
import { useUserStore } from '@arco/store'

interface Props {
  width?: string | number
  maskClosable?: boolean
  title?: string
  text?: string
}

withDefaults(defineProps<Props>(), {
  maskClosable: true,
  draggable: false,
  width: '600px',
  title: '',
  text: ''
})

const [visible] = defineModel('visible', { default: false, required: true, type: Boolean })
const userStore = useUserStore()

const updatePassWord = () => {
  visible.value = false
  userStore.logout()
}
</script>

<script lang="ts">
export default { name: 'UserCenter' }
</script>

<style scoped lang="scss">
.content {
  display: flex;
  margin-top: 12px;

  .tab-pane-wrapper {
    padding: 0 16px 16px 16px;
  }
}
</style>
