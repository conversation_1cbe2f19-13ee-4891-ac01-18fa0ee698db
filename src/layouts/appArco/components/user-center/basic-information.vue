<template>
  <a-form
    ref="formRef"
    class="form"
    :model="formData"
    :rules="formRules"
    :label-col-props="{ span: 5 }"
    :wrapper-col-props="{ span: 19 }"
  >
    <a-form-item label="姓名" field="employeeName">
      <span>{{ formData.employeeName }}</span>
    </a-form-item>
    <a-form-item label="所属部门" field="deptName">
      <span>{{ formData.deptName }}</span>
    </a-form-item>
    <a-form-item label="联系电话" field="employeeMobile">
      <a-input v-model="formData.employeeMobile" placeholder="请输入联系电话"></a-input>
    </a-form-item>
    <a-form-item label="座机电话" field="landLineNumber">
      <a-input v-model="formData.landLineNumber" placeholder="请输入座机号码"></a-input>
    </a-form-item>
    <a-form-item label="邮箱" field="employeeEmail">
      <a-input v-model="formData.employeeEmail" placeholder="请输入邮箱"></a-input>
    </a-form-item>
    <a-form-item label="角色" field="roleNames">
      <span>{{ formData.roleNames }}</span>
    </a-form-item>
  </a-form>
  <div class="security-opreation">
    <a-space>
      <a-button type="secondary" @click="reset"> 重置 </a-button>
      <a-button :loading="loading" type="primary" @click="validate"> 保存 </a-button>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
import type { ACCOUNT_API_USERINFO_TYPE } from '@/api/commonApi/account/types/account'
import type { FormInstance } from '@arco-design/web-vue/es/form'
import { updateUserInfo } from '@/api/commonApi/account'
import { Message } from '@arco-design/web-vue'
import { useUserStore } from '@arco/store'
import { computed, ref, watch } from 'vue'

const userStore = useUserStore()

const formRef = ref<FormInstance>()
const formData = ref<ACCOUNT_API_USERINFO_TYPE['userInfo']>({
  employeeMobile: '',
  landLineNumber: '',
  employeeEmail: '',
  employeeName: '',
  isFirstLogin: 0,
  companyName: '',
  companyType: '',
  employeeId: '',
  agentFlag: '',
  loginName: '',
  roleNames: '',
  accountId: '',
  copyFlag: '',
  deptName: ''
})

const formRules = ref({
  employeeName: [
    { required: true, message: '员工名称不能为空', trigger: 'blur' },
    { max: 20, message: '员工名称不能超过20个字符', trigger: 'blur' }
  ],
  deptName: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
  employeeMobile: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
  employeeEmail: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
  roleNames: [{ required: true, message: '角色不能为空', trigger: 'blur' }]
})
const loading = ref(false)

const user = computed(() => {
  return userStore.userInfo
})

watch(
  () => user.value,
  (userInfo) => (formData.value = { ...userInfo }),
  { immediate: true, deep: true }
)

const validate = async () => {
  const res = await formRef.value?.validate()
  if (!res) {
    loading.value = true
    updateUserInfo({
      landLineNumber: formData.value.landLineNumber,
      moblie: formData.value.employeeMobile,
      email: formData.value.employeeEmail
    })
      .then(() => {
        // appPinia.getAppUserInfo()
        Message.success('修改成功')
      })
      .finally(() => {
        loading.value = false
      })
  }
}
const reset = async () => {
  await formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.security-opreation {
  width: 100%;
  text-align: center;
}
</style>
