<template>
  <div class="tab-bar-container">
    <a-affix ref="affixRef" :offset-top="offsetTop">
      <div class="tab-bar-box">
        <div class="tab-bar-scroll">
          <div class="tags-wrap">
            <tab-item v-for="(tag, index) in tagList" :key="tag.fullPath" :index="index" :item-data="tag" />
          </div>
        </div>
        <div class="tag-bar-operation"></div>
      </div>
    </a-affix>
  </div>
</template>

<script lang="ts" setup>
import { listenerRouteChange, removeRouteListener } from '@arco/utils/route-listener'
import { useAppStore, useTabBarStore } from '@arco/store'
import type { RouteLocationNormalized } from 'vue-router'
import { ref, computed, watch, onUnmounted } from 'vue'
import { WHITE_LIST } from '@arco/router/constants'
import tabItem from './tab-item.vue'

const appStore = useAppStore()
const tabBarStore = useTabBarStore()

const affixRef = ref()
const tagList = computed(() => {
  return tabBarStore.getTabList
})
const offsetTop = computed(() => {
  return appStore.navbar ? 60 : 0
})

watch(
  () => appStore.navbar,
  () => {
    affixRef.value.updatePosition()
  }
)

listenerRouteChange((route: RouteLocationNormalized) => {
  const hasPath = tagList.value.some((tag) => tag.fullPath === route.fullPath)
  const hasWhite = WHITE_LIST.some((page) => page.name === route.name)
  if (!route.meta.noAffix && !hasPath && !hasWhite) {
    tabBarStore.addTabList(route)
  }
}, true)

onUnmounted(() => {
  removeRouteListener()
})
</script>

<style scoped lang="scss">
.tab-bar-container {
  position: relative;
  background-color: var(--color-bg-2);
  .tab-bar-box {
    display: flex;
    padding: 0 0 0 10px;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
    .tab-bar-scroll {
      height: 32px;
      flex: 1;
      overflow: hidden;
      .tags-wrap {
        padding: 3px 0;
        height: 48px;
        white-space: nowrap;
        overflow-x: auto;

        :deep(.arco-tag) {
          display: inline-flex;
          align-items: center;
          margin-right: 6px;
          cursor: pointer;
          &:first-child {
            .arco-tag-close-btn {
              display: none;
            }
          }
        }
      }
    }
  }

  .tag-bar-operation {
    width: 20px;
    height: 32px;
  }
}
</style>
