import { getLocalStorage, setLocalStorage } from '@/utils/auth'
import type { ModalReturn } from '@arco-design/web-vue'
import { Message, Modal } from '@arco-design/web-vue'
import { getUserInfo } from '@/api/commonApi/account'
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { LOGINS } from '@arco/router/constants'
import { useUserStore } from '@arco/store'
import { listenerUserInfoChange } from '@arco/utils/userinfo-listener.ts'

export default function useVisibility() {
  const tipModal = ref<ModalReturn>()
  const saveUser = ref('')

  const router = useRouter()
  const route = useRoute()

  const _userStore = useUserStore()

  listenerUserInfoChange((loginName:string)=>{
    saveUser.value = loginName;
  })

  const onVisibilityChange = async () => {
    const routeName = route.name as string

    const isLoginPage = LOGINS.includes(routeName)
    let currentUser = getLocalStorage('loginName')
    // console.log(currentUser, saveUser.value)
    // getToken()

    if (document.hidden) {
      tipModal.value?.close()
    } else {
      // 场景1：多个登录页签，用户A登录，点击其他登录标签显示视图,自动跳转首页
      if (currentUser && isLoginPage) {
        Message.info('账号已登录！')
        router.push({ name: 'desktop' })
        return
      }

      // 场景2：多页签，用户登出登录，点击其他标签显示视图，提示跳转登录页面
      if (!currentUser && !isLoginPage) {
        // 获取用户信息
        const userInfo = await getUserInfo(true)
        if (userInfo) {
          setLocalStorage('loginName', userInfo.userInfo.loginName)
          currentUser = userInfo.userInfo.loginName
        } else {
          Message.info('已登出系统，已为你返回登录页面!')
          _userStore.backLogin()
          saveUser.value = ''
          return
        }
      }
      // 场景3：多页签，用户A登出登录，切换用户B登录,点击其他标签显示视图
      if (currentUser && currentUser != saveUser.value) {
        tipModal.value = Modal.warning({
          okText: '确定',
          content: '由于切换登录账号,当前页面已失效',
          title: '账号切换提示',
          width: '350px',
          escToClose: false,
          maskClosable: false,
          onOk: async () => {
            location.reload()
          }
        })
      }
    }
  }

  onMounted(() => {
    saveUser.value = _userStore.userInfo.loginName
    // 添加事件监听
    document.addEventListener('visibilitychange', onVisibilityChange)
  })

  onUnmounted(() => {
    // 移除事件监听
    document.removeEventListener('visibilitychange', onVisibilityChange)
  })
}
