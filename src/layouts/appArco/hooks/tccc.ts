// import { modifyCaseContactsStatus } from '@/api/arcoApi/modules/case/mycase'
import { bindVirtualNumber, unBindVirtualNumber } from '@/api/arcoApi/mediateManage/jobLog/callRepairRecord'
import { getAgentMsg, getCaseInfo, saveCallInfo } from '@/api/arcoApi/workbench/tccWorkbench'
import { useTcccStore, useUserStore } from '@/layouts/appArco/store'
import { isTccc, show, online } from '@arco/utils/tccc'
import { ref, getCurrentInstance, computed } from 'vue'
import { Modal, Message } from '@arco-design/web-vue'
import { phoneAndTelReg } from '@/assets/ts/regexp'

import { tcccDomID } from '@/settings'
import router from '@arco/router'

export default function useTccc() {
  const mediatorName = ref<string>('')
  const mediatorId = ref<string>('')

  const tcccStore = useTcccStore()
  const userStore = useUserStore()

  const instance = getCurrentInstance()

  mediatorId.value = userStore.userInfo.accountId
  mediatorName.value = userStore.userInfo.employeeName
  const globConfig = instance?.appContext.config.globalProperties

  const caseEventparam = computed(() => tcccStore.getCaseEventParam)

  // 根据号码获取案件信息
  const byPhoneGetCaseInfo = async (phone) => {
    // 如果是在案件详情页面呼叫
    if (caseEventparam.value.fromCaseDetails) {
      // 返回案件信息
      return { caseId: caseEventparam.value.caseId, calleeName: caseEventparam.value.calleeName, litigantIdList:caseEventparam.value.litigantIdList }
    }

    let param = new FormData()
    // 正则替换号码前四位数“0086”
    phone = phone.replace(/^0086/, '')
    param.append('phone', phone)
    const caseInfo = await getCaseInfo(param)
    // 如果存在关联案件，弹出窗口-是否跳转所属案件下案件详情
    // 获取当前路由参数
    const { caseId: routeCaseId } = globConfig?.$route.params as { caseId?: string }
    if (caseInfo && caseInfo.caseId && caseInfo.caseId !== routeCaseId) {
      Modal.info({
        title: '温馨提示',
        hideCancel: false,
        content: '该号码用户已关联案件，是否跳转至该案件详情？',
        onOk: () => router.push({ name: `caseDetails`, params: { caseId: caseInfo.caseId } }),
        onCancel: () => {}
      })
    }
    // 如果不存在关联案件，不弹窗
    return caseInfo ? caseInfo : { caseId: '', calleeName: '',litigantIdList: [] }
  }

  const initTccc = async () => {
    if (isTccc()) {
      online()
      show()
    } else {
      const tcccInfo = await getAgentMsg()
      tcccInfo ? injectTccc(tcccInfo) : Message.error('获取坐席信息失败')
    }
  }

  const injectTccc = (info: any) => {
    let { token, appId, sdkURL, userId } = info
    const scriptDom = document.createElement('script')
    scriptDom.setAttribute('crossorigin', 'anonymous')
    scriptDom.setAttribute('id', tcccDomID)
    scriptDom.dataset.token = token
    scriptDom.dataset.sdkAppId = appId
    scriptDom.dataset.userId = userId
    scriptDom.src = sdkURL
    document.body.appendChild(scriptDom)
    if (!Object.prototype.hasOwnProperty.call(window, 'tccc')) {
      scriptDom.addEventListener('load', listenerEvents)
    }
  }

  const listenerEvents = () => {
    // 检测当前浏览器是否支持
    window.tccc.Devices.isBrowserSupported()
    // sdk初始化完成
    window.tccc.on(window.tccc.events.ready, function () {
      console.log('坐席初始化')
      Message.success('坐席初始化成功')
    })
    // 会话呼入
    window.tccc.on(window.tccc.events.callIn, async function (res: any) {
      console.log('会话呼入事件', res)
      console.log(caseEventparam.value)
      let caseInfo = await byPhoneGetCaseInfo(res.callerPhoneNumber)
      saveCallInfo({
        mediatorName: mediatorName.value,
        calleeName: caseInfo.calleeName,
        mediatorId: mediatorId.value,
        phone: res.callerPhoneNumber,
        sessionId: res.sessionId,
        caseId: caseInfo.caseId,
				litigantIdList:caseInfo.litigantIdList
      }).finally(() => {
        tcccStore.resetState()
      })
    })
    // 座席接入会话
    window.tccc.on(window.tccc.events.userAccessed, async function (res: any) {
      console.log('座席接入会话', res)
    })
    // 会话超时转接事件
    window.tccc.on(window.tccc.events.autoTransfer, function (res: any) {
      console.log('会话超时转接事件', res)
    })
    // 会话结束事件
    window.tccc.on(window.tccc.events.sessionEnded, function (res: any) {
      console.log('会话结束事件', res)
      if (res) {
        const { direction, callerPhoneNumber, calleePhoneNumber } = res
        const params = {
          seatNumber: direction === '1' ? callerPhoneNumber : calleePhoneNumber,
          virtualNo: direction === '1' ? calleePhoneNumber : callerPhoneNumber
        }
        // 解绑绑虚拟号
        unBindVirtualNumber(params)
      }
    })
    // 外呼成功事件
    window.tccc.on(window.tccc.events.callOuted, async function (res: any) {
      console.log('外呼成功事件', res)
      console.log(caseEventparam.value)
      const { direction, callerPhoneNumber, calleePhoneNumber } = res
      const phone = direction === '1' ? calleePhoneNumber : callerPhoneNumber
      let caseInfo = await byPhoneGetCaseInfo(phone)
      saveCallInfo({
        mediatorName: mediatorName.value,
        calleeName: caseInfo.calleeName,
        mediatorId: mediatorId.value,
        sessionId: res.sessionId,
        caseId: caseInfo.caseId,
				litigantIdList:caseInfo.litigantIdList,
        phone
      }).finally(() => {
        tcccStore.resetState()
      })
    })
    // 外呼对方接听事件
    window.tccc.on(window.tccc.events.calloutAccepted, function (res: any) {
      console.log('外呼对方接听事件', res)
    })
    // 会话转接事件
    window.tccc.on(window.tccc.events.transfer, function (res: any) {
      console.log('会话转接事件', res)
    })
    // 座席状态变更事件
    window.tccc.on(window.tccc.events.statusChanged, function (res: any) {
      console.log('座席状态变更事件', res)
    })
    // 座席被踢下线事件
    window.tccc.on(window.tccc.events.kickedOut, function (res: any) {
      console.log('座席被踢下线事件', res)
    })
    // 语音识别事件
    window.tccc.on(window.tccc.events.asr, function (res: any) {
      console.log('语音识别事件', res)
    })
  }

  const startOutboundCall = async (phoneNumber: string, phoneDesc: string, callerPhone?: string) => {
    let params = { phoneNumber, phoneDesc }
    // 指定呼叫号码
    if (callerPhone) params['callerPhoneNumber'] = callerPhone
    try {
      const CallResponse = await window.tccc.Call.startOutboundCall(params)
      if (CallResponse.status === 'success') {
        Message.success('呼叫成功')
      } else {
        Message.warning('号码呼叫异常!')
      }
    } catch (error: any) {
      Message.warning(error.errorMsg || '号码呼叫失败')
    }
  }

  const mapFormBox = async (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE, caseId: string, field = 'litigantPhone',phoneNumber:string) => {
    if (row.phoneStatus === '正常') {
      Message.warning('恢复跟进后才可以执行此操作')
    } else {
      const callPhone = phoneNumber
      if (phoneAndTelReg.test(callPhone)) {
        tcccStore.setTcccState({ caseId: caseId, calleeName: row.litigantName, fromCaseDetails: true ,litigantIdList:[row.litigantId]})
        // 虚拟号-绑定坐席
        if (field === 'virtualNo') {
          const params = { litigantId: row.litigantId, virtualNo: callPhone, type: 1, caseId }
          const virtualRes = await bindVirtualNumber(params)
          if (virtualRes && virtualRes.callNo) {
            await startOutboundCall(phoneNumber, row.litigantName, virtualRes.callNo)
          } else {
            Message.warning('绑定坐席指定呼叫坐席号码获取失败！')
          }
        } else {
          await startOutboundCall(phoneNumber, row.litigantName)
        }
      } else {
        Message.warning(`${globConfig?.dzPhone(phoneNumber)}不是有效电话`)
      }
    }
  }

  const stopFollowUp = (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE, field = 'litigantPhone') => {
    Modal.info({
      title: '提示',
      hideCancel: false,
      content: `将停止跟进联系人电话-${globConfig?.dzPhone(row[field])}，是否继续？`,
      onOk: () => {
        // modifyCaseContactsStatus({ contactId: row.contactId, optType: 1 }).then(() => {
        //   initData()
        // })
      },
      onCancel: () => {}
    })
  }

  const startFollowUp = (row: REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE, field = 'litigantPhone') => {
    Modal.info({
      title: '提示',
      hideCancel: false,
      content: `将恢复跟进联系人电话-${globConfig?.dzPhone(row[field])}，是否继续？`,
      onOk: () => {
        // modifyCaseContactsStatus({ contactId: row.contactId, optType: 2 }).then(() => {
        //   initData()
        // })
      },
      onCancel: () => {}
    })
  }

  return { initTccc, stopFollowUp, startFollowUp, mapFormBox }
}
