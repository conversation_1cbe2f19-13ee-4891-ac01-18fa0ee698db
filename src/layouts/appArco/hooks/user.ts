import { Message, Modal } from '@arco-design/web-vue'
import { useUserStore } from '@arco/store'

export default function useUser() {
  const userStore = useUserStore()
  const logout = (logoutTo?: string) => {
    Modal.info({
      title: '系统登出',
      titleAlign: 'start',
      content: '确定注销并退出系统吗？',
      closable: false,
      hideCancel: false,
      onOk: async () => {
        await userStore.logout(logoutTo)
        Message.success('登出成功!')
      }
    })
  }
  return { logout }
}
