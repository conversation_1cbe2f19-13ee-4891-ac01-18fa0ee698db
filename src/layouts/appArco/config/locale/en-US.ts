export default {
  'settings.alertContent':
    'After the configuration is only temporarily effective, if you want to really affect the project, click the "Copy Settings" button below and replace the configuration in settings.json.',
  'settings.copySettings.message': 'Copy succeeded, please paste to file src/settings.json.',
  'settings.color.tooltip': '10 gradient colors generated according to the theme color',
  'settings.navbar.screen.toFull': 'Click to switch to full screen mode',
  'settings.navbar.screen.toExit': 'Click to exit the full screen mode',
  'settings.navbar.theme.toLight': 'Click to use light mode',
  'settings.navbar.theme.toDark': 'Click to use dark mode',
  'settings.menuFromServer': 'Menu From Server',
  'settings.otherSettings': 'Other Settings',
  'settings.copySettings': 'Copy Settings',
  'settings.menuWidth': 'Menu Width (px)',
  'settings.content': 'Content Setting',
  'settings.themeColor': 'Theme Color',
  'settings.navbar.alerts': 'alerts',
  'settings.colorWeak': 'Color Weak',
  'settings.language': 'Language',
  'settings.topMenu': 'Top Menu',
  'settings.title': 'Settings',
  'settings.tabBar': 'Tab Bar',
  'settings.search': 'Search',
  'settings.navbar': 'Navbar',
  'settings.footer': 'Footer',
  'settings.close': 'Close',
  'settings.menu': 'Menu'
}
