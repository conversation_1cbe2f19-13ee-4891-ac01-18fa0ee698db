import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

export interface LoginData {
  username: string
  password: string
}

export interface LoginRes {
  token: string
}

export function login(data: LoginData) {
  return axios.post(`${baseURL}/api/user/login`, data)
}

export function logout() {
  return axios.post(`${baseURL}/api/user/logout`)
}

export function getUserInfo() {
  return axios.post(`${baseURL}/api/user/info`)
}

export function getMenuList() {
  return axios.post(`${baseURL}/api/user/menu`)
}
