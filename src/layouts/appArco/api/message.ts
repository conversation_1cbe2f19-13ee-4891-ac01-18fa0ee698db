import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

export interface MessageRecord {
  id: number
  type: number
  title: string
  subTitle: string
  avatar?: string
  content: string
  time: string
  status: 0 | 1
  messageType?: number
}

export type MessageListType = MessageRecord[]
export function queryUnreadCount() {
  return axios.get(
    `${baseURL}/api/mis/sys/sysMsg/pollingMsg`,{hideLoading:true}
  ).then((response:any) => {
    return response.ownerHaveUnreadCount;
  });
}
export function listMsg(readed:0|1): Promise<MessageListType[]> {
  return axios.get(`${baseURL}/api/mis/sys/sysMsg/listMsg/${readed}`,{hideLoading:true})
}



export async function setMessageStatus(msgId: number,status: boolean) {
  const url = `${baseURL}/api/mis/sys/sysMsg/updateReaded/${msgId}/${status}`;
  return axios.put(url,null,{hideLoading:true});
}


export async function updateAllToReaded() {
	const url = `${baseURL}/api/mis/sys/sysMsg/updateAllToReaded`;
	return axios.put(url,null,{hideLoading:true});
}
