<template>
  <router-view v-slot="{ Component, route }">
    <transition name="fade" mode="out-in" appear>
      <component :is="Component" v-if="route.meta.keepAlive" :key="route.fullPath" />
      <keep-alive v-else :include="cacheList">
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
    </transition>
  </router-view>
</template>

<script lang="ts" setup>
import { useTabBarStore } from './store'
import { computed } from 'vue'

const tabBarStore = useTabBarStore()

const cacheList = computed(() => tabBarStore.getCacheList)
</script>

<style scoped lang="scss"></style>
