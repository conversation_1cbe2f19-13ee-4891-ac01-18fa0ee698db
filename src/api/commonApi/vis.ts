/**
 * 访客接口
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import { sysName } from '@/settings' //
import axios from '@/utils/http' // 导入http中创建的axios实例

// /api/vis/main/getSctName 登录页获取系统信息
export const getSysInfo = (): Promise<{ sctName: string; logoUrl: string; twoFactorAuthentication: string }> => {
  return axios.get(`${baseURL}/api/mis/sctvis/main/getSctMsg`, { params: { showLoading: false } })
}

// 获取备案信息
export const getBeiAnInfo = (): Promise<OBJ_KEY_STR_ANY_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sctvis/main/getBeiAnInfo`)
}

// 获取登录页外观数据
export const getLoginAppearanceByAppearancePath = (
  appearancePath: string
): Promise<REQUEST_POST_LOGINAPPEARANCE_RESULT_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sctvis/main/getLoginAppearanceByAppearancePath`, { params: { appearancePath } })
}

// 获取当前平台LOGO
export const getWebLogo = function () {
  return `${baseURL}/api/mis/${sysName}/auth/vis/getWebLogo`
}

// 获取预览LOGO
export const readLogoPath = function () {
  return `${baseURL}/api/mis/sctvis/main/readLogo`
}

// 获取预览
export const getReadLogo = (): Promise<{ data: ArrayBuffer }> => {
  return axios.get(`${baseURL}/api/mis/sctvis/main/readLogo`, { responseType: 'arraybuffer' })
}

export default {
  getBeiAnInfo,
  getReadLogo,
  getWebLogo,
  getSysInfo
}
