/**
 * 消息清单
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// GET /api/mis/sys/sysMsg/listUnReadMsg 查询自己所有的未读消息
export const listUnReadMsg = () => {
  return axios.get(`${baseURL}/api/mis/sys/sysMsg/listUnReadMsg`)
}

// POST /api/mis/sys/sysMsg/pageList 分页查询消息
export const messageList = (param: MESSAGE_API_PARAM_TYPE): Promise<MESSAGE_API_LIST_TYPE> => {
  return axios.post(`${baseURL}/api/mis/sys/sysMsg/pageList`, JSON.stringify(param))
}

// PUT /api/mis/sys/sysMsg/updateAllToReaded/{msgType} 将自己的消息全部置为已读
export const setAllReaded = (msgType: number) => {
  return axios.put(`${baseURL}/api/mis/sys/sysMsg/updateAllToReaded/${msgType}`)
}

// PUT /api/mis/sys/sysMsg/updateReaded/{msgId}/{status} 更新消息消息状态
export const updateMessageState = (msgId: string, status: number) => {
  return axios.put(`${baseURL}/api/mis/sys/sysMsg/updateReaded/${msgId}/${status}`)
}

// GET /api/mis/sys/sysMsg/pollingMsg 更新消息消息状态
export const pollingMsg = (): Promise<{ ownerHaveUnreadCount: number }> => {
  return axios.get(`${baseURL}/api/mis/sys/sysMsg/pollingMsg`, { hideLoading: true })
}
