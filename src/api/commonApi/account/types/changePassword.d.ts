/**
 * 修改密码
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

const changePassword = {
  // /api/mis/auth/userInfo/sendModifyPassSmsCode 修改密码第一步->发送短信验证码
  sendModifyPassSmsCode: (params: OBJ_KEY_STR_ANY_TYPE) => {
    return axios.post(`${baseURL}/api/mis/auth/userInfo/sendModifyPassSmsCode`, JSON.stringify(params))
  },
  // /api/mis/auth/userInfo/updatePassword 修改密码第二步->修改密码
  updatePassword: (params: OBJ_KEY_STR_ANY_TYPE) => {
    return axios.post(`${baseURL}/api/mis/auth/userInfo/updatePassword`, JSON.stringify(params))
  }
}

export default changePassword
