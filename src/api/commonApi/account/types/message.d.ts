declare type MESSAGE_LIST_ROW_TYPE = {
  receiveUserId: number
  receiveUserNm: string
  fromUserId: number
  fromUserNm: string
  msgContent: string
  msgModule: number
  createTm: string
  msgTitle: string
  othParam: string
  msgType: number
  readed: boolean
  bizId: number
  msgId: number
}

declare interface MESSAGE_API_LIST_TYPE {
  list: MESSAGE_LIST_ROW_TYPE[]
  head: {
    additionalProp1: string
    additionalProp2: string
    additionalProp3: string
  }
  hasPrevious: boolean
  lastPageNum: number
  pageNumber: number
  hasNext: boolean
  data?: string
  total: number
  size: number
  msg?: string
}

declare interface MESSAGE_API_PARAM_TYPE {
  extParam?: { msgModule: number; msgTypes: (string | number)[]; readed: number; searchPeriod: number }
  pageInfo?: { orderParams?: []; pageNumber: number; size: number }
  param?: { msgContent: string; msgTitle: string }
  defaultDescs?: string[]
  defaultAscs?: string[]
}
