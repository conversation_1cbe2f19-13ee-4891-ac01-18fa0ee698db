import type { RELEVANCE_USER_INFO } from '@/api/arcoApi/systemManage/types/relevanceUserInfo'
export interface ACCOUNT_API_USERINFO_TYPE {
  permissions: ARR_STRING_TYPE
  userInfo: {
    pwdUpdateTime: string | null
    landLineNumber: string
    employeeMobile: string
    employeeEmail: string
    employeeName: string
    isFirstLogin: number
    companyName: string
    companyType: string
    employeeId: string
    agentFlag: string
    loginName: string
    roleNames: string
    accountId: string
    copyFlag: string
    deptName: string
    userImg?: string
  },
	setting:REQUEST_COMPANY_SETTING_DETAIL_DATA_TYPE,
	relevanceUserInfoList:RELEVANCE_USER_INFO[]
}
