import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

import type { ACCOUNT_API_USERINFO_TYPE } from './types/account'

// /api/mis/auth/userInfo/detail 获取用户个人信息
export const getUserInfo = (loading = false): Promise<ACCOUNT_API_USERINFO_TYPE> => {
  return axios.get(`${baseURL}/api/mis/auth/userInfo/detail`, { hideLoading: loading })
}

// /api/mis/auth/userInfo/resetPwd 修改用户个人密码
export const resetPwd = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/userInfo/resetPwd`, JSON.stringify(params))
}

// /api/mis/auth/userInfo/updateUserInfo 修改用户个人信息
export const updateUserInfo = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/userInfo/updateUserInfo`, JSON.stringify(params))
}

export default {
  resetPwd,
  getUserInfo,
  updateUserInfo
}
