declare interface REQUEST_GET_SYSTEM_PARAM_DATA_TYPE {
  moduleCode: string
  paramCmnt: string
  paramCode: string
  paramName: string
  paramValue: string
}

declare interface REQUEST_GET_APP_SYSTEM_PARAM_TYPE {
  passwordModifyFirstTime: number
  twoFactorAuthentication: number
  regularPasswordUpdate: number
  passwordErrorNumber: number
  isPasswordSetting: number
  passwordErrorLock: number
  passwordContent: number[]
  isTimeoutLogout: number
  autoUnlockTime: number
  passwordSize: number
  isPhoneSign: number
  updateCycle: number
  logoutTime: number
}
