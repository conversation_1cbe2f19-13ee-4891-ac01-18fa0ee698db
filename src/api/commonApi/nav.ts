/**
 * 菜单接口
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

import type { NEW_LIST_USER_MENU_DATA_TYPE } from './types/nav'

// /api/mis/sysMenu/listUserMenu 获取菜单
export const getNavigation = (): Promise<NEW_LIST_USER_MENU_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sysMenu/listUserMenu`)
}
// /api/mis/sysMenu/newListUserMenu 获取菜单-new
export const newListUserMenu = (): Promise<NEW_LIST_USER_MENU_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sysMenu/newListUserMenu`)
}

export default { getNavigation, newListUserMenu }
