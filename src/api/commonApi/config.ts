/**
 * 配置接口
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 查询所有系统参数
export const findSystemParam = (): Promise<REQUEST_GET_SYSTEM_PARAM_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/all`)
}

// 通过模块代码查询系统参数
export const bySystemModuleFindParam = (
  moduleCode: string,
  paramCode: string
): Promise<REQUEST_GET_SYSTEM_PARAM_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/${moduleCode}/${paramCode}`)
}

// 更新系统参数
export const updateSystemParam = (
  params: REQUEST_GET_SYSTEM_PARAM_DATA_TYPE[]
): Promise<REQUEST_GET_SYSTEM_PARAM_DATA_TYPE> => {
  return axios.put(`${baseURL}/api/mis/sys/sysParameter`, JSON.stringify(params))
}

// 保存系统参数
export const saveSystemParam = (params: REQUEST_GET_APP_SYSTEM_PARAM_TYPE): Promise<any> => {
  return axios.post(`${baseURL}/api/mis/sys/sysParameter/saveSystemConfiguration`, params)
}

// 获取系统参数
export const getSystemParam = (): Promise<REQUEST_GET_APP_SYSTEM_PARAM_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/getSystemConfiguration`)
}

export default { bySystemModuleFindParam }
