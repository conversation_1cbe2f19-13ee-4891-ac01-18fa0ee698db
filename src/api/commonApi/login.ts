/**
 * 登录接口
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 登录
export const login = (params: LOGIN_USER_FORM_DATA): Promise<{ token: string }> => {
  return axios.post(`${baseURL}/api/mis/auth/login`, JSON.stringify(params), { hideLoading: true })
}

// 获取图片验证码信息
export const getCaptchaInfo = (params: {
  loginName: string
}): Promise<{ imageBase64Str: string; verifyKey: string }> => {
  return axios.get(`${baseURL}/api/mis/auth/getCaptchaInfo`, { params, hideLoading: true })
}

// 根据用户名发送手机验证码
export const sendSmsCodeByUserName = (params: { loginName: string; captcha: string; verifyKey: string }) => {
  return axios.post(`${baseURL}/api/mis/auth/sendSmsCodeByUserName`, JSON.stringify(params), {
    hideLoading: false
  })
}

export default { login }
