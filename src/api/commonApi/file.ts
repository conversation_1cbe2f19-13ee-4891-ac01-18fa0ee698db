/**
 * company文件组件接口
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import { getToken } from '@/utils/auth' // getToken
import axios from '@/utils/http' // 导入http中创建的axios实例
import { TokenKeyOfHeader } from '@/settings'
import Qs from 'qs'
/**
 * 文件接口
 */
// /api/mis/common/file/deleteFile
export const deleteFile = (filePath: string) => {
  return axios.delete(`${baseURL}/api/mis/common/file/deleteFile`, {
    data: { filePath },
    hideLoading: true
  })
}
// 下载文件 path:/a/b/ fileName:xxxx.docx
export const download = ({ filePath, fileFolder, fileName, fileId }: OBJ_KEY_STR_STR_TYPE) => {
  return `${baseURL}/api/mis/common/file/downloadFiles/${encodeURI(
    filePath ? filePath : fileFolder + fileName
  )}?${Qs.stringify({
    [`auth-token`]: getToken(),
    fileId
  })}`
}
// 视频调解视频下载 path:/a/b/ fileName:xxxx.docx
export const downloadVideo = ({ filePath, fileFolder, fileName, fileId }: OBJ_KEY_STR_STR_TYPE) => {
  return `${baseURL}/api/video/downloadMeetingVideo/${encodeURI(
    filePath ? filePath : fileFolder + fileName
  )}?${Qs.stringify({
    [`auth-token`]: getToken(),
    fileId
  })}`
}
// 下载生成pdf的docx文件 path:/a/b/ fileName:xxxx.docx
export const downloadGeneratedDocxFiles = ({ filePath, fileFolder, fileName, fileId }: OBJ_KEY_STR_STR_TYPE) => {
  return `${baseURL}/api/mis/common/file/downloadGeneratedDocxFiles${encodeURI(
    filePath ? filePath : fileFolder + fileName
  )}?${Qs.stringify({
    [`auth-token`]: getToken(),
    fileId
  })}`
}
// 获取文件夹里的文件 {folder:string} /api/mis/common/file/listFolderFiles 获取文件夹里的文件
export const getFolderFiles = (folder: string) => {
  return axios.get(`${baseURL}/api/mis/common/file/listFolderFiles`, {
    hideLoading: true,
    params: { folder: encodeURIComponent(folder) }
  })
}

// /api/mis/common/file/readFiles/** 预览文件
export const preview = ({ fileFolder, fileName }: OBJ_KEY_STR_STR_TYPE) => {
  return `${baseURL}/api/mis/common/file/readFiles${encodeURI(fileFolder + fileName)}?${Qs.stringify({
    [TokenKeyOfHeader]: getToken()
  })}`
}

// /api/mis/common/file/uploadFile 上传文件
export const upload = (params: FormData): Promise<{ filePath: string }> => {
  return axios.post(`${baseURL}/api/mis/common/file/uploadFile`, params, {
    hideLoading: true,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// /api/mis/common/file/docToPdf 传入doc路径生成对应pdf docPath  isTemp
export const covertToPdf = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.get(`${baseURL}/api/mis/common/file/docToPdf`, { params })
}

// /api/mis/common/file/uploadAndConvertPdf 上传文件转pdf
export const uploadAndConvertPdf = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/common/file/uploadAndConvertPdf`, params, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

export default {
  deleteFile,
  download,
  getFolderFiles,
  preview,
  upload,
  covertToPdf,
  uploadAndConvertPdf,
  downloadGeneratedDocxFiles
}
