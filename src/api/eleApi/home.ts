/**
 * 桌面页接口
 */
import { TokenKeyOfHeader } from '@/settings' //
import { getToken } from '@/utils/auth' // getToken
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

import Qs from 'qs'

// /api/auth/company/getCompanyInfo 获取企业信息
export const getCompanyInfo = () => {
  return axios.get(`${baseURL}/api/mis/auth/company/getCompanyInfo`)
}

// /api/auth/company/switchCompany/{companyId} 切换企业
export const switchCompany = (companyId: string) => {
  return axios.get(`${baseURL}/api/mis/auth/company/switchCompany/${companyId}`)
}

// /api/vis/mainPage/readCommissionLogo 预览企业LOGO
export const readCommissionLogo = () => {
  return `${baseURL}/api/mis/vis/mainPage/readCommissionLogo?${Qs.stringify({
    [TokenKeyOfHeader]: getToken()
  })}`
}

// /api/auth/company/getNotRelaCompanyInfo 尚未关联企业时的信息
export const getNotRelaCompanyInfo = () => {
  return axios.get(`${baseURL}/api/mis/auth/company/getNotRelaCompanyInfo`)
}

// /api/auth/company/joinCompany 加入某企业
export const joinCompany = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/company/joinCompany`, JSON.stringify(params))
}

// /api/auth/company/sendJoinCompanyCode 发送加入企业的短信验证码
export const sendJoinCompanyCode = () => {
  return axios.post(`${baseURL}/api/mis/auth/company/sendJoinCompanyCode`)
}

// /api/auth/companyRegister/createEmptyCompany 创建空的企业
export const createEmptyCompany = () => {
  return axios.post(`${baseURL}/api/mis/auth/companyRegister/createEmptyCompany`)
}

// /api/auth/companyRegister/registerCompany 注册企业
export const registerCompany = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/companyRegister/registerCompany`, JSON.stringify(params))
}

// /api/auth/companyRegister/getRegisterCompanyInfo/{companyRegisterId} 获取注册的企业信息
export const getRegisterCompanyInfo = (companyRegisterId: string) => {
  return axios.get(`${baseURL}/api/mis/auth/companyRegister/getRegisterCompanyInfo/${companyRegisterId}`)
}

// /api/mis/auth/compnay/get 桌面页获取企业信息
export const getSysCompany = (): Promise<{
  address: string
  companyIcon: string
  companyId: string
  companyName: string
  email: string
  mobile: string
}> => {
  return axios.get(`${baseURL}/api/mis/auth/company/get`, { params: { showLoading: false } })
}

export default {
  getNotRelaCompanyInfo,
  registerCompany,
  getCompanyInfo,
  switchCompany,
  getSysCompany
}
