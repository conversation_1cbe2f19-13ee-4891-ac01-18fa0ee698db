/**
 * 公共接口
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 获取所有字典<关联登录企业>
export const getDictList = (): Promise<COMMOM_LISTALL_DICTS_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sysDict/listAll`)
}

// 获取对应字典<无关联>
export const getTargetDict = (params: { type: string }): Promise<COMMOM_LIST_ALLDICT_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sysDict/getByType`, { params })
}

// 查询所有启用的委托方
export const getEntrusts = (): Promise<REQUEST_GET_ENTRUSTS_LIST_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/common/biz/getEntrusts`)
}

// 查询所有组织下的部门
export const getAllDept = (): Promise<REQUEST_GET_DEPT_ALLDATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/common/biz/getAllDept`)
}

// 查询所有案由
export const getCaseNatures = (): Promise<REQUEST_GET_CASENATURES_DICTLIST_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/common/biz/getCaseNatures`)
}

// 获取案由字典ID
export const getNaturesDictId = (): Promise<string> => {
  return axios.get(`${baseURL}/api/mis/sys/nature/getNatureDictId`)
}

// 获取所有功能权限
export const getAllFunPermission = (): Promise<OBJ_KEY_STR_ANY_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/role/allFun`)
}

export default {
  getDictList,
  getEntrusts
}
