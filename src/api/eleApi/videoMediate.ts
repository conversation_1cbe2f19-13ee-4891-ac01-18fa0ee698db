/**
 * 视频调解
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// /api/video/getMeetingRoom 获取一天的预约会议记录
export const getMeetingRoom = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/video/getMeetingRoom`, JSON.stringify(params), {
    hideLoading: true
  })
}
// /api/video/reminderMeeting 预约会议
export const reminderMeeting = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/video/reminderMeeting`, JSON.stringify(params))
}
// /api/video/getMyMeetingRoom 我的会议
export const getMyMeetingRoom = () => {
  return axios.get(`${baseURL}/api/video/getMyMeetingRoom`)
}
// /api/video/updateMeeting 修改预约会议信息
export const updateMeeting = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/video/updateMeeting`, JSON.stringify(params))
}
// /api/video/deleteMeeting/{meetingId} 删除预约会议信息
export const deleteMeeting = (meetingId: string) => {
  return axios.delete(`${baseURL}/api/video/deleteMeeting/${meetingId}`)
}
// /api/video/getMeetingRecord 分页查询视频记录
export const getMeetingRecord = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/video/getMeetingRecord`, JSON.stringify(params))
}
// /api/video/getMeetingRecordFilePath 获取视频文件下载地址
export const getMeetingRecordFilePath = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/video/getMeetingRecordFilePath`, JSON.stringify(params))
}
// /api/video/pullMeetingRecord 拉取视频记录
export const pullMeetingRecord = () => {
  return axios.get(`${baseURL}/api/video/pullMeetingRecord`)
}
// /api/video/sendSms/${meetingId} 给当事人发短信
export const sendSms = (meetingId: string) => {
  return axios.get(`${baseURL}/api/video/sendSms/${meetingId}`)
}

export default {
  getMeetingRoom,
  reminderMeeting,
  getMyMeetingRoom,
  updateMeeting,
  deleteMeeting,
  getMeetingRecord,
  getMeetingRecordFilePath,
  pullMeetingRecord,
  sendSms
}
