/**
 * 案件管理/案件分配
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案件分配分页  /api/mis/mdt/caseDistribute/pageList
export const pageListOfAllocation = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/pageList`, JSON.stringify(param), { hideLoading: true })
}

// POST /api/mis/mdt/caseDistribute/distribute 分配调解员
export const allocationMediator = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/distribute`, JSON.stringify(param))
}

// POST /api/mis/mdt/caseDistribute/autoDistribute 自动分配调解员
export const autoDistribute = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/autoDistribute`, JSON.stringify(param))
}

// GET /api/mis/mdt/caseDistribute/mediatorList 查询所有调解员
export const findAllMediator = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseDistribute/mediatorList`)
}
