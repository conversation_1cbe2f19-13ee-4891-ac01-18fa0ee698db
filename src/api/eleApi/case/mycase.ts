/**
 * 案件管理/我的案件
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// POST /api/mis/mdt/myCase/pageList 分页查询我的案件
export const pageListOfMyCase = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/myCase/pageList`, JSON.stringify(param), { hideLoading: true })
}

// GET /api/mis/mdt/caseMediate/collectRecord/getAll/{caseId} 分页获取 调解记录
export const collectRecordPage = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/collectRecord/getAll/${params.caseId}`, { hideLoading: true })
}

// POST /api/mis/mdt/caseMediate/collectRecord/save 保存 调解记录 信息
export const collectInfoSave = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/collectRecord/save`, JSON.stringify(param))
}

// DELETE /api/mis/mdt/caseMediate/contacts/delete/{contactId} 删除 案件联系人
export const deleteCaseContacts = (contactId: string) => {
  return axios.delete(`${baseURL}/api/mis/mdt/caseMediate/contacts/delete/${contactId}`)
}

// GET /api/mis/mdt/caseMediate/contacts/getAll/{caseId} 获取 案件联系人列表
export const getCaseContactsList = (caseId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/contacts/getAll/${caseId}`)
}

// POST /api/mis/mdt/caseMediate/contacts/save 保存 案件联系人 信息
export const saveCaseContactsInfo = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/contacts/save`, JSON.stringify(param))
}

// PUT /api/mis/mdt/caseMediate/contacts/updateStatus/{contactId}/{optType} 修改 案件联系人 状态
export const modifyCaseContactsStatus = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(
    `${baseURL}/api/mis/mdt/caseMediate/contacts/updateStatus/${param.contactId}/${param.optType}`,
    JSON.stringify(param)
  )
}

// GET /api/mis/mdt/caseMediate/getCaseDetail/{caseId} 获取案件详情
export const getCaseDetail = (caseId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/getCaseDetail/${caseId}`)
}

// GET /api/mis/mdt/caseMediate/getAllColor 获取所有颜色
export const getAllColor = () => {
  return axios.get(`${baseURL}/api/mis/sys/color/getAllColor`, { hideLoading: true })
}

// PUT /api/mis/mdt/caseMediate/updateCaseInfo 更新案件信息
export const updateCaseResult = ({ caseId, code }: { caseId: string; code: string }) => {
  return axios.put(`${baseURL}/api/mis/mdt/caseMediate/updateCaseResult/${caseId}/${code}`)
}

// PUT /api/mis/mdt/caseMediate/updateCaseInfo 更新案件信息
export const updateCaseInfo = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/mdt/caseMediate/updateCaseInfo`, JSON.stringify(param))
}

// GET /api/mis/mdt/call/getRegisterMassage 获取软电话注册相关信息
export const getRegisterMassage = () => {
  return axios.get(`${baseURL}/api/mis/mdt/call/getRegisterMassage`)
}

// GET /api/mis/mdt/call/hangUp 挂断
export const phoneHandUp = () => {
  return axios.get(`${baseURL}/api/mis/mdt/call/hangUp`)
}

// POST /api/mis/mdt/call/call  呼叫
export const phoneCallOut = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/call/call`, JSON.stringify(param))
}

// GET /api/mis/mdt/caseMediate/repay/listAll/{caseId}/{repayType}  查询 回款信息
export const repayDetail = (caseId: string, repayType: number) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/repay/listAll/${caseId}/${repayType}`)
}

// POST /api/mis/mdt/caseMediate/repay/save  保存 回款信息
export const saveRepay = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/repay/save`, JSON.stringify(param))
}

// DELETE /api/mis/mdt/caseMediate/repay/{repayId}删除 回款信息
export const deleteRepay = (repayId: string) => {
  return axios.delete(`${baseURL}/api/mis/mdt/caseMediate/repay/${repayId}`)
}

// GET /api/mis/mdt/caseSms/templateList 获取短信模板
export const templateList = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseSms/templateList`)
}

// POST /api/mis/mdt/case/batchCloseCase 批量结案
export const batchCloseCase = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchCloseCase`, JSON.stringify(param))
}

// POST /api/mis/mdt/case/batchAddProcess 批量加入待处理
export const batchAddProcess = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchAddProcess`, JSON.stringify(param))
}

// POST /api/mis/mdt/case/batchDeleteProcess 批量取消待处理
export const batchDeleteProcess = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchDeleteProcess`, JSON.stringify(param))
}

// POST /api/mis/mdt/case/batchDeleteAllProcess 取消所有待处理
export const batchDeleteAllProcess = () => {
  return axios.get(`${baseURL}/api/mis/mdt/case/batchDeleteAllProcess`)
}
// GET /api/mis/mdt/case/getFileType 获取文书类型
export const getFileType = () => {
  return axios.get(`${baseURL}/api/mis/mdt/case/getFileType`)
}
// GET /api/mis/mdt/case/filePageList 分页查询文书案件列表
export const filePageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/filePageList`, JSON.stringify(param))
}
// POST /api/mis/mdt/case/batchSign 批量盖章
export const batchSign = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchSign`, JSON.stringify(param))
}
// POST /api/mis/mdt/case/batchFile 批量生成文书
export const batchFile = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchFile`, JSON.stringify(param))
}
// POST  生成文书
export const generateFile = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/generateFile`, JSON.stringify(param))
}
// POST  获取盖章状态
export const getSignStatus = () => {
  return axios.get(`${baseURL}/api/mis/mdt/case/getSignStatus`)
}

//  批量导出文书
export const exportFiles = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/exportFiles`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
// POST  生成文书记录分页查询
export const batchFileRecordPage = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchFileRecordPage`, JSON.stringify(param))
}
// GET  重新生成失败的文书
export const regenerateFile = (recordId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/case/regenerateFile/${recordId}`)
}
// POST  分页查询短信发送案件列表
export const smsPageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/casePageList`, JSON.stringify(param))
}
