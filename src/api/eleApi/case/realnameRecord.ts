/**
 * 案件管理/实名记录
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// POST /api/mis/mdt/case/realNamePageList 分页查询实名记录
export const realNamePageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/realNamePageList`, JSON.stringify(param))
}
// POST /api/mis/mdt/case/exportFaceImg 批量导出刷脸图片
export const exportFaceImg = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/exportFaceImg`, JSON.stringify(param), {
    hideLoading: true,
    responseType: 'blob'
  })
}
