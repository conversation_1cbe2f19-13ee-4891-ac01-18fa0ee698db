/**
 * 案件管理/外呼记录
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// /api/mis/mdt/callRecord/pageList 分页查询呼叫记录
export const pageListOfCallRecord = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/pageList`, JSON.stringify(params), { hideLoading: true })
}

export const getCallRecord = () => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/pullRecord`)
}
// 查询电话状态
export const getStates = () => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/getStates`)
}

// /api/mis/mdt/callRecord/exportAsExcel 导出Excel文件
export const exportAsExcel = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/exportAsExcel`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
// /api/mis/mdt/callRecord/exportAsZip 导出压缩包文件
export const exportAsZip = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/exportAsZip`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}

// 查询电话状态
export const getStream = (path: string) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/getStream`, JSON.stringify(pat), {
    responseType: 'blob'
  })
}

// 刷新坐席绑定关系
export const refreshAgent = () => {
  return axios.get(`${baseURL}/api/mis/mdt/aliccCall/refreshAgent`)
}

// 获取所有外呼提供商
export const getProviderList = () => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/getProviderList`)
}
