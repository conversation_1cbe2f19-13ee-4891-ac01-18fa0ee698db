/**
 * 案件管理/导入记录
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
import Qs from 'qs'

// 导入记录分页 /api/mis/mdt/caseBatchImport/pageImpLog
export const pageListOfRecord = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseImportRecord/pageList`, JSON.stringify(param), { hideLoading: true })
}

export const downloadCaseImportRecord = (caseImportId:number ,exportType:number ) => {
	return axios.get(`${baseURL}/api/mis/mdt/caseImportRecord/download?${Qs.stringify({
		"caseImportId":caseImportId,
		"exportType":exportType
	})}`, {
		headers: { 'content-type': 'application/json; charset=utf-8' },
		responseType: 'blob'
	})
}
