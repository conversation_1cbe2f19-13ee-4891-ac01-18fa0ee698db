/**
 * 案件管理/我的案件
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// /api/mis/mdt/caseMediate/publicCase/pageList 分页查询共债案件
export const pageListOfPublicCase = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/publicCase/pageList`, JSON.stringify(param), {
    hideLoading: true
  })
}

// /api/mis/mdt/caseMediate/publicCase/{caseId} 获取共债案件
export const getPublicCase = (caseId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/publicCase/${caseId}`)
}
