/**
 * 案件管理/还款管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
// POST /api/mis/repay/repayMgr/pageList 查询 分页 还款信息
export const pageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/repay/repayMgr/pageList`, JSON.stringify(param), { hideLoading: true })
}
// POST  /api/mis/repay/repayMgr/saveRecord 保存 还款信息
export const saveRecord = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/repay/repayMgr/saveRecord`, JSON.stringify(param))
}
// DELETE /api/mis/repay/repayMgr/{repayId} 删除 还款信息
export const deleteRecord = (repayId: string) => {
  return axios.delete(`${baseURL}/api/mis/repay/repayMgr/${repayId}`)
}
// GET /api/mis/repay/repayMgr/downloadImpTmpl 下载 批量导入还款信息模版
export const downloadImpTmpl = () => {
  return axios.get(`${baseURL}/api/mis/repay/repayMgr/downloadImpTmpl`, {
    headers: { 'content-type': 'application/json; charset=utf-8' },
    responseType: 'blob'
  })
}
// POST /api/mis/repay/repayMgr/impRepayRecord 导入 批量还款信息
export const impRepayRecord = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/repay/repayMgr/impRepayRecord`, param, {
    headers: { 'Content-Type': 'multipart/form-data' },
    hideLoading: true
  })
}

// /api/mis/repay/repayMgr/exportAsExcel 导出Excel文件
export const exportAsExcel = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/repay/repayMgr/exportAsExcel`, JSON.stringify(param), {
    hideLoading: true,
    responseType: 'blob'
  })
}
