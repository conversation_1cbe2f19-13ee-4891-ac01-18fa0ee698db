/**
 * 案件管理/调解记录
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
// /api/mis/mdt/case/record/pageList 分页查询调解记录
export const pageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/pageList`, JSON.stringify(param), { hideLoading: true })
}

// /api/mis/mdt/case/record/downloadImpTmpl 下载 批量导入调解记录模版
export const downloadImpTmpl = () => {
  return axios.get(`${baseURL}/api/mis/mdt/case/record/downloadImpTmpl`, {
    headers: { 'content-type': 'application/json; charset=utf-8' },
    responseType: 'blob'
  })
}
// /api/mis/mdt/case/record/impCaseRecord 导入 批量调解记录信息
export const impCaseRecord = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/impCaseRecord`, param, {
    hideLoading: true,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// /api/mis/mdt/case/record/exportAsExcel 导出Excel文件
export const exportAsExcel = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/exportAsExcel`, JSON.stringify(param), {
    hideLoading: true,
    responseType: 'blob'
  })
}
