/**
 * 案件管理/批次管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 批次分页
export const pageListOfBatch = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseBatchMgr/pageQuery`, JSON.stringify(param), { hideLoading: true })
}
// 批次分页路径
export const batchPagePath = `${baseURL}/api/mis/mdt/caseBatchMgr/pageQuery`

// 查询所有委托方 /api/mis/common/biz/getEntrusts
export const findAllEntrusts = () => {
  return axios.get(`${baseURL}/api/mis/common/biz/getEntrusts`, { hideLoading: true })
}

// 查询所有案源方组织机构 /api/mis/common/biz/getEntrustsOrg
export const findEntrustsOrg = () => {
  return axios.get(`${baseURL}/api/mis/common/biz/getEntrustsOrg`, { hideLoading: true })
}

// 查询所有案源方组织机构 /api/mis/common/biz/getEntrustsOrg
export const findOrgStructure = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseApproval/getOrgStructure`, { hideLoading: true })
}

// 查询所有启用的金融机构
export const findAllOrgs = () => {
  return axios.get(`${baseURL}/api/mis/common/biz/getFinOrgs`)
}

// DELETE /api/mis/mdt/caseBatchMgr/deleteBatch/{batchId} 操作-删除批次
export const deleteBatch = (batchId: string) => {
  return axios.delete(`${baseURL}/api/mis/mdt/caseBatchMgr/deleteBatch/${batchId}`)
}

// GET /api/mis/mdt/caseBatchMgr/detail/{batchId} 获取批次详情
export const getBatchDetails = (batchId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseBatchMgr/detail/${batchId}`)
}

// GET /api/mis/mdt/caseBatchImport/getRule 获取自动分配要素
export const getRule = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseBatchImport/getRule`)
}

// PUT /api/mis/mdt/caseBatchMgr/quitCase/{batchId} 操作-退案
export const quitCase = (batchId: string) => {
  return axios.put(`${baseURL}/api/mis/mdt/caseBatchMgr/quitCase/${batchId}`)
}

// POST /api/mis/mdt/caseBatchMgr/save 新增/编辑 保存批次
export const saveBactch = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseBatchMgr/save`, JSON.stringify(params))
}

// POST /api/mis/mdt/caseBatchImport/importBatchCase/{bachId}/{impFlag} 批量导入案件
export const importBatchCase = (params: OBJ_KEY_STR_ANY_TYPE, bachId: string, tmplId: string, impFlag: boolean) => {
  return axios.post(
    `${baseURL}/api/mis/mdt/caseBatchImport/importBatchStgCase/${bachId}/${tmplId}/${impFlag}`,
    params,
    { headers: { 'Content-Type': 'multipart/form-data', hideLoading: true } }
  )
}
// 查询所有 案件导入模版
export const listAllTmpl = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseBatchImport/listAllTmpl`)
}

// GET /api/mis/mdt/caseBatchImport/downloadTmpl 下载模版
export const downloadBatchTmpl = (tmplId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseBatchImport/downloadStgTmpl/${tmplId}`, {
    headers: { 'content-type': 'application/json; charset=utf-8' },
    responseType: 'blob'
  })
}
