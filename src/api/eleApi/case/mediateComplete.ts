/**
 * 调解结案
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
import Qs from 'qs'
import { TokenKeyOfHeader } from '@/settings'
import { getToken } from '@/utils/auth' // getToken

// /api/mis/mdt/caseMediate/generateMdtDocumentFile/{caseId}/{documentEname} 生成案件相关的文书
export const generateMediateCompleteDoc = ({ caseId, documentEname }: { caseId: string; documentEname: string }) => {
  return axios.get(`${baseURL}/api/mis/mdt/caseMediate/generateMdtDocumentFile/${caseId}/${documentEname}`)
}

// /api/mis/mdt/caseMediate/addOrUpdateCaseDetails 添加或更新案件的details字段
export const addOrUpdateCaseDetails = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/addOrUpdateCaseDetails`, JSON.stringify(param))
}

// /api/mis/mdt/caseMediate/generateMdtDocumentFile/{caseId}/{documentEname} 修改案件相关文书的路径
export const updateFilePath = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/updateMdtDocumentFile`, JSON.stringify(params))
}

// /api/mis/mdt/caseMediate/signAgreement/{documentEname} 签署调解协议
export const signAgreement = ({
  documentEname,
  param
}: { documentEname?: string; param?: OBJ_KEY_STR_ANY_TYPE } = {}) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseMediate/signAgreement/${documentEname}`, JSON.stringify(param))
}

// /api/mis/mdt/caseMediate/getMdtDocumentFileAsPdf/{caseId}/{documentEname} 获取文书pdf的流
export const getMdtDocumentFileAsPdf = ({ caseId, documentEname }: { caseId: string; documentEname: string }) => {
  return `${baseURL}/api/mis/mdt/caseMediate/getMdtDocumentFileAsPdf/${caseId}/${documentEname}?${Qs.stringify({
    [TokenKeyOfHeader]: getToken()
  })}`
}
