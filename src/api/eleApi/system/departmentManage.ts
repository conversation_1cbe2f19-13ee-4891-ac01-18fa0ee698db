/**
 * 系统管理/部门管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 分页查询部门列表
export const pageListOfDepartment = (params: REQUEST_POST_DEPT_PAGEARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/dept/pageList`, JSON.stringify(params), { hideLoading: true })
}

// 查询所有的部门
export const getAllDepartment = () => {
  return axios.get(`${baseURL}/api/mis/auth/dept/all`)
}

// 通过id获取部门详情
export const getDepartmentInfo = (deptId: string) => {
  return axios.get(`${baseURL}/api/mis/auth/dept/${deptId}`)
}

// 删除部门
export const deleteDepartment = (deptId: string) => {
  return axios.delete(`${baseURL}/api/mis/auth/dept/${deptId}`)
}

// 添加部门
export const addDepartment = (params: REQUEST_POST_DEPT_ADDPARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/dept/add`, JSON.stringify(params))
}

// 更新部门
export const updateDepartment = (params: REQUEST_PUT_DEPT_EDITPARAM_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/dept`, JSON.stringify(params))
}

// 通过公司id获取所属部门列表详情
export const getDepartmentListByCompId = (compId: string) => {
  return axios.get(`${baseURL}/api/mis/auth/dept/compId/${compId}`)
}

export default {
  getAllDepartment,
  getDepartmentInfo,
  deleteDepartment,
  addDepartment,
  updateDepartment,
  getDepartmentListByCompId
}
