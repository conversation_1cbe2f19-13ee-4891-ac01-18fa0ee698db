/**
 * 金融机构管理接口
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 分页查询金融机构
export const pageList = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/finOrg/pageList`, JSON.stringify(params), { hideLoading: true })
}
// 新增金融机构
export const add = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/finOrg/add`, JSON.stringify(params))
}
// 修改机构状态
export const updateStatus = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/finOrg/updateStatus`, JSON.stringify(params))
}
// 修改机构
export const update = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/finOrg`, JSON.stringify(params))
}
// 通过id获取机构详情
export const getInfo = (id: string) => {
  return axios.get(`${baseURL}/api/mis/sys/finOrg/${id}`)
}
// 关联委托方
export const related = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/finOrg/related`, JSON.stringify(params))
}
// 删除金融机构
export const deleteById = (id: string) => {
  return axios.delete(`${baseURL}/api/mis/sys/finOrg/delete/${id}`)
}

export default {
  add,
  pageList,
  updateStatus,
  update,
  getInfo,
  related,
  deleteById
}
