/**
 * 文件模板信息
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

export const docTemplateFile = (documentId: string, intelligentCaseTypeId: string) => {
  return axios.get(`${baseURL}/api/mis/mdt/docTemplateFile/${documentId}/${intelligentCaseTypeId}`)
} // 获取类型模板信息及模板文件路径

export const UpdateDocTemplateFile = (params: OBJ_KEY_STR_ANY_TYPE): Promise<string> => {
  return axios.put(`${baseURL}/api/mis/mdt/docTemplateFile`, JSON.stringify(params))
} // 更新文书模板文件

export default { docTemplateFile, UpdateDocTemplateFile }
