/**
 * 系统管理/员工管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 添加员工
export const addEmployee = (params: REQUEST_POST_EMPLOYEE_ADDPARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/auth/employee/add`, JSON.stringify(params))
}
// 更新员工
export const updateEmployee = (params: REQUEST_PUT_EMPLOYEE_EDITPARAM_TYPE): Promise<string> => {
  return axios.put(`${baseURL}/api/mis/auth/employee/update`, JSON.stringify(params))
}
// 通过id获取员工详情
export const getEmployeeInfo = (id: string): Promise<REQUEST_GET_EMPLOYEE_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/mis/auth/employee/${id}`)
}
// 删除员工
export const deleteEmployee = (id: string): Promise<string> => {
  return axios.delete(`${baseURL}/api/mis/auth/employee/delete/${id}`)
}
// 分页查询员工列表
export const pageListOfEmployee = (
  params: REQUEST_POST_EMPLOYEE_LISTPARAM_TYPE
): Promise<REQUEST_POST_EMPLOYEE_LIST_TYPE> => {
  return axios.post(`${baseURL}/api/mis/auth/employee/pageList`, JSON.stringify(params), { hideLoading: true })
}
// 修改密码
export const resetPwdOfEmployee = (params: REQUEST_PUT_EMPLOYEE_PSWPARAM_TYPE): Promise<string> => {
  return axios.put(`${baseURL}/api/mis/auth/employee/resetPwd`, JSON.stringify(params))
}
// 部门列表
export const getDeptList = (): Promise<REQUEST_GET_EMPLOYEE_DEPTLIST_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/auth/employee/deptList`)
}

// 获取当前部门所有员工
export const getUserByDept = (): Promise<REQUEST_GET_EMPLOYEE_DEPTUSER_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/auth/employee/getUserByDept`)
}

export default {
  getEmployeeInfo,
  deleteEmployee,
  addEmployee,
  updateEmployee,
  pageListOfEmployee,
  resetPwdOfEmployee,
  getDeptList,
  getUserByDept
}
