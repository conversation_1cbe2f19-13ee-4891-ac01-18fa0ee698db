/**
 * 系统管理/调解团队管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 调解团队分页
export const pageListOfMediateTeam = (params: {
  companyName: string
}): Promise<REQUEST_GET_MEDIATETEAM_LIST_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/auth/company/list`, { hideLoading: true, params })
}

// 调解团队分页路径
export const mediateTeamPagePath = `${baseURL}/api/mis/auth/company/list`

// DELETE /api/mis/auth/company/deleteMediateTeam/{companyId} 操作-移除调解团队
export const deleteMediateTeam = (companyId: string) => {
  return axios.delete(`${baseURL}/api/mis/auth/company/delete/${companyId}`)
}

// DELETE /api/mis/auth/company/recover/{companyId} 操作-恢复调解团队
export const restoreMediateTeam = (companyId: string) => {
  return axios.put(`${baseURL}/api/mis/auth/company/recover/${companyId}`)
}

// GET /api/mis/auth/company/detail/{companyId} 获取调解团队详情
export const getMediateTeamDetails = (companyId: string): Promise<REQUEST_POST_MEDIATETEAM_DETAILDATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/auth/company/detail/${companyId}`)
}

// POST /api/mis/auth/company/save 新增/编辑 保存调解团队
export const saveMediateTeam = (params: FormData) => {
  return axios.put(`${baseURL}/api/mis/auth/company/update`, params, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// POST /api/mis/auth/company/add 新增调解团队
export const addMediateTeam = (params: FormData) => {
  return axios.post(`${baseURL}/api/mis/auth/company/add`, params, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// PUT /api/mis/auth/company/update 修改调解团队
export const updateMediateTeam = (params: REQUEST_POST_MEDIATETEAM_EDITPARM_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/company/update`, JSON.stringify(params))
}
