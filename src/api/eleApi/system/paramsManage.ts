/**
 * 系统管理/参数管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// PUT /api/mis/sys/sysParameter 更新系统参数
export const updateSys = (params: REQUEST_PUT_PARAM_EDITPARAM_TYPE): Promise<number> => {
  return axios.put(`${baseURL}/api/mis/sys/sysParameter`, JSON.stringify(params))
}

// GET /api/mis/sys/sysParameter/${moduleCode} 通过模块代码查询系统参数
export const getModuleCode = () => {
  return axios.get(`${baseURL}/api/mis/sys/color/list`)
}

// GET /api/mis/sys/sysParameter/${moduleCode}/${paramCode} 通过模块代码查询系统参数
export const getParamCode = (moduleCode: string, paramCode: string): Promise<REQUEST_GET_PARAM_PAEAMRES_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/${moduleCode}/${paramCode}`)
}

// POST /api/mis/mdt/caseMediate/addColor 添加颜色对应关系
export const addColor = (params: REQUEST_POST_COLOR_ADDPARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/color/saveColor`, JSON.stringify(params))
}

// PUT /api/mis/mdt/caseMediate/updateColor 修改颜色对应关系
export const updateColor = (params: REQUEST_POST_COLOR_ADDPARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/color/saveColor`, JSON.stringify(params))
}

// DELETE /api/mis/mdt/caseMediate/deleteColor 删除颜色对应关系
export const deleteColor = (colorId: string) => {
  return axios.delete(`${baseURL}/api/mis/sys/color/${colorId}`)
}

// /api/mis/sys/sysParameter/uploadLogo 上传logoImg
export const logoUpload = (params: FormData): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/sys/sysParameter/uploadLogo`, params, {
    hideLoading: true,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// GET /api/mis/sys/sysParameter/getCaseNoSequence 获取案件号生成配置
export const getCaseNoSequence = (): Promise<REQUEST_GET_PARAM_SEQUENCERES_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/getCaseNoSequence`)
}

// DELETE /api/mis/sys/sysParameter/deleteCaseNoSequence/{sequenceId} 删除案件号生成配置
export const deleteCaseNoSequence = (sequenceId: string): Promise<number> => {
  return axios.delete(`${baseURL}/api/mis/sys/sysParameter/deleteCaseNoSequence/${sequenceId}`)
}

// POST /api/mis/sys/sysParameter/saveCaseNoSequence 添加案件号生成配置
export const saveCaseNoSequence = (params: REQUEST_POST_PARAM_ADDCASENOPARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/sys/sysParameter/saveCaseNoSequence`, JSON.stringify(params))
}

// GET /api/mis/sys/sysParameter/refresh 刷新系统配置
export const refresh = (): Promise<number> => {
  return axios.get(`${baseURL}/api/mis/sys/sysParameter/refresh`)
}

export default {
  getModuleCode,
  addColor,
  deleteColor,
  updateColor,
  getParamCode,
  logoUpload,
  getCaseNoSequence,
  deleteCaseNoSequence,
  saveCaseNoSequence,
  refresh
}
