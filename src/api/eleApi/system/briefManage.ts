/**
 * 系统管理/案由管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案由分页
export const pageListOfBrief = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.get(`${baseURL}/api/mis/sys/nature/list`, { params: JSON.stringify(params), hideLoading: true })
}

// /api/mis/sys/nature 新增案由
export const newNature = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/nature`, JSON.stringify(params))
}

// /api/mis/sys/nature 修改案由
export const modifyNature = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/nature`, JSON.stringify(params))
}

// /api/mis/sys/nature/caseNatureLike 模糊查询案由列表
export const caseNatureLike = () => {
  return axios.get(`${baseURL}/api/mis/sys/nature/caseNatureLike`)
}

// /api/mis/sys/nature/listConstant 获取常用案由列表
export const getListConstant = () => {
  return axios.get(`${baseURL}/api/mis/sys/nature/listConstant`)
}

// /api/mis/sys/nature/move 移动案由
export const moveNature = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/nature/move`, JSON.stringify(params))
}

// /api/mis/sys/nature/{natureId} 通过案由ID获取案由详情
export const getNatureInfo = (natureId: string) => {
  return axios.get(`${baseURL}/api/mis/sys/nature/${natureId}`)
}

// /api/mis/sys/nature/{natureId} 通过案由ID删除案由
export const deleteNature = (natureId: string) => {
  return axios.delete(`${baseURL}/api/mis/sys/nature/${natureId}`)
}
