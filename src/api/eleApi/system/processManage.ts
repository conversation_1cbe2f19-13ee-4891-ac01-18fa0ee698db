/**
 * 系统管理/流程管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 新增流程审批
export const addProcApprMgr = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/proc/procApprMgr/add`, JSON.stringify(params))
}
// 删除流程审批设置
export const deleteProcApprMgr = ({ procApprId, operate }: { procApprId: string; operate: number }) => {
  return axios.delete(`${baseURL}/api/proc/procApprMgr/delete/${procApprId}/${operate}`)
}
// 通过id获取流程审批设置详情
export const detailProcApprMgr = (procApprId: string) => {
  return axios.get(`${baseURL}/api/proc/procApprMgr/detail/${procApprId}`)
}
// 查询所有流程审批设置配置列表
export const listAllProcApprMgr = () => {
  return axios.get(`${baseURL}/api/proc/procApprMgr/listAll`)
}
// 获取所有公司
export const listAllCompProcApprMgr = () => {
  return axios.get(`${baseURL}/api/proc/procApprMgr/listAllComp`)
}
// 获取所有部门
export const listAllDeptProcApprMgr = () => {
  return axios.get(`${baseURL}/api/proc/procApprMgr/listAllDept`)
}
// 获取所有用户
export const listAllUserProcApprMgr = () => {
  return axios.get(`${baseURL}/api/proc/procApprMgr/listAllUser`)
}
// 更新流程审批设置
export const updateProcApprMgr = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/proc/procApprMgr/update`, JSON.stringify(params))
}
// 修改流程审批启用状态
export const updateStatusProcApprMgr = ({
  procApprId,
  enable,
  operate
}: {
  procApprId: string
  enable: boolean
  operate: string
}) => {
  return axios.put(`${baseURL}/api/proc/procApprMgr/updateStatus/${procApprId}/${enable}/${operate}`)
}

export default {
  addProcApprMgr,
  listAllCompProcApprMgr,
  listAllDeptProcApprMgr,
  detailProcApprMgr,
  deleteProcApprMgr,
  updateProcApprMgr,
  listAllProcApprMgr,
  updateStatusProcApprMgr
}
