/**
 * 首页
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

import type { REQUEST_GET_CASEDETAIL_DATA_TYPE, REQUEST_GET_CASETODAY_DATA_TYPE } from './types/homeMange'

export const getCaseDetail = (): Promise<REQUEST_GET_CASEDETAIL_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/myCase/caseDetail`)
}
export const getCaseToday = (): Promise<REQUEST_GET_CASETODAY_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/myCase/getCaseToday`)
}
export const noRemindersToday = () => {
  return axios.get(`${baseURL}/api/mis/mdt/myCase/noRemindersToday`)
}

export default {
  noRemindersToday,
  getCaseDetail,
  getCaseToday
}
