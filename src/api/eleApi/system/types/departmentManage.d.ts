declare interface REQUEST_PUT_DEPT_EDITPARAM_TYPE {
  companyId: number
  deptId: number
  deptName: string
  parentDeptId: number
  remark: number
}

declare interface REQUEST_POST_DEPT_ADDPARAM_TYPE {
  companyId: number
  deptId: number
  deptName: string
  parentDeptId: number
  remark: number
}

declare interface REQUEST_POST_DEPT_PAGEARAM_TYPE {
  defaultAscs: string[]
  defaultDescs: string[]
  pageInfo: {
    orderParams: OBJ_KEY_STR_ANY_TYPE[]
    pageNumber: number
    size: number
  }
  param: {
    companyName: string
    deptId: number
    deptName: string
    parentDeptId: number
    remark: string
    treeLevel: number
  }
}
