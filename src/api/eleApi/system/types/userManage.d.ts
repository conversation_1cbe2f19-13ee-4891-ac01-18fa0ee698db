declare interface REQUEST_POST_EMPLOYEE_ADDPARAM_TYPE {
  deptId: number
  employeeAddress: string
  employeeEmail: string
  employeeGender: number
  employeeId: number
  employeeMobile: string
  employeeName: string
  employeeRoleIds: number[]
  employeeStatus: number
  loginName: string
  loginPwd: string
  mediatorFlag: true
}

declare interface REQUEST_PUT_EMPLOYEE_EDITPARAM_TYPE {
  deptId: number
  employeeAddress: string
  employeeEmail: string
  employeeGender: number
  employeeId: number
  employeeMobile: string
  employeeName: string
  employeeRoleIds: number[]
  employeeStatus: number
  loginName: string
  loginPwd: string
  mediatorFlag: true
}

declare interface REQUEST_GET_EMPLOYEE_INFO_TYPE {
  deptId: number
  employeeAddress: string
  employeeEmail: string
  employeeGender: number
  employeeId: number
  employeeMobile: string
  employeeName: string
  employeeRoleIds: number[]
  employeeStatus: number
  loginName: string
  mediatorFlag: true
}

declare interface REQUEST_POST_EMPLOYEE_LISTPARAM_TYPE {
  defaultAscs: string[]
  defaultDescs: string[]
  pageInfo: {
    orderParams: []
    pageNumber: number
    size: number
  }
  param: {
    accountId: number
    deptId: number
    deptName: string
    employeeId: number
    employeeName: string
    employeeStatus: number
    loginName: number
    roleNames: string
  }
}

declare interface REQUEST_POST_EMPLOYEE_LIST_TYPE {
  hasNext: true
  hasPrevious: true
  head: {
    additionalProp1: string
    additionalProp2: string
    additionalProp3: string
  }
  lastPageNum: number
  list: [
    {
      accountId: number
      deptId: number
      deptName: string
      employeeId: number
      employeeName: string
      employeeStatus: number
      loginName: number
      roleNames: string
    }
  ]
  pageNumber: number
  size: number
  total: number
}

declare interface REQUEST_PUT_EMPLOYEE_PSWPARAM_TYPE {
  accountId: number
  newLoginPwd: string
}

interface REQUEST_GET_EMPLOYEE_DEPTLIST_TYPE {
  companyName: string
  deptId: string
  deptName: string
  parentDeptId: string
  remark: string
  treeLevel: number
}

interface REQUEST_GET_EMPLOYEE_DEPTUSER_TYPE {
  accountId: number
  deptId: number
  deptName: string
  employeeId: number
  employeeName: string
  employeeStatus: number
  loginName: number
  roleNames: string
}
