import { getDeptEmployee } from '@/api/eleApi/system/structManage.ts'

declare type REQUEST_GET_DEPT_EMPLOYEE_TYPE = {
	deptId: number
	deptName: string
	employeeDTOList: {
		accountId: number
		deptId: number
		deptName: string
		employeeId: number
		employeeName: string
		employeeStatus: number
		loginName: string
		roleNames: string
	}[]
}[]

declare type REQUEST_GET_ORG_DEPT_TYPE = {
	companyId:number
	companyType:number
	companyName:string
	deptEmployeeDTOList:REQUEST_GET_DEPT_EMPLOYEE_TYPE
}[]
