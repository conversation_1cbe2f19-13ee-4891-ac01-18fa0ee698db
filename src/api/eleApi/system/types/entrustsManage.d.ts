declare interface REQUEST_POST_ENTRUSTS_ADDPARAM_TYPE {
  contactsName: 'string'
  entrustsAddress: 'string'
  entrustsEmail: 'string'
  entrustsId: 0
  entrustsMobile: 'string'
  entrustsName: 'string'
  entrustsStatus: true
  remark: 'string'
}

declare interface REQUEST_POST_ENTRUSTS_PAGEPARAM_TYPE {
  defaultAscs: ['string']
  defaultDescs: ['string']
  pageInfo: {
    orderParams: []
    pageNumber: 1
    size: 10
  }
  param: {
    contactsName: 'string'
    entrustsAddress: 'string'
    entrustsEmail: 'string'
    entrustsId: 0
    entrustsMobile: 'string'
    entrustsName: 'string'
    entrustsStatus: true
    remark: 'string'
  }
}

declare interface REQUEST_PUT_ENTRUSTS_EDITPARAM_TYPE {
  contactsName: 'string'
  entrustsAddress: 'string'
  entrustsEmail: 'string'
  entrustsId: 0
  entrustsMobile: 'string'
  entrustsName: 'string'
  entrustsStatus: true
  remark: 'string'
}

declare interface REQUEST_PUT_ENTRUSTS_STATUSPARAM_TYPE {
  contactsName: 'string'
  entrustsAddress: 'string'
  entrustsEmail: 'string'
  entrustsId: 0
  entrustsMobile: 'string'
  entrustsName: 'string'
  entrustsStatus: true
  remark: 'string'
}
