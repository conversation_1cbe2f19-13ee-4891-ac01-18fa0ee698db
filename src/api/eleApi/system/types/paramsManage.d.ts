declare type REQUEST_PUT_PARAM_EDITPARAM_TYPE = {
  moduleCode: string
  paramCmnt: string
  paramCode: string
  paramName: string
  paramValue: string
}[]

declare interface REQUEST_POST_PARAM_ADDCASENOPARAM_TYPE {
  digitalLength: number
  sequenceCurrent: number
  sequenceId: number
  sequenceScope: string
  sequenceTitle: string
}

declare interface REQUEST_POST_COLOR_ADDPARAM_TYPE {
  colorCode: string
  colorId: number
  mdtResult: number
  title: string
}

declare type REQUEST_GET_PARAM_SEQUENCERES_TYPE = {
  digitalLength: number
  lastValue: number
  sequenceCurrent: number
  sequenceId: number
  sequenceScope: string
  sequenceTitle: string
}[]

declare type REQUEST_GET_PARAM_PAEAMRES_TYPE = {
  moduleCode: string
  paramCmnt: string
  paramCode: string
  paramName: string
  paramValue: string
}[]
