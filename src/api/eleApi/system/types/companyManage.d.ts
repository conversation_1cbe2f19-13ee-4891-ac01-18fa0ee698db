declare interface REQUEST_GET_MEDIATETEAM_LIST_TYPE {
  deleteTime: null | number
  companyStatus: boolean
  companyIcon: string
  companyName: string
  companyId: string
  address: string
  mobile: string
  email: string
}

declare interface REQUEST_POST_MEDIATETEAM_FORMDATA_TYPE {
  accountMobile: string
  companyRemark: string
  companyName: string
  loginName: string
  companyId: string
  loginPwd: string
  address: string
  name: string
}

declare interface REQUEST_POST_MEDIATETEAM_DETAILDATA_TYPE {
  deleteTime: null | number
  companyStatus: boolean
  companyRemark: string
  companyName: string
  companyIcon: string
  companyId: string
  address: string
  mobile: string
  email: string
  authCompanyAdminDTO: {
    accountMobile: string
    loginName: string
    accountId: string
    name: string
  } | null
}

declare interface REQUEST_POST_MEDIATETEAM_ADDPARM_TYPE {
  companyRemark: string
  companyName: string
  address: string
  adminAddDTO: {
    accountMobile: string
    loginName: string
    loginPwd: string
    name: string
  }
}

declare interface REQUEST_POST_MEDIATETEAM_EDITPARM_TYPE {
  accountMobile: string
  companyRemark: string
  companyName: string
  companyId: string
  loginPwd: string
  address: string
  name: string
}
