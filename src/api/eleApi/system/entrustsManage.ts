/**
 * 委托方管理接口
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
// 添加委托方
export const add = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/entrusts/add`, JSON.stringify(params))
}
// 分页查询委托方
export const pageList = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/entrusts/pageList`, JSON.stringify(params), { hideLoading: true })
}
// 修改委托方状态
export const updateStatus = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/entrusts/updateStatus`, JSON.stringify(params))
}
// 删除委托方
export const deleteById = (deptId: string) => {
  return axios.delete(`${baseURL}/api/mis/sys/entrusts/${deptId}`)
}
// 修改委托方
export const update = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/entrusts/update`, JSON.stringify(params))
}

export default {
  add,
  pageList,
  updateStatus,
  deleteById,
  update
}
