/**
 * 案件模板接口
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
//  分页查询模板
export const pageTmpl = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/stg/caseTmplMgr/pageTmpl`, JSON.stringify(params), { hideLoading: true })
}
// 通过id获取模板详情
export const getTmpl = (tmplId: string) => {
  return axios.get(`${baseURL}/api/mis/stg/caseTmplMgr/tmplDetil/${tmplId}`)
}
// 保存编辑模板
export const saveTmpl = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/stg/caseTmplMgr/saveTmpl`, JSON.stringify(params))
}
// 删除模板
export const deleteTmpl = (tmplId: string) => {
  return axios.delete(`${baseURL}/api/mis/stg/caseTmplMgr/deleteTmpl/${tmplId}`)
}
// 删除模板字段
export const deletetmplColumn = (tmplColumnId: string) => {
  return axios.delete(`${baseURL}/api/mis/stg/caseTmplMgr/deleteTmplColumn/${tmplColumnId}`)
}
// 分页获取模板中所有字段
export const pageTmplColumns = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(
    `${baseURL}/api/mis/stg/caseTmplMgr/pageTmplColumns/${params.param.tmplId}`,
    JSON.stringify(params),
    { hideLoading: true }
  )
}
// 获取所有系统字段
export const getAllBaseColumn = () => {
  return axios.get(`${baseURL}/api/mis/stg/caseTmplMgr/getAllBaseColumn`)
}
// 保存编辑 模板字段
export const saveTmplColumn = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/stg/caseTmplMgr/saveTmplColumn`, JSON.stringify(params))
}
// 一键设置脱敏规则
export const setRule = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/stg/caseTmplMgr/setRule`, JSON.stringify(params))
}

export default {
  pageTmpl,
  getTmpl,
  saveTmpl,
  deleteTmpl,
  deletetmplColumn,
  pageTmplColumns,
  getAllBaseColumn,
  saveTmplColumn,
  setRule
}
