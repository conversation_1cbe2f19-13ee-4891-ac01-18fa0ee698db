/**
 * 系统管理/公司管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// mis获取公司信息
export const getCompanyDetails = (): Promise<REQUEST_GET_COMPANY_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/mis/auth/company/get`)
}
// mis修改公司信息
export const modifyCompanyInfo = (params: REQUEST_PUT_COMPANY_PARAM_TYPE): Promise<string> => {
  return axios.put(`${baseURL}/api/mis/auth/company/update`, JSON.stringify(params))
}

// 查询公司列表,只能查询 id>10 的公司
export const list = () => {
  return axios.get(`${baseURL}/api/auth/authCompMgr/list`, { hideLoading: true })
}
// 通过id获取公司详情
export const getCompanyInfo = (compId: string) => {
  return axios.get(`${baseURL}/api/auth/authCompMgr/${compId}`)
}
// 删除公司
export const deleteCompany = (compId: string) => {
  return axios.delete(`${baseURL}/api/auth/authCompMgr/${compId}`)
}
// 添加公司
export const addCompany = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/auth/authCompMgr`, JSON.stringify(params))
}
// 更新公司
export const updateCompany = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/auth/authCompMgr`, JSON.stringify(params))
}

export default {
  list,
  getCompanyInfo,
  deleteCompany,
  addCompany,
  updateCompany
}
