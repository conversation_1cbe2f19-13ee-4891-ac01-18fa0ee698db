/**
 * 系统管理/产品设置
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 产品设置---添加表单
export const addCaseForm = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/sys/sysProdSetup/prod/addCaseForm`, JSON.stringify(params))
}
// 产品设置---添加表单项
export const addCaseFormItem = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/sys/sysProdSetup/prod/addCaseFormItem`, JSON.stringify(params))
}
// 产品设置---添加产品
export const addCaseType = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/sys/sysProdSetup/prod/addCaseType`, JSON.stringify(params))
}
// 产品设置---改变产品类型状态
export const changeCaseTypeStatus = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/changeCaseTypeStatus`, JSON.stringify(params))
}
// 产品设置---删除表单
export const deleteCaseForm = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.delete(`${baseURL}/api/sys/sysProdSetup/prod/deleteCaseForm`, {
    data: params
  })
}
// 产品设置---删除表单项
export const deleteCaseFormItem = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.delete(`${baseURL}/api/sys/sysProdSetup/prod/deleteCaseFormItem`, {
    data: params
  })
}
// 产品设置---查询表单项
export const getCaseFormItem = ({ caseTypeId, formId }: { caseTypeId: string; formId: string }) => {
  return axios.get(`${baseURL}/api/sys/sysProdSetup/prod/getCaseFormItem/${caseTypeId}/${formId}`)
}
// 产品设置--模板配置获取模板内容
export const getCaseProdTmpl = (caseTypeId: string) => {
  return axios.get(`${baseURL}/api/sys/sysProdSetup/prod/getCaseProdTmpl/${caseTypeId}`)
}
// 产品设置--模板配置获取模板配置
export const getProdTmplConfig = () => {
  return axios.get(`${baseURL}/api/sys/sysProdSetup/prod/getProdTmplConfig`)
}
// 产品设置---下移表单
export const moveDownCaseForm = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/moveDownCaseForm`, JSON.stringify(params))
}
// 产品设置--下移表单项
export const moveDownCaseFormItem = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/moveDownCaseFormItem`, JSON.stringify(params))
}
// 产品设置---上移表单
export const moveUpCaseForm = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/moveUpCaseForm`, JSON.stringify(params))
}
// 产品设置--上移表单项
export const moveUpCaseFormItem = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/moveUpCaseFormItem`, JSON.stringify(params))
}
// 产品设置---分页查询产品列表
export const pageCaseTypeList = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/sys/sysProdSetup/prod/pageCaseTypeList`, JSON.stringify(params), {
    hideLoading: true
  })
}
// 产品设置---查询表单
export const queryCaseFrom = (caseTypeId: string) => {
  return axios.get(`${baseURL}/api/sys/sysProdSetup/prod/queryCaseFrom/${caseTypeId}`)
}
// 产品设置--模板配置保存模板
export const saveProdTmpl = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/sys/sysProdSetup/prod/saveProdTmpl`, JSON.stringify(params))
}
// 产品设置---修改表单
export const updateCaseForm = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/updateCaseForm`, JSON.stringify(params))
}
// 产品设置---更新表单项
export const updateCaseFormItem = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/updateCaseFormItem`, JSON.stringify(params))
}
// 产品设置---修改产品
export const updateCaseType = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/sys/sysProdSetup/prod/updateCaseType`, JSON.stringify(params))
}

export default {
  addCaseForm,
  addCaseFormItem,
  addCaseType,
  changeCaseTypeStatus,
  deleteCaseForm,
  deleteCaseFormItem,
  getCaseFormItem,
  getCaseProdTmpl,
  getProdTmplConfig,
  moveDownCaseForm,
  moveDownCaseFormItem,
  moveUpCaseForm,
  moveUpCaseFormItem,
  pageCaseTypeList,
  queryCaseFrom,
  saveProdTmpl,
  updateCaseForm,
  updateCaseFormItem,
  updateCaseType
}
