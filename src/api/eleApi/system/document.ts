/**
 * 文书信息
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

export const getDocumentTemplate = (): Promise<REQUEST_GET_DOC_ALLFIND_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/document/findAll`)
}

export const getCaseParam = () => {
  return axios.get(`${baseURL}/api/mis/mdt/docTemplateFile/getCaseParam`)
}
export const saveParam = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/mdt/docTemplateFile/saveParam`, JSON.stringify(params))
}
export const deleteParam = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/docTemplateFile/deleteParam`, JSON.stringify(params))
}

export default {
  getDocumentTemplate,
  getCaseParam,
  saveParam,
  deleteParam
}
