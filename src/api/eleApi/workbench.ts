/**
 * 集成工作台
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// /api/mis/mdt/aliccCall 获取阿里云呼全局参数
export const aliccCall = () => {
  return axios.get(`${baseURL}/api/mis/mdt/aliccCall`)
}

// /api/mis/mdt/aliccCall/doSend 阿里云呼API入口
export const doSend = `${baseURL}/api/mis/mdt/aliccCall/doSend`

//  刷新我的坐席
export const refreshMyAgent = () => {
  return axios.get(`${baseURL}/api/mis/mdt/aliccCall/refreshMyAgent`)
}
//  获取外呼提供商
export const getCallProvider = () => {
  return axios.get(`${baseURL}/api/mis/mdt/call/getCallProvider`)
}
//  获取腾讯坐席信息
export const getAgentMsg = () => {
  return axios.get(`${baseURL}/api/mis/mdt/tencentCall/getAgentMsg`)
}
//  保存呼叫信息
export const saveCallInfo = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/tencentCall/saveCallInfo`, JSON.stringify(params))
}
export default {
  getCallProvider,
  refreshMyAgent,
  getAgentMsg,
  aliccCall,
  doSend
}
