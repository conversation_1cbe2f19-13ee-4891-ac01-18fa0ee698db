interface COMMOM_LIST_ALLDICT_TYPE {
  dictId: number
  dictName: string
  dictStatus: number
  dictType: string
  remark: string
  sysDictDataList: {
    dictDataId: number
    dictId: number
    dictKey: number
    dictTag: string
    enableFlag: boolean
    remark: string
  }[]
  sysFlag: number | null
}

declare interface REQUEST_GET_CASENATURES_LIST_TYPE {
  children?: REQUEST_GET_CASENATURES_LIST_TYPE[]
  constantFlag: true
  description: string
  natureId: number
  parentId: number
  selectable: true
  sort: number
  title: string
}

declare interface REQUEST_GET_DEPT_DTOLIST_TYPE {
  companyName: string
  companyType: string
  treeLevel: number
  deptName: string
  remark: string
  deptId: number
  sort: number
}

declare interface REQUEST_GET_DEPT_ALLDATA_TYPE {
  deptDTOList: REQUEST_GET_DEPT_DTOLIST_TYPE[]
  groupName: string
}

declare interface REQUEST_GET_CASENATURES_DICTLIST_TYPE {
  dictDataId: string | number
  sysFlag: string | number
  dictKey: string | number
  dictId: string | number
  enableFlag: boolean
  dictTag: string
  remark: string
}

declare interface REQUEST_GET_ENTRUSTS_LIST_TYPE {
  contactsName: string
  entrustsAddress: string
  entrustsEmail: string
  entrustsId: number
  entrustsMobile: string
  entrustsName: string
  entrustsStatus: true
  remark: string
}

type COMMOM_LISTALL_DICTS_TYPE = COMMOM_LIST_ALLDICT_TYPE[]
