/**
 * 工作台/工作看板
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 工作看板接口

// POST 获取调解作业情况

export const getMediateWorkSituation = (
  params: REQUEST_GET_MDT_WORKSITUATION_PARAM_TYPE
): Promise<REQUEST_GET_MDT_WORKSITUATION_DATA_TYPE[]> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getMediateWorkSituation`, params)
}

// GET 获取调解作业排名
export const getMediationRank = (): Promise<REQUEST_GET_MDT_RANK_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseStatistics/getMediationRank`, { hideLoading: true })
}

// GET 获取工作面板统计信息
export const getTaskBoardStatistics = (): Promise<REQUEST_GET_TASKBOARDSTATISTICS_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseStatistics/getTaskBoardStatistics`, { hideLoading: true })
}
