declare interface REQUEST_GET_MDT_WORKSITUATION_DATA_TYPE {
  xAxis: string
  yAxis: string
  type: string | number
}

declare interface REQUEST_GET_MDT_WORKSITUATION_PARAM_TYPE {
  dateEnd?: string
  dateStart?: string
  type?: number
}

declare interface REQUEST_GET_MDT_RANK_DATA_TYPE {
  averageProcessingCycle: number
  orgName: string
  pendAmount: number
  rank: number
  successRate: number
}

declare interface REQUEST_GET_TASKBOARDSTATISTICS_DATA_TYPE {
  caseApprovalCloseCount: number
  caseApprovalDelayCount: number
  caseCloseAmount: number
  caseFollowingAmount: number
  caseNearTimeoutAmount: number
  casePendingAmount: number
  caseSubjectMatterSum: number
  caseWorkingAmount: number
  failRate: string
  failCount: number
  monthlySuccessCounts: REQUEST_GET_MDT_WORKSITUATION_DATA_TYPE[]
  successCount: number
  successRate: string
}
