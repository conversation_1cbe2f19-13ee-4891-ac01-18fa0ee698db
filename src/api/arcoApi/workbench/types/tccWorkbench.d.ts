declare interface REQUEST_POST_TENCENTCALL_AGENT_INFO_TYPE {
  appId: string
  sdkURL: string
  token: string
  userId: string
}

declare interface REQUEST_POST_TENCENTCALL_PARAM_INFO_TYPE {
  appId: string
  secretId: string
  secretKey: string
}

declare interface REQUEST_POST_TENCENTCALL_CASE_INFO_TYPE {
  caseId: string | number
  calleeName: string
	litigantIdList: (string|number)[]
}

declare interface REQUEST_POST_TENCENTCALL_SAVE_INFO_TYPE {
  caseId: string | number
  mediatorName: string
  calleeName: string
  mediatorId: strng
  sessionId: string
  phone: string,
	litigantIdList: (string|number)[]
}
