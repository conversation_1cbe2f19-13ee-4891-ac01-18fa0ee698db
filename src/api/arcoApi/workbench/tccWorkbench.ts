/**
 * 集成工作台
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// GET  获取腾讯坐席信息
export const getAgentMsg = (): Promise<REQUEST_POST_TENCENTCALL_AGENT_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/tencentCall/getAgentMsg`)
}

// POST 通过手机号获取案件信息
export const getCaseInfo = (params: FormData): Promise<REQUEST_POST_TENCENTCALL_CASE_INFO_TYPE> => {
  return axios.post(`${baseURL}/api/mis/mdt/tencentCall/getCaseId`, params, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// GET 获取腾讯云呼全局参数
export const getMsg = (): Promise<REQUEST_POST_TENCENTCALL_PARAM_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/tencentCall/getMsg`)
}

//  保存呼叫信息
export const saveCallInfo = (params: REQUEST_POST_TENCENTCALL_SAVE_INFO_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/tencentCall/saveCallInfo`, JSON.stringify(params))
}

export default {
  saveCallInfo,
  getAgentMsg
}
