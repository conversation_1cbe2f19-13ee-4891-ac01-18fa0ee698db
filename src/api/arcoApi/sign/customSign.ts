/**
 * 电子签名
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 签名相关接口

// GET 获取会议的笔录ID
export const getTrailRecordId = (meetingId: string): Promise<string> =>
  axios.get(`${baseURL}/api/meeting/control/getTrailRecordId/${meetingId}`)

// POST 生成庭审笔录签名二维码
export const newCreateQrCode = (params: { filePath: string }): Promise<string> =>
  axios.post(`${baseURL}/api/meeting/doc/sign/newCreateQrCode`, JSON.stringify(params))

// GET 获取庭审笔录签名二维码
export const newGetQrCode = (filePath: string): Promise<REQUEST_GET_QRCODE_RESULT_TYPE> =>
  axios.get(`${baseURL}/api/meeting/doc/sign/newGetQrCode?filePath=${filePath}`)

// PUT 完成签名
export const completeSign = (filePath: string): Promise<string> =>
  axios.put(`${baseURL}/api/meeting/doc/sign/completeSign?filePath=${filePath}`)

// PUT 补充签名
export const supplementSign = (filePath: string): Promise<string> =>
  axios.put(`${baseURL}/api/meeting/doc/sign/supplementSign?filePath=${filePath}`)

// POST 重新签名
export const restartSign = (filePath: string, meetingId: string): Promise<string> =>
  axios.post(`${baseURL}/api/meeting/doc/sign/restartSign?filePath=${filePath}&meetingId=${meetingId}`)

// POST 通知后台并获得认证结果
export const endNotice = (bizToken: string, signFilePath: string): Promise<REQUEST_POST_NOTICE_RESULT_TYPE> =>
  axios.post(`${baseURL}/api/meeting/doc/signOpen/endNotice/${bizToken}?signFilePath=${signFilePath}`)

// POST 第一步：实名认证-当前登陆人申请进入房间实名认证
export const applyRealNameAuth = (params: REQUEST_POST_REALNAMEAUTH_PARAM_TYPE): Promise<string> =>
  axios.post(`${baseURL}/api/auth/realName/applyRealName`, JSON.stringify(params))

// POST 实名认证-认证结束通知后台并获取认证结果
export const realNameAuthNotice = (bizToken: string): Promise<REQUEST_POST_REALNAMEAUTH_NOTICE_PARAM_TYPE> =>
  axios.post(`${baseURL}/api/mis/sctvis/main/endNotice/${bizToken}`)

// POST 检测签名文件和签名信息
export const newCheck = (params: REQUEST_POST_CHECK_SIGN_PARAM_TYPE): Promise<string> =>
  axios.post(`${baseURL}/api/mis/sctvis/main/newCheck`, JSON.stringify(params))

// GET 获取当前用户实名认证结果
export const getRealNameAuthResults = (bizToken: string): Promise<string> =>
  axios.get(`${baseURL}/api/mis/sctvis/main/getRealNameResult/${bizToken}`)

//  预览待签名文件
export const readFiles = (authKey: string, filePath: string): string => {
  return `${baseURL}/api/mis/sctvis/main/readFiles${encodeURI(filePath)}?authKey=${authKey}`
}

// GET 获取已有签名图片
export const getExistSignImage = (authKey: string): Promise<string> =>
  axios.get(`${baseURL}/api/mis/sctvis/main/getExistSignImage?authKey=${authKey}`)

// POST 签名发送短信验证码
export const sendSignSmsCode = (
  params: REQUEST_POST_SEND_SIGNSMS_PARAM_TYPE
): Promise<{ mobile: string; sendResult: boolean }> =>
  axios.post(`${baseURL}/api/mis/sctvis/main/sendSignSmsCode`, JSON.stringify(params), { hideLoading: true })

// POST 拒绝签名
export const rejectSign = (params: REQUEST_POST_REJECT_SIGN_PARAM_TYPE): Promise<string> =>
  axios.post(`${baseURL}/api/mis/sctvis/main/rejectSign`, JSON.stringify(params))

// POST 上传签名文件并签名
export const uploadSignImageAndSignFile = (
  param: REQUEST_POST_UPLOAD_SIGNIMGANDFILE_TYPE,
  formData: FormData
): Promise<void> => {
  return axios.post(`${baseURL}/api/mis/sctvis/main/uploadSignImageAndSignFile`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    params: param
  })
}
