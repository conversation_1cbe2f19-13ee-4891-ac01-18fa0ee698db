declare interface REQUEST_GET_QRCODE_RESULT_TYPE {
  base64QrCode: string // 二维码跳转
  content: string // 二维码跳转目标
}

declare interface REQUEST_POST_NOTICE_RESULT_TYPE {
  platformId: number
  realnameId: number
  success: boolean
  newToken: string
  refType: number
  errMsg: string
  refId: string
}

declare interface REQUEST_POST_CHECK_SIGN_PARAM_TYPE {
  encryptContent: string
  filePath: string
}

declare interface REQUEST_POST_REJECT_SIGN_PARAM_TYPE {
  signFilePath: string
  rejectReason: string
  authKey: string
}

declare interface REQUEST_POST_SEND_SIGNSMS_PARAM_TYPE {
  filePath: string
  authKey: string
  mobile: string
}

declare interface REQUEST_POST_UPLOAD_SIGNIMGANDFILE_TYPE {
  signFilePath: string
  authKey: string
  smsCode: string
}

declare interface REQUEST_POST_REALNAMEAUTH_PARAM_TYPE {
  refId: number
  refType: number
}

declare interface REQUEST_POST_REALNAMEAUTH_NOTICE_PARAM_TYPE {
  platformId?: number
  realnameId?: number
  success: boolean
  idcardNo?: string
  filePath?: string
  refType?: number
  newToken?: string
  errMsg: string
  mobile?: string
  refId2?: string
  refId?: string
}
