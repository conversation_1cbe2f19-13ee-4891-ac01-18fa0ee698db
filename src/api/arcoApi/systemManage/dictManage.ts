/**
 * 系统管理/字典管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http'
import { REQUEST_GET_DICT_LIST_TYPE } from '@/api/arcoApi/systemManage/types/dictManage' // 导入http中创建的axios实例

// 获取所有的字典类型+字典值树形
export const listAllTreeData = ():Promise<REQUEST_GET_DICT_LIST_TYPE[]> => {
	return axios.post(`${baseURL}/api/mis/sysClientDictMgr/listAllTreeData`,{ hideLoading: true })
}

// 分页查询用户字典列表
export const pageList = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sysClientDictMgr/pageList`, JSON.stringify(params), { hideLoading: true })
}

// 添加用户字典数据,用户字典存在才可添加字典数据
export const addVal = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/v1/sys/sysClientDictDataMgr`, JSON.stringify(params))
}
// 更新用户字典
export const updateVal = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/v1/sys/sysClientDictDataMgr`, JSON.stringify(params))
}
// 更新用户字典
export const update = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sysClientDictMgr`, JSON.stringify(params))
}
// 删除用户字典值
export const deleteById = (dictDataId: string) => {
  return axios.delete(`${baseURL}/api/v1/sys/sysClientDictDataMgr/${dictDataId}`)
}
// 根据用户字典ID获取用户字典数据列表
export const listByDictId = (dictId: string|number) => {
  return axios.get(`${baseURL}/api/v1/sys/sysClientDictDataMgr/listByDictId/${dictId}`)
}
