/**
 * 系统管理/操作日志
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 操作日志管理接口

// POST 分页查询操作日志列表
export const getOperationLogList = (
  params: REQUEST_POST_OPERATIONLOG_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_OPERATIONLOG_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/auth/operateLog/pageList`, params)
}

// GET 查询案件操作记录
export const byCaseIdGetOperationLog = (caseId: string): Promise<REQUEST_GET_OPERATIONLOG_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/auth/operateLog/list/${caseId}`, { hideLoading: true })
}

// GET 根据ID查询操作日志请求参数
export const byLogIdGetOperationLog = (logId : string): Promise<string> => {
  return axios.get(`${baseURL}/api/mis/auth/operateLog/getLogParam/${logId}`)
}
