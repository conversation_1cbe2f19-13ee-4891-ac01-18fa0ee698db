import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http'

// 分页查询用户字典列表
export const businessTypeTree = () => {
  return axios.get(`${baseURL}/api/business/type/tree`,{ hideLoading: true })
}


// 提交业务类型 新增/修改
export const submitBusinessType = (businessType:BUSINESS_TYPE) => {
  return axios.post(`${baseURL}/api/business/type`,JSON.stringify(businessType),{ hideLoading: false })
}

