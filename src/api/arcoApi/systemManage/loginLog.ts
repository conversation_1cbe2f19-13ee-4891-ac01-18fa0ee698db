/**
 * 系统管理/登录日志
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 登录日志管理接口

// POST 分页查询登录日志列表
export const getLoginLogList = (
  params: REQUEST_POST_LOGINLOG_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_LOGINLOG_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/auth/loginLog/pageList`, params)
}
