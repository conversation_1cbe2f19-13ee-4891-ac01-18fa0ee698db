import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 根据公司ID查询配置
export const getByCompanyId = (companyId: number|string): Promise<REQUEST_COMPANY_SETTING_DETAIL_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mdt/companySetting/getByCompanyId`, { params: { companyId } })
}

// 保存公司配置
export const saveCompanySetting = (params: REQUEST_COMPANY_SETTING_DATA_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/companySetting/save`, JSON.stringify(params))
}



export default {
  getByCompanyId,
  saveCompanySetting
}
