/**
 * 系统管理/角色管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 新增角色
export const addRole = (params: REQUEST_ROLE_DATA_TYPE) => {
  return axios.post(`${baseURL}/api/mis/sys/role/add`, JSON.stringify(params))
}

// 获取所有有效功能权限-新
export const allFunNew = (): Promise<REQUEST_GET_ROLE_AUTH_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/role/allFunNew`)
}

// 获取所有的角色
export const getAll = (): Promise<REQUEST_GET_ROLE_LIST_ALLDATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/role/getAll`)
}

// 根据角色归属获取的角色列表
export const listByGroupName = (params: { groupName: string }): Promise<REQUEST_ROLE_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/role/listByGroupName`, { params })
}

// 修改角色
export const updateRole = (params: REQUEST_ROLE_DATA_TYPE) => {
  return axios.put(`${baseURL}/api/mis/sys/role/update`, JSON.stringify(params))
}

// 修改角色功能权限
export const updateRolePermisiion = (params: { menuIds: string[]; roleId: string }) => {
  return axios.put(`${baseURL}/api/mis/sys/role/updateFun`, JSON.stringify(params))
}

// 查看角色详情
export const getRoleDetail = (id: string | number): Promise<REQUEST_GET_ROLE_DETAILS_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/role/${id}`)
}

// 删除角色
export const deleteRole = (
  id: string | number
): Promise<{ code: number; message: string; result: string; success: boolean }> => {
  return axios.delete(`${baseURL}/api/mis/sys/role/${id}`)
}

export default {
  updateRolePermisiion,
  listByGroupName,
  getRoleDetail,
  updateRole,
  deleteRole,
  allFunNew,
  addRole,
  getAll
}
