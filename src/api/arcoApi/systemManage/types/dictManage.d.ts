declare interface DICT_MANAGE_SEARCH_FORM_TYPE {
  dictName: string
}

declare interface REQUEST_GET_DICT_LIST_TYPE {
  dictId: number | null
  dictName: string
  dictStatus: string | null
  dictType: string
  remark: string
  sysDictDataList: REQUEST_GET_DICT_VALUE_LIST_TYPE[]
  sysFlag: boolean | null
}

declare interface REQUEST_GET_DICT_VALUE_LIST_TYPE {
  dictDataId: number | string
  dictId: number | string
  dictKey: number
  dictTag: string
  enableFlag: boolean | number
	parentId: number | null
  remark: string
	businessType?:number
	businessTypeName?:string
	children: REQUEST_GET_DICT_VALUE_LIST_TYPE[]
}

declare interface DICT_VALUE_EDIT_FORM_TYPE {
  dictDataId?: number | string
	parentId?: number | null
  dictId: number | string
  dictKey?: number | null
  dictTag: string
  enableFlag: boolean
  remark: string
	businessType?:string
}

