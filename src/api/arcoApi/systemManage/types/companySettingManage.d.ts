declare interface IpWhitelist {
  name: string
  ip: string
}

declare interface WhiteLocation {
  name: string
  longitude: string
  latitude: string
}

declare interface REQUEST_COMPANY_SETTING_DATA_TYPE {
  settingId?: number
  companyId: number|string
  aiSwitch: boolean
  ipWhitelistList?: <PERSON><PERSON><PERSON><PERSON>elist[]
  whiteLocationList?: WhiteLocation[]
}

declare interface REQUEST_COMPANY_SETTING_DETAIL_DATA_TYPE {
  settingId: number
  companyId: number|string
  aiSwitch: boolean
  ipWhitelistList?: IpWhitelist[]
  whiteLocationList?: WhiteLocation[]
}
