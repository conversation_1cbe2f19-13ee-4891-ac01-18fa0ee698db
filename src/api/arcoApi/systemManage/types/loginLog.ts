declare interface REQUEST_POST_LOGINLOG_LISTSEARCH_PARAM_TYPE {
  accountId: string | number
  creatorId: string | number
  orgId: string | number
  employeeName: string
  optDateStart: string
  optDateEnd: string
  loginName: string
  requestIp: string
  allTime?: string[]
}

declare interface REQUEST_POST_LOGINLOG_LIST_PARAM_TYPE {
  param?: REQUEST_POST_LOGINLOG_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_LOGINLOG_LIST_DATA_TYPE {
  loginLogId: string | number
  creatorId: string | number
  orgId: string | number
  employeeName: string
  loginName: string
  requestIp: string
  orgName: string
  longitude?: string
  latitude?: string
  loginTime?: string
}
