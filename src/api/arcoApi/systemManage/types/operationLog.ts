declare interface REQUEST_POST_OPERATIONLOG_LISTSEARCH_PARAM_TYPE {
  entrustsDeptIdList: (string | number)[]
  accountDeptIdList: (string | number)[]
  accountOrgIdList: (string | number)[]
  accountIdList: (string | number)[]
  entrustsIdList: (string | number)[]
  accountName: string | number
  accountId: string | number
  optDateStart: string
  operateType: string
  optDateEnd: string
  requestIp: string
  logTitle: string
  caseNo: string
  allTime?: (string | number)[]
	caseId?:string | number
}

declare interface REQUEST_POST_OPERATIONLOG_LIST_PARAM_TYPE {
  param?: REQUEST_POST_OPERATIONLOG_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_OPERATIONLOG_LIST_DATA_TYPE {
  accountDeptId: string | number
  accountOrgId: string | number
  companyId: string | number
  accountId: string | number
  creatorId: string | number
  entrustsDeptName: string
  accountDeptName: string
  accountOrgName: string
  entrustsDeptId: string
  logId: string | number
  entrustsName: string
  accountName: string
  operateType: string
  requestUrl: string
  entrustsId: string
  createTime: string
  requestIp: string
  logParam: string
  logTitle: string
  endTime: string
  caseId: string
  caseNo: string
}
