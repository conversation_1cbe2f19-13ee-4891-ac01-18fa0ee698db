declare interface EMPLOYEE_DEPARTMENT_SEARCH_FORM_TYPE {
  employeeNumber: string
  employeeName: string
  employeeMobile: string
  roleIdList: never[]
  accountStatus: string
}

declare interface EMPLOYEE_DEPARTMENT_EDIT_FORM_TYPE {
  employeeId: number | string
  employeeName: string
  employeeNumber: string
  employeeMobile: string
  deptId: string
  employeeRoleIds: never[]
  accountStatus: boolean
  ipConfig: string[]
}

declare interface EMPLOYEE_DEPARTMENT_PASSWORD_FORM_TYPE {
  accountId: number | string
  employeeName: string
  newLoginPwd: string
  confirmPassword: string
}

declare interface REQUEST_GET_EMPLOYEE_DEPARTMENT_LIST_TYPE {
  accountId: number | string
  accountStatus: number
  deptName: string
  employeeId: number | string
  employeeMobile: string
  employeeName: string
  employeeNumber: string
  roleNames: string
}