declare interface REQUEST_ROLE_DATA_TYPE {
  groupName: string
  isSystem: boolean
  roleId: string
  roleName: string
  roleRemark: string
}

declare interface REQUEST_GET_ROLE_DETAILS_DATA_TYPE {
  groupName: string
  menuIds: (string | number)[]
  roleId: string
  roleName: string
  roleRemark: string
}

declare interface REQUEST_GET_ROLE_AUTH_DATA_TYPE {
  children: REQUEST_GET_ROLE_AUTH_DATA_TYPE[]
  component: string
  icon: string
  level: number
  menuId: string
  menuType: number
  meta: {
    hideChildrenInMenu: boolean
    hideInMenu: boolean
    icon: string
    keepAlive: boolean
    locale: string
    multipage: string
    order: number
    requiresAuth: boolean
    title: string
  }
  name: string
  parentMenuId: string
  path: string
  redirect: string
}

declare interface REQUEST_GET_ROLE_LIST_ALLDATA_TYPE {
  groupName: string
  roles: REQUEST_ROLE_DATA_TYPE[]
}
