declare interface LOGINAPPEARANCE_FILE_DATA_TYPE {
  file: File | string
  fileType: string
}

declare interface REQUEST_POST_LOGINAPPEARANCE_ADD_PARAM_TYPE {
  appearanceEnableStatus: string | number
  backgroundFile: any
  navigationFile: any
  appearanceId: string | number
  appearanceDomainName: string
  appearanceUserName: string
  brandFile: any
  tabFile: any
  backgroundPath?: string
  navigationPath?: string
  navigationName: string
  appearanceName: string
  appearancePath: string
  appearanceUrl: string
  loginBoxTitle: string
  brandTitle: string
  brandPath?: string
  tabPath?: string
  tabName: string
}

declare interface REQUEST_POST_LOGINAPPEARANCE_RESULT_TYPE {
  appearanceEnableStatus: string | number
  appearanceId: string | number
  appearanceDomainName: string
  appearanceUserName: string
  creatorId: string | number
  isDeleted: string | number
  updaterId: string | number
  appearanceName: string
  appearancePath: string
  backgroundPath: string
  appearanceUrl: string
  navigationName: string
  navigationPath: string
  loginBoxTitle: string
  creatorName: string
  updaterName: string
  brandTitle: string
  createTime: string
  updateTime: string
  brandPath: string
  tabName: string
  tabPath: string
}

declare interface REQUEST_POST_LOGINAPPEARANCE_LISTSEARCH_PARAM_TYPE {
  appearanceEnableStatus: string | number
  appearanceId: string | number
  appearanceDomainName: string
  appearanceUserName: string
  navigationName: string
  appearanceName: string
  appearancePath: string
  appearanceUrl: string
  loginBoxTitle: string
  updaterName: string
  brandTitle: string
  tabName: string
}

declare interface REQUEST_POST_LOGINAPPEARANCE_LIST_PARAM_TYPE {
  param?: REQUEST_POST_LOGINAPPEARANCE_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_LOGINAPPEARANCE_LIST_DATA_TYPE {
  appearanceDomainName: string
  appearanceEnableStatus: string | number
  appearanceId: string | number
  appearanceName: string
  appearancePath: string
  appearanceUrl: string
  appearanceUserName: string
  backgroundPath: string
  brandPath: string
  brandTitle: string
  createTime: string
  creatorId: string | number
  creatorName: string
  isDeleted: string | number
  loginBoxTitle: string
  navigationName: string
  navigationPath: string
  tabName: string
  tabPath: string
  updateTime: string
  updaterId: string | number
  updaterName: string
}

declare interface REQUEST_GET_LOGINAPPEARANCE_UPDATE_TYPE {
  appearanceEnableStatus: string | number
  backgroundFile: any
  navigationFile: any
  appearanceId: string | number
  appearanceDomainName: string
  appearanceUserName: string
  brandFile: any
  tabFile: any
  backgroundPath?: string
  navigationPath?: string
  navigationName: string
  appearanceName: string
  appearancePath: string
  appearanceUrl: string
  loginBoxTitle: string
  brandTitle: string
  brandPath?: string
  tabPath?: string
  tabName: string
}
