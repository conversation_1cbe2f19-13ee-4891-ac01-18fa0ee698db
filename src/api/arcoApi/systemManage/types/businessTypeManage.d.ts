declare interface BUSINESS_TYPE {
  businessTypeId: number | null,
  businessTypeName: string,
  description: string,
  status: number,
  parentId: number | null,
  createTime: string | null,
  processTypeList: number[]
  children: BUSINESS_TYPE[] | null
}

/**
 * 系统业务类型对象 - 对应Java实体类 SysBusinessType
 */
declare interface SysBusinessType {
  /** 业务类型id */
  businessTypeId: number | null;

  /** 业务类型名称 */
  businessTypeName: string;

  /** 描述 */
  description: string;

  /**
   * 办结流程
   * @see CaseApprovalTypeEnum
   */
  processTypeList: number[];

  /** 业务状态 */
  status: number;

  /** 父级id */
  parentId: number | null;

  /** 创建时间 */
  createTime: string | null;

  /** 创建人 */
  creatorId: number | null;

  /** 更新时间 */
  updateTime: string | null;

  /** 更新人 */
  updaterId: number | null;
}
