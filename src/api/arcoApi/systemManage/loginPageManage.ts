/**
 * 系统设置/登录页管理
 */

import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 登录页管理

// POST 新增登录页外观数据
export const addLoginAppearance = (param: FormData): Promise<REQUEST_POST_LOGINAPPEARANCE_RESULT_TYPE> => {
  return axios.post(`${baseURL}/api/mis/sys/loginAppearance/add`, param, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// POST 登录页自定义外观分页查询
export const findLoginAppearanceList = (
  param: REQUEST_POST_LOGINAPPEARANCE_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_LOGINAPPEARANCE_LIST_DATA_TYPE[]; total: number }> => {
  param = removeNullParam(param)
  return axios.post(`${baseURL}/api/mis/sys/loginAppearance/pageList`, JSON.stringify(param), {
    hideLoading: true
  })
}

// POST 修改登录页外观数据
export const updateLoginAppearance = (param: any) => {
  return axios.post(`${baseURL}/api/mis/sys/loginAppearance/update`, param, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// PUT 修改启用状态
export const updateLoginAppearancetate = (appearanceId: string | number) => {
  return axios.put(`${baseURL}/api/mis/sys/loginAppearance/updateEnableStatus/${appearanceId}`)
}

// DELETE 通过主键删除数据
export const deleteLoginAppearance = (appearanceId: string | number) => {
  return axios.delete(`${baseURL}/api/mis/sys/loginAppearance/${appearanceId}`)
}

// GET 通过ID查询单条数据
export const byIdfindSingleLoginAppearance = (
  appearanceId: string | number
): Promise<REQUEST_POST_LOGINAPPEARANCE_RESULT_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/loginAppearance/${appearanceId}`)
}
