/**
 * 系统管理/成员和部门管理
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 通过企业类型获取公司列表
export const listByCompanyType = (params: OBJ_KEY_STR_ANY_TYPE): Promise<any[]> => {
  return axios.get(`${baseURL}/api/mis/auth/company/listByCompanyType`, { params })
}

// 获取平台方公司
export const getPlantFormCompany = (): Promise<any[]> => {
  return axios.get(`${baseURL}/api/mis/auth/company/getPlantFormCompany`)
}

// 修改部门
export const updateDept = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/dept`, JSON.stringify(params))
}

// 添加部门
export const addDept = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/dept/add`, JSON.stringify(params))
}

// 部门列表
export const deptList = (companyId: string | number): Promise<any[]> => {
  return axios.get(`${baseURL}/api/mis/auth/dept/deptList/${companyId}`)
}

// 移动部门
export const moveDept = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/dept/move`, JSON.stringify(params))
}

// 删除部门
export const deleteDept = (id: string | number) => {
  return axios.delete(`${baseURL}/api/mis/auth/dept/${id}`)
}

// 添加员工
export const addEmployee = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/employee/add`, JSON.stringify(params))
}

// 删除员工
export const deleteEmployee = (id: string | number) => {
  return axios.delete(`${baseURL}/api/mis/auth/employee/delete/${id}`)
}

// 分页查询员工信息
export const pageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/auth/employee/pageList`, JSON.stringify(param), {
    hideLoading: true
  })
}

// 重置密码
export const resetPwd = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/employee/resetPwd`, JSON.stringify(params))
}

// 修改员工
export const updateEmployee = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/employee/update`, JSON.stringify(params))
}

// 校验账号是否为关联账号
export const checkRelevanceAccount = (params: string) => {
  return axios.put(`${baseURL}/api/mis/auth/employee/checkRelevanceAccount`, {employeeNumber:params},{
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 修改账号状态
export const updateAccountStatus = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.put(`${baseURL}/api/mis/auth/employee/updateAccountStatus`, JSON.stringify(params))
}

// 通过id获取员工详情
export const getEmployeeDetail = (id: string | number) => {
  return axios.get(`${baseURL}/api/mis/auth/employee/${id}`)
}

// 根据部门id获取员工信息列表
export const getListByDeptId = (params: {
  companyId: string | number
  deptId: string | number
}): Promise<REQUEST_POST_COMMON_MEDIATOR_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/auth/employee/getListByDeptId`, { params })
}

export default {
  getPlantFormCompany,
  updateAccountStatus,
  getEmployeeDetail,
  listByCompanyType,
  updateEmployee,
  getListByDeptId,
  deleteEmployee,
  addEmployee,
  deleteDept,
  updateDept,
  moveDept,
  pageList,
  resetPwd,
  deptList,
  addDept
}
