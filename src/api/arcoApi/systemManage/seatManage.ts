/**
 * 系统管理/坐席管理
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 坐席管理接口

// POST 腾讯坐席保存
export const saveSeat = (param: REQUEST_POST_SEAT_SAVE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callConfig/ten/save`, JSON.stringify(param), {
    hideLoading: true
  })
}

// POST 腾讯坐席列表
export const getSeatList = (): Promise<REQUEST_GET_SEAT_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/callConfig/ten/list`)
}
