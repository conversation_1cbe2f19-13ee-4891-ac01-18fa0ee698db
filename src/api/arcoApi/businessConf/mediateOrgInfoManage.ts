/**
 * 业务配置/调解组织信息管理
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 调解组织信息管理接口

// POST 分页查询调解组织列表
export const getOrgPageList = (
  params: REQUEST_POST_ORG_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_ORG_LIST_DATA_TYPE[]; total: number }> => {
  return axios.post(`${baseURL}/api/mis/sys/mdtOrg/pageList`, params)
}

// POST  添加调解组织信息
export const addOrgInfo = (params: REQUEST_POST_ORG_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/sys/mdtOrg/add`, JSON.stringify(params))
}

// POST 修改调解组织信息
export const editOrgInfo = (params: REQUEST_POST_ORG_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.put(`${baseURL}/api/mis/sys/mdtOrg`, JSON.stringify(params))
}

// DELETE删除调解组织
export const deleteOrg = (id: number): Promise<string> => {
  return axios.delete(`${baseURL}/api/mis/sys/mdtOrg/delete/${id}`)
}

// GET获取所有委托方
export const getAllMdtOrg = (): Promise<REQUEST_GET_ORG_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/mdtOrg/getAll`)
}

// GET 通过id查询调解组织
export const getOrgDetails = (id: number): Promise<REQUEST_POST_ORG_SAVE_PARAM_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/mdtOrg/${id}`)
}

export default {
  getOrgPageList,
  getOrgDetails,
  getAllMdtOrg,
  addOrgInfo,
  deleteOrg
}
