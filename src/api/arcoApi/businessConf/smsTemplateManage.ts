/**
 * 模板管理/短信模板管理
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// POST 分页查询短信模板
export const smsTempPage = (
  params: RequestPostSmsTempListParamType
): Promise<{ list: RequestGetSmstempListDataType[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/smsTempPage`, params)
}

// 保存短信模板
export const saveSmsTemplate = (params: RequestPostSmsTempSaveParamType): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/saveSmsTemp`, JSON.stringify(params))
}

// 保存短信模板状态
export const saveTempStatus = (params: RequestPostSmsTempStatusParamType): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/saveSmsTempStaus`, JSON.stringify(params))
}

// 保存短信模板参数
export const saveTempParam = (params: RequestPostSmsTempParamSaveType): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/saveTempParam`, JSON.stringify(params))
}

//GET 根据id获取短信模板
export const byIdGetSmstemplate = (id: number): Promise<RequestPostSmsTempResultInfoType> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseSms/getCaseSmsTemplate/${id}`)
}

//GET 查询短信模板参数
export const byIdGetSmstemplateParam = (id: number): Promise<RequestPostSmsTempParamSaveType[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseSms/getTempParam/${id}`)
}
