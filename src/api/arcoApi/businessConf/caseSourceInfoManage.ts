/**
 * 业务配置/案源方信息管理
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案源方信息管理接口

// POST 分页查询案源方列表
export const getEntrustsList = (
  params: REQUEST_POST_ENTRUSTS_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/sys/entrusts/pageList`, params)
}

// POST  添加案源方信息
export const addEntrustsInfo = (params: REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/sys/entrusts/add`, JSON.stringify(params))
}

// POST 修改案源方信息
export const editEntrustsInfo = (params: REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.put(`${baseURL}/api/mis/sys/entrusts/update`, JSON.stringify(params))
}

// DELETE删除案源方
export const deleteEntrusts = (id: number): Promise<string> => {
  return axios.delete(`${baseURL}/api/mis/sys/entrusts/${id}`)
}

// GET获取所有委托方
export const getAllEntrusts = (): Promise<REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/getAll`)
}

// GET 通过id查询案源方
export const getEntrustsDetails = (id: number): Promise<REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/${id}`)
}

// GET /api/mis/sys/entrusts/getDeptInfoById/{entrustsId} 通过案源方id获取部门信息
export const getDeptInfoById = (entrustsId: string | number): Promise<REQUEST_GET_ENTRUSTS_BYIDDEPT_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/getDeptInfoById/${entrustsId}`, { hideLoading: true })
}

// GET /api/mis/sys/entrusts/chargePerson/{entrustsId} 案源方和关联组织下的用户
export const getChargePerson = (entrustsId: string | number): Promise<[]> => {
	return axios.get(`${baseURL}/api/mis/sys/entrusts/chargePerson/${entrustsId}`, { hideLoading: true })
}

// GET /api/mis/sys/entrusts/chargeAllPerson 案源方和关联组织下的所有用户
export const getChargeAllPerson = (): Promise<[]> => {
	return axios.get(`${baseURL}/api/mis/sys/entrusts/chargeAllPerson`, { hideLoading: true })
}
export default {
  getEntrustsDetails,
  addEntrustsInfo,
  getEntrustsList,
  getDeptInfoById,
  getAllEntrusts,
  deleteEntrusts
}
