/**
 * 模板管理/文书模板管理
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 文书模版管理接口

// GET 分页查询文书模板列表
export const getDocTemplateList = (
  params: REQUEST_POST_DOC_TEMPLATE_LISTPARAM_TYPE
): Promise<{ list: REQUEST_GET_DOC_TEMPLATE_LIST_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/v1/tmpl/docTmpl/pageList`, params)
}

// POST /api/v1/tmpl/docTmpl/addDocTmpl 上传文书模板文件
export const uploadDocTemplate = (docTmplId: string | number, formData: FormData): Promise<{ filePath: string }> => {
  return axios.post(`${baseURL}/api/v1/tmpl/docTmpl/addDocTmpl`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    params: { docTmplId }
  })
}

// DELETE /api/v1/tmpl/docTmpl/delete/{id} 删除模板
export const deleteDocTemplate = (id: string | number) => {
  return axios.delete(`${baseURL}/api/v1/tmpl/docTmpl/delete/${id}`)
}

// POST/api/v1/tmpl/docTmpl/save 保存模板
export const saveDocTemplate = (params: REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/v1/tmpl/docTmpl/save`, params)
}

// GET /api/v1/tmpl/docTmpl/getDocInfoById/{id} 通过模板文书id获取模板信息
export const getDocTemplateInfo = (id: string | number): Promise<REQUEST_GET_DOC_TEMPLATE_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/v1/tmpl/docTmpl/getDocInfoById/${id}`)
}

// 文书模板字段映射管理接口 Doc Tmpl Mapping Controller

// POST /api/v1/tmpl/docTmplMapping/docTmplMappingList 查询映射字段列表
export const getDocTemplateMappingList = (
  docTmplId: string
): Promise<REQUEST_POST_DOC_TEMPLATE_MAPPING_LIST_DATA_TYPE[]> => {
  return axios.post(`${baseURL}/api/v1/tmpl/docTmplMapping/docTmplMappingList`, { docTmplId })
}

// DELETE /api/v1/tmpl/docTmplMapping/delete/{id} 删除映射字段
export const deleteDocTemplateMapping = (id: string | number): Promise<REQUEST_MODULE_FIELD_RESINFO_TYPE> => {
  return axios.delete(`${baseURL}/api/v1/tmpl/docTmplMapping/delete/${id}`)
}

// POST /api/v1/tmpl/docTmplMapping/save 保存映射字段
export const saveDocTemplateMapping = (params: REQUEST_POST_DOC_TEMPLATE_MAPPING_SAVE_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/v1/tmpl/docTmplMapping/save`, params)
}

// GET /api/v1/tmpl/docTmplMapping/getDocTmplMappingInfoById/{id} 通过映射字段id获取映射信息
export const getDocTemplateMappingInfo = (
  id: string | number
): Promise<REQUEST_POST_DOC_TEMPLATE_MAPPING_SAVE_PARAM_TYPE> => {
  return axios.get(`${baseURL}/api/v1/tmpl/docTmplMapping/getDocTmplMappingInfoById/${id}`)
}

// 生成文书接口 Mdt Doc Generate Controller

// POST /api/mis/mdt/docGenerate/batchGenerateMultipleDoc 批量生成多案制作文书
export const batchGenerateMultipleDoc = (params: {
  caseIdList: (string | number)[]
  docTmplId: string | number
}): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/docGenerate/batchGenerateMultipleDoc`, params)
}

// POST /api/mis/mdt/docGenerate/batchGenerateSingleDoc 批量生成单案制作文书
export const batchGenerateSingleDoc = (params: {
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
  caseIdList: (string | number)[]
  docTmplId: (string | number)[]
  ifChooseAll?: boolean
}): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/docGenerate/batchGenerateSingleDoc`, params)
}

// GET /api/mis/mdt/docGenerate/findSingleByCaseId/{caseId} 根据案件id获取单案制作文书模板列表
export const findSingleByCaseIds = (params: {
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
  caseIdList: (string | number)[]
  ifChooseAll?: boolean
}): Promise<REQUEST_GET_CREATEDOC_TEMPLATE_LIST_TYPE[]> => {
  return axios.post(`${baseURL}/api/mis/mdt/docGenerate/findSingleByCaseId`, params)
}

export default {
  getDocTemplateList,
  uploadDocTemplate,
  deleteDocTemplate,
  saveDocTemplate,

  getDocTemplateMappingInfo,
  getDocTemplateMappingList,
  deleteDocTemplateMapping,
  saveDocTemplateMapping
}
