/**
 * 模板管理/案件模板管理
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 主模板模块字段管理接口

// GET 基础模块字段列表
export const getBasicModuleList = (): Promise<REQUEST_TEMPLATE_INFO_TYPE> => {
  return axios.get(`${baseURL}/api/v1/tmpl/tmplCaseModuleField/basisModuleList`)
}

// DELETE删除模块字段
export const deleteModuleField = (fieldId: string): Promise<REQUEST_MODULE_FIELD_RESINFO_TYPE> => {
  return axios.delete(`${baseURL}/api/v1/tmpl/tmplCaseModuleField/delete/${fieldId}`)
}

// GET 模块字段列表
export const getModuleFieldList = (tmplId: string | number): Promise<REQUEST_GET_MODULE_FIELD_LIST_TYPE> => {
  return axios.get(`${baseURL}/api/v1/tmpl/tmplCaseModuleField/moduleFieldList/${tmplId}`)
}

// POST 保存模块字段
export const saveModuleField = (
  params: REQUEST_POST_MODULE_FIELD_SAVE_TYPE
): Promise<REQUEST_MODULE_FIELD_RESINFO_TYPE> => {
  return axios.post(`${baseURL}/api/v1/tmpl/tmplCaseModuleField/save`, JSON.stringify(params))
}

// 主模板模块管理接口
// DELETE 删除模块
export const deleteMainTemplateModule = (id: string | number): Promise<REQUEST_MODULE_FIELD_RESINFO_TYPE> => {
  return axios.delete(`${baseURL}/api/v1/tmpl/tmplCaseModule/delete/${id}`)
}

// POST 保存模块
export const saveMainTemplateModule = (
  params: REQUEST_POST_MODULE_SAVE_TYPE
): Promise<REQUEST_MODULE_FIELD_RESINFO_TYPE> => {
  return axios.post(`${baseURL}/api/v1/tmpl/tmplCaseModule/saveModule`, JSON.stringify(params))
}

// 主模板管理接口
// DELETE 删除模板
export const deleteMainTemplate = (tmpId: string | number) => {
  return axios.delete(`${baseURL}/api/v1/tmpl/tmplCase/delete/${tmpId}`)
}

// POST 分页查询模板列表
export const getPageMainTemplateList = (
  params: REQUEST_POST_TEMPLATE_LISTPARAM_TYPE
): Promise<{ list: REQUEST_GET_TEMPLATE_LIST_TYPE[]; total: number }> => {
  return axios.post(`${baseURL}/api/v1/tmpl/tmplCase/pageList`, params)
}

// POST 保存主模板
export const saveMainTemplate = (params: REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/v1/tmpl/tmplCase/save`, JSON.stringify(params))
}

// GET 主模板列表
export const getMainTemplateList = (): Promise<REQUEST_TEMPLATE_INFO_TYPE> => {
  return axios.post(`${baseURL}/api/v1/tmpl/tmplCase/tmplList`)
}
// GET 查询模板列表
export const getSelectTemplateList = (entrustsId:number): Promise<number> => {
  return axios.get(`${baseURL}/api/v1/tmpl/tmplCase/select/${entrustsId}`)
}

// 模块下移
export const tmplCaseModuleMoveDown = (id: string | number): Promise<boolean> => {
  return axios.put(`${baseURL}/api/v1/tmpl/tmplCaseModule/moveDown/${id}`)
}

// 模块上移
export const tmplCaseModuleMoveUp = (id: string | number): Promise<boolean> => {
  return axios.put(`${baseURL}/api/v1/tmpl/tmplCaseModule/moveUp/${id}`)
}

// GET /api/v1/tmpl/tmplCase/downloadTmpl/{tmplType}/{tmplId} 下载模版
export const downloadTmpl = (params: { tmplType: string; tmplId: string | number }) => {
  return axios.get(`${baseURL}/api/v1/tmpl/tmplCase/downloadTmpl/${params.tmplType}/${params.tmplId}`, {
    headers: { 'content-type': 'application/json; charset=utf-8' },
    responseType: 'blob'
  })
}

// 副模板字段映射管理接口
// GET 字段映射列表
export const getDeputyTmplMappingList = (deputyTmplId: number): Promise<REQUEST_DEPUTY_MODULE_FIELD_LIST_TYPE> => {
  return axios.get(`${baseURL}/api/v1/tmpl/deputyTmplMapping/list/${deputyTmplId}`)
}

// POST 保存映射字段
export const saveDeputyTmplField = (params: REQUEST_DEPUTY_MODULE_FIELD_LIST_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/v1/tmpl/deputyTmplMapping/save`, JSON.stringify(params))
}

// 副模板管理接口
// DELETE 删除副模板
export const deleteDeputyTemplate = (id: string | number): Promise<REQUEST_TEMPLATE_INFO_TYPE> => {
  return axios.delete(`${baseURL}/api/v1/tmpl/deputyTmpl/delete/${id}`)
}
// POST 副模板列表
export const deputyTmplList = (
  param: REQUEST_POST_DEPUTY_TEMPLATE_LIST_PARAM_TYPE
): Promise<REQUEST_POST_DEPUTY_TEMPLATE_LIST_DATA_TYPE[]> => {
  return axios.post(`${baseURL}/api/v1/tmpl/deputyTmpl/list`, JSON.stringify(param), {
    hideLoading: true
  })
}
// POST 保存副模板
export const saveDeputyTemplate = (params: REQUEST_POST_DEPUTY_TEMPLATE_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/v1/tmpl/deputyTmpl/save`, JSON.stringify(params))
}

// 查询所有模板列表-主模板
export const getAllTemplateList = (): Promise<REQUEST_GET_TEMPLATE_LIST_TYPE[]> => {
  return axios.get(`${baseURL}/api/v1/tmpl/tmplCase/tmplList`)
}

// 查询模板列表
export const getTemplateList = (params: {
  current: number
  pageSize: number
}): Promise<{ list: REQUEST_GET_TEMPLATE_LIST_TYPE[]; total: number }> => {
  return axios.get(`${baseURL}/api/template/list`, { hideLoading: true, params })
}

export default {
  deleteMainTemplateModule,
  saveMainTemplateModule,

  getBasicModuleList,
  getModuleFieldList,
  deleteModuleField,
  saveModuleField,

  getPageMainTemplateList,
  getMainTemplateList,
  deleteMainTemplate,
  saveMainTemplate,
  downloadTmpl,

  getDeputyTmplMappingList,
  saveDeputyTmplField,

  deleteDeputyTemplate,
  saveDeputyTemplate,

  getTemplateList
}
