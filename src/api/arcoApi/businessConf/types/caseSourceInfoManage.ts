declare interface REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE {
  closeCaseAuditFlag: boolean
  contactWay: string
  contactsName: string
  entrustsAddress: string
  entrustsId: number | null
  entrustsName: string
  entrustsType: string
  entrustsDeptOrgBOList?: REQUEST_GET_ENTRUSTS_DEPTORGBOLIST_TYPE[]
  timeLimit: number
  timeLimitStatus: boolean
  entrustsCode: string
}

declare interface REQUEST_POST_ENTRUSTS_LISTSEARCH_PARAM_TYPE {
  relaMdtOrgIdList: string[]
  contactsName: string
  entrustsName: string
  entrustsType: string
  creatorName: string
  startTime: string
  allTime: string[]
  endTime: string
}

declare interface REQUEST_POST_ENTRUSTS_LIST_PARAM_TYPE {
  param?: Partial<REQUEST_POST_ENTRUSTS_LISTSEARCH_PARAM_TYPE>
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE {
  timeLimitStatus: boolean
  entrustsAddress: string
  contactsName: string
  entrustsName: string
  entrustsType: string
  entrustsCod: string
  creatorName: string
  contactWay: string
  createTime: string
  relaMdtOrg: string
  entrustsId: number
  timeLimit: number
}

declare interface REQUEST_GET_ENTRUSTS_BYIDDEPT_DATA_TYPE {
  sysMdtOrgDTOList: REQUEST_GET_COMMON_ORG_DATA_TYPE[]
  entrustsId: string | number
  deptId: string | number
  entrustsName: string
  deptName: string
}

declare interface REQUEST_GET_ENTRUSTS_DEPTORGBOLIST_TYPE {
  deptId: string | number
  deptName: string
  sysMdtOrgIdList: (string | number)[]
}
