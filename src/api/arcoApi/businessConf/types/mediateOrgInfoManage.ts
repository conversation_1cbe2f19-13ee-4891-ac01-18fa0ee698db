declare interface REQUEST_POST_ORG_SAVE_PARAM_TYPE {
  address: string
  buildTime: string
  companyId?: number
  contactWay: string
  createArea?: string
  entrustsList: { entrustsId: number; entrustsName: string }[]
  intro?: string
  leadBody: string
  mdtScope: string
  orgId: number | null
  orgName: string
  orgType: string
  principal: string
  buildOrgType: string
  socialCreditCode: string
  relaEntrustsIdList?: number[]
}

declare interface REQUEST_POST_ORG_LISTSEARCH_PARAM_TYPE {
  creatorName?: string
  endTime?: string
  orgName?: string
  orgType?: string
  principal?: string
  relaEntrustsIdList?: number[]
  startTime?: string
  allTime?: string[]
}

declare interface REQUEST_POST_ORG_LIST_PARAM_TYPE {
  param?: REQUEST_POST_ORG_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_ORG_LIST_DATA_TYPE {
  contactWay: string
  createTime: string
  creatorName: string
  handleCaseCount: number
  mdtScope: string
  mediatorCount: number
  orgId: number
  orgName: string
  orgType: string
  principal: string
  relaEntrusts: string
}
