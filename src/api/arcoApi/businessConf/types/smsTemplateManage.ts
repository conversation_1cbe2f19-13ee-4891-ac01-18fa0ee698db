declare interface RequestPostSmsTempCommomType {
  templateContent: string
  templateStatus: number
  smsTemplateId: number
  templateName: string
  txTemplateId: string
  description: string
  applyto: string
  channel: string
  smsSign: string
  scene: string
}

declare interface RequestPostSmsTempSaveParamType extends RequestPostSmsTempCommomType {
  roleNameList?: string[]
  templateParams: string
  deptIdList?: string[]
  companyId?: string
}

declare interface RequestPostSmsTempParamSaveType {
  allowEdit: string
  default: string
  param: string
  title: string
}

declare interface RequestPostSmsTempStatusParamType {
  templateStatus: number
  smsTemplateId: number
}

declare interface RequestPostSmsTempResultInfoType extends RequestPostSmsTempCommomType {
  templateParams: RequestPostSmsTempParamSaveType[]
  deptDTOList: [
    {
      treeLevel: number
      deptName: string
      deptId: string
      remark: string
      sort: number
    }
  ]

  roleNameList: string
  roleNames: string[]
  deptIdList: string
}

declare interface RequestPostSmsTempListsearchParamType {
  templateStatus: number | null
  templateTypes: string[]
  templateName: string
  txTemplateId: string
  deptIdList: string[]
  updaterName: string
}

declare interface RequestPostSmsTempListParamType {
  param?: Partial<RequestPostSmsTempListsearchParamType>
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface RequestGetSmstempListDataType extends RequestPostSmsTempCommomType {
  deptDTOList: [
    {
      treeLevel: number
      deptName: string
      remark: string
      deptId: string
      sort: number
    }
  ]

  templateParams: string
  roleNameList: string
  roleNames: string[]
  deptIdList: string
}
