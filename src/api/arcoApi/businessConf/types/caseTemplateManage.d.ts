declare interface REQUEST_TEMPLATE_INFO_TYPE {
  companyName: string
  companyIcon: string
  companyId: number
  address: string
  mobile: string
  email: string
}

declare interface REQUEST_POST_TEMPLATE_LISTPARAM_TYPE {
  param?: REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_TEMPLATE_LIST_TYPE {
  deputyTmplId: number | string
  tmplVersion: number | string
  tmplId: number | string
  baseTmplTitle: string
  entrustsName: string
  updaterName: string
  natureTitle: string
  id: number | string
  updateTime: string
  tmplStatus: number
  tmplTitle: string
  tmplType: string
  isDelete: number
  tmplDesc: string
}

declare interface REQUEST_GET_FIELD_LIST_TYPE {
  allowMultipleRecord: number
  basisModuleFieldId: number
  componentRelation: string
  tmplModuleFieldId: number
  desensitizeRule: string
  toCaseExtField: string
  fieldDataType: string
  fileSemantics: string
  isDesensitize: number
  originalName: string
  tmplModuleId: number
  fieldValues: string
  isValidated: number
  showFormat: string
  createTime: string
  fieldTitle: string
  updateTime: string
  validation: string
  creatorId: number
  fieldDesc: string
  fieldName: string
  isDisable: number
  isRequire: number
  isApprovalValidated: number
  isUpdate: number
  updateId: number
  fieldSn: number
  fieldUi: string
  tmplId: number
  readOnlyRoles?: string[];
}

declare interface REQUEST_GET_MODULE_LIST_TYPE {
  moduleFields: REQUEST_GET_FIELD_LIST_TYPE[]
  allowMultipleRecord: number
  basisModuleId: number
  tmplModuleId: number
  moduleTitle: string
  updateTime: string
  moduleDesc: string
  moduleType: string
  createTime: string
  updaterId: string
  creatorId: string
  tableName: string
  moduleSn: number
  tmplId: number
}

declare interface REQUEST_GET_MODULE_FIELD_LIST_TYPE extends REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE {
  businessModules: REQUEST_GET_MODULE_LIST_TYPE[]
  customModules: REQUEST_GET_MODULE_LIST_TYPE[]
  systemModules: REQUEST_GET_MODULE_LIST_TYPE[]
  allModules: REQUEST_GET_MODULE_LIST_TYPE[]
}

declare interface REQUEST_GET_MAPPINGS_LIST_TYPE {
  tmplModuleFieldId: number
  mappingId: number | null
  deputyTmplId: number
  updateTime?: string
  createTime?: string
  fieldName: string
  creatorId?: string
  converter?: string
  updaterId?: string
  fieldSn: number
}

declare interface REQUEST_GETDEPUTY_MODULE_LIST_TYPE {
  mappings: REQUEST_GET_MAPPINGS_LIST_TYPE[]
  tmplModuleId: number | null
  moduleTitle?: string
  moduleType?: string
  moduleSn?: string
}

declare interface REQUEST_DEPUTY_MODULE_FIELD_LIST_TYPE extends REQUEST_POST_DEPUTY_TEMPLATE_SAVE_PARAM_TYPE {
  businessModules: REQUEST_GETDEPUTY_MODULE_LIST_TYPE[]
  customModules: REQUEST_GETDEPUTY_MODULE_LIST_TYPE[]
  systemModules: REQUEST_GETDEPUTY_MODULE_LIST_TYPE[]
  allModules: REQUEST_GETDEPUTY_MODULE_LIST_TYPE[]
}

declare interface REQUEST_POST_MODULEFIELD_SAVE_TYPE {
  tmplModuleFieldId: number
  deputyTmplId: number
  createTime: string
  creatorId: number
  updateTime: string
  updaterId: string
  converter: string
  mappingId: number
  fieldName: string
  fieldSn: number
}

declare interface REQUEST_POST_MODULE_SAVE_TYPE {
  allowMultipleRecord: number
  tmplModuleId: number | null
  tmplId: number | null
  moduleTitle: string
  moduleDesc: string
}

declare interface REQUEST_POST_MODULE_FIELD_SAVE_TYPE {
  tmplModuleFieldId: number | null
  tmplModuleId: number | null
  componentRelation: string
  readOnlyRoles: string[]
  desensitizeRule?: string
  toCaseExtField?: string
  fileSemantics?: string
  fieldDataType: string
  isDesensitize?: number
  tmplId: number | null
  fieldValues?: string
  isValidated: number
  showFormat?: string
  validation?: string
  fieldTitle: string
  fieldDesc: string
  isDisable?: number
  isRequire: number
	isApprovalValidated: number
  isUpdate?: number
  fieldUi?: string
}

declare interface REQUEST_MODULE_FIELD_RESINFO_TYPE {
  tmplModuleFieldId: number
  tmplModuleId: number
  tmplId: number
}

declare interface REQUEST_POST_MAIN_TEMPLATE_SAVE_PARAM_TYPE {
  isDesensitize?: number | null
  tmplStatus?: number | string
  companyId?: number | string
  entrustsId: number | string
  tmplId: number | string
  updaterName?: string
  entrustsName: string
  natureTitle: string
  tmplTitle: string
  tmplDesc: string
  tmplType: string
}

declare interface REQUEST_POST_DEPUTY_TEMPLATE_SAVE_PARAM_TYPE {
  deputyTmplId: number | string
  tmplId: number | string
  tmplStatus: number
  tmplTitle: string
  tmplType: string
  tmplDesc: string
}

declare interface REQUEST_POST_DEPUTY_TEMPLATE_LIST_PARAM_TYPE {
  deputyTmplIds?: (string | number)[]
  entrustsId?: string | number
  tmplIds?: (string | number)[]
  tmplStatus?: string | number
  tmplType?: string
}

declare interface REQUEST_POST_DEPUTY_TEMPLATE_LIST_DATA_TYPE {
  deputyTmplId: string | number
  entrustsId: string | number
  entrustsName: string
  tmplDesc: string
  tmplId: string | number
  tmplStatus: string | number
  tmplTitle: string
  tmplType: string
}
