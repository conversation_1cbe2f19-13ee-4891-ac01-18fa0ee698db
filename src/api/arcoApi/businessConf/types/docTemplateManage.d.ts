/**
 * 文书模板-保存参数
 * @property { string | number } entrustsId 委托书id
 * @property { string | number } tmplStatus 文书模板状态
 * @property { string | number } docTmplId 文书模板id
 * @property { string | number } tmplId 所属案件模板ID
 * @property { string } entrustsName 案源方名称
 * @property { string } docTmplTitle 文书模板名称
 * @property { string } docTmplType 制作方式
 * @property { string } natureTitle 案由标题
 * @property { string } updaterName 更新人
 * @property { string } docPath 模板路径
 * @property { string } remark 备注
 */
declare interface REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE {
  entrustsId: string | number
  tmplStatus: string | number
  docTmplId: string | number
  tmplId: string | number
  entrustsName: string
  docTmplTitle: string
  docTmplType: string
  natureTitle: string
  updaterName: string
  docPath: string
  remark: string
}

/**
 * 文书模板-模板信息
 * @property { string | number} docTmplId 文书模板id
 * @property { string } docTmplTitle 文书模板名称
 * @property { string } docTmplType 制作方式
 * @property { string } tmplTitle 关联模板名称
 * @property { string } docPath 模板路径
 * @property { string | number} tmplId 所属案件模板ID
 * @property { string } docName 模板名称
 */
declare interface REQUEST_GET_DOC_TEMPLATE_INFO_TYPE {
  docTmplId: string | number
  tmplId: string | number
  docTmplTitle: string
  docTmplType: string
  tmplTitle: string
  docPath: string
  docName: string
}

declare interface REQUEST_POST_DOC_TEMPLATE_LISTPARAM_TYPE {
  param?: Partial<REQUEST_POST_DOC_TEMPLATE_SAVE_PARAM_TYPE>
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

/**
 * 文书模板-列表数据
 * @property { string | number } tmplStatus 模板状态
 * @property { string | number } docTmplId 文书模板id
 * @property { string | number } tmplId 所属案件模板ID
 * @property { string } entrustsName 委托方名称
 * @property { string } docTmplTitle 文书模板名称
 * @property { string } docTmplType 制作方式
 * @property { string } updaterName 更新人
 * @property { string } natureTitle 案由标题
 * @property { string } updateTime 修改时间
 * @property { string } tmplTitle 所属案件模板标题
 * @property { string } remark 备注
 */
declare interface REQUEST_GET_DOC_TEMPLATE_LIST_TYPE {
  tmplStatus: string | number
  docTmplId: string | number
  tmplId: string | number
  entrustsName: string
  docTmplTitle: string
  docTmplType: string
  updaterName: string
  natureTitle: string
  updateTime: string
  tmplTitle: string
  remark: string
}

/**
 * 模板映射-列表数据
 * @property { string } allowMultipleRecord 映射方式
 * @property { string | number } mappingId 映射ID
 * @property { string } parameterContent 参数内容
 * @property { string } parameterName 参数名称
 * @property { string } mappingField 映射字段
 * @property { string } moduleName 模块名称
 * @property { string } updateTime 修改时间
 * @property { string } delimiter 分隔符
 */
declare interface REQUEST_POST_DOC_TEMPLATE_MAPPING_LIST_DATA_TYPE {
  allowMultipleRecord: number
  mappingId: string | number
  parameterContent: string
  parameterName: string
  mappingField: string
  moduleName: string
  updateTime: string
  delimiter: string
}

/**
 * 模板映射-字段列表数据
 * @property { string | number } tmplModuleFieldId 模板模块字段ID
 * @property { string | number } tmplModuleId 模板模块ID
 * @property { string } originalName 组织
 * @property { string } moduleTitle 模块标题
 * @property { string } fieldTitle 字段标题
 * @property { string } fieldName 对应数据表字段名
 * @property { string } tableName 模块对应表名
 */
declare interface REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE {
  tmplModuleFieldId: string | number
  tmplModuleId: string | number
  originalName: string
  moduleTitle: string
  fieldTitle: string
  fieldName: string
  tableName: string
}

/**
 * 模板映射-保存参数
 * @property { arrar } caseFields 模板模块字段
 * @property { string } allowMultipleRecord 映射方式
 * @property { string | number} creatorId 创建者
 * @property { string | number} updaterId 修改者
 * @property { string | number} docTmplId 文书模板ID
 * @property { string | number} mappingId 映射ID
 * @property { string } parameterContent 参数内容
 * @property { string } tmplId 所属案件模板ID
 * @property { string } parameterName 参数名称
 * @property { string } createTime 创建时间
 * @property { string } updateTime 修改时间
 * @property { string } delimiter 分隔符
 */
declare interface REQUEST_POST_DOC_TEMPLATE_MAPPING_SAVE_PARAM_TYPE {
  caseFields: REQUEST_POST_DOC_TEMPLATE_MAPPING_FIELDS_TYPE[]
  allowMultipleRecord: number
  creatorId: string | number
  updaterId: string | number
  docTmplId: string | number
  mappingId: string | number
  parameterContent: string
  tmplId: string | number
  parameterName: string
  createTime: string
  updateTime: string
  delimiter: string
}

/**
 * 生成文书接口-根据案件id获取单案制作文书模板列表
 * @property {string } createTime 创建时间
 * @property {string | number} creatorId 创建者
 * @property {string } docPath 文서模板路径
 * @property {string | number} docTmplId 文书模板ID
 * @property {string } docTmplTitle 文书模板名称
 * @property {string } docTmplType 制作方式
 * @property {string } isDelete 删除标识
 * @property {string } remark 备注
 * @property {string | number} tmplId 所属案件模板ID
 * @property {string | number} tmplStatus 模板状态
 * @property {string } updateTime 修改时间
 * @property {string | number} updaterId 修改者
 */
declare interface REQUEST_GET_CREATEDOC_TEMPLATE_LIST_TYPE {
  tmplStatus: string | number
  docTmplId: string | number
  updaterId: string | number
  creatorId: string | number
  tmplId: string | number
  docTmplTitle: string
  docTmplType: string
  updateTime: string
  createTime: string
  tmplTitle: string
  isDelete: string
  docPath: string
  remark: string
}
