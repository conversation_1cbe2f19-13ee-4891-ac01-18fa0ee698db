/**
 * 案件配置/审批记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 审批记录接口

// POST 批量审核呈批
export const batchAuditApproval = (params: REQUEST_POST_APPROVAL_AUDIT_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/batchAuditApproval`, JSON.stringify(params))
}

// POST 分页查询案件审批
export const getCaseApprovalPageList = (params: REQUEST_POST_APPROVAL_LIST_PARAM_TYPE): Promise<{ list: REQUEST_POST_APPROVAL_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/pageList`, JSON.stringify(params))
}

// POST 发起呈批
export const startAuditApproval = (params: REQUEST_POST_APPROVAL_SAVE_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/startApproval`, JSON.stringify(params))
}

// POST 批量发起呈批
export const batchStartApproval = (params: REQUEST_POST_BATCH_APPROVAL_START_PARAM_TYPE): Promise<string> => {
	return axios.post(`${baseURL}/api/mis/mdt/caseApproval/batchStartApproval`, JSON.stringify(params))
}

export default {
  getCaseApprovalPageList,
  batchAuditApproval,
  startAuditApproval,
	batchStartApproval
}
