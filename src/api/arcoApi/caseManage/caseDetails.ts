/**
 * 案件管理/案件详情
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 调解案件当事人接口 Mdt Case Litigant Controller

// DELETE 删除当事人
export const caseDetailsDeleteLit = (id: string | number) => {
  return axios.delete(`${baseURL}/api/mis/mediate/litigant/delete/${id}`)
}

// GET 查询案件当事人
export const caseDetailsQueryLit = (caseId: string | number): Promise<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mediate/litigant/list/${caseId}`, { hideLoading: true })
}

// POST 保存当事人
export const caseDetailsSaveLit = (param: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mediate/litigant/save`, JSON.stringify(param))
}

// 调解案件接口 Mediate Case Controller

// GET 查询案件基本信息
export const caseDetailsQueryCaseInfo = (caseId: string): Promise<REQUEST_CASEBASIS_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/mediate/case/caseBasis/${caseId}`)
}

export const updateCaseSubjectMatter = (caseId: number,caseSubjectMatter:number): Promise<REQUEST_CASEBASIS_DATA_TYPE> => {
  let param = {
    caseId,
    caseSubjectMatter
  }
  return axios.post(`${baseURL}/api/mis/mediate/case/updateCaseSubjectMatter/`, JSON.stringify(param))
}

// GET 查询案件自定义模块信息
export const caseDetailsQueryModuleInfo = (caseId: string): Promise<REQUEST_CASEDETAILS_CUSTOM_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mediate/case/caseCustom/${caseId}`)
}

// POST 导出案件
export const caseDetailsExportCase = () => {
  return axios.post(`${baseURL}/api/mis/mediate/case/exportCase`)
}

// POST 查询导出模板
export const caseDetailsQueryExportTmpl = () => {
  return axios.post(`${baseURL}/api/mis/mediate/case/getExportTmpl`)
}

// POST 分页查询案件列表
export const caseDetailsQueryCaseList = () => {
  return axios.post(`${baseURL}/api/mis/mediate/case/pageList`)
}

// POST 案件修改
export const caseDetailsCaseEdit = (param: REQUEST_CASEDETAILS_CUSTOM_SAVE_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mediate/case/update`, JSON.stringify(param))
}

// POST 案件删除
export const caseDetailsCaseDelete = (dataId: string) => {
  return axios.delete(`${baseURL}/api/mis/mediate/case/delete/${dataId}`)
}

// 调解案件附件接口 Mdt Case File Controller

// DELETE 删除附件
export const caseDetailsDeleteFile = (fileId: string | number) => {
  return axios.delete(`${baseURL}/api/mis/mediate/file/delete/${fileId}`)
}

// GET 查询案件附件列表
export const caseDetailsQueryFileList = (caseId: string): Promise<REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mediate/file/list/${caseId}`, {
    hideLoading: true
  })
}

// POST 保存附件
export const caseDetailsSaveFile = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mediate/file/save`, JSON.stringify(param))
}

// 调解记录接口 Mdt Case Record Controller

// DELETE 删除调解记录
export const caseDetailsDeleteRecord = (id: string) => {
  return axios.delete(`${baseURL}/api/mis/mdt/case/record/delete/${id}`)
}

// GET 下载 批量导入调解记录模版
export const caseDetailsDownloadImpTmpl = () => {
  return axios.get(`${baseURL}/api/mis/mdt/case/record/downloadImpTmpl`)
}

// POST 导出Excel文件
export const caseDetailsExportAsExcel = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/exportAsExcel`, JSON.stringify(param))
}

// POST 导入 批量调解记录信息
export const caseDetailsImpCaseRecord = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/impCaseRecord`, JSON.stringify(param))
}

// GET 查询案件的调解记录
export const byCaseIdGetMediateRecord = (caseId: string): Promise<REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/case/record/list/${caseId}`, { hideLoading: true })
}

// POST 分页查询调解记录
export const caseDetailsQueryRecordPage = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/pageList`, JSON.stringify(param))
}

// POST 保存调解记录
export const caseDetailsSaveRecord = (param: REQUEST_CASEDETAILS_MEDIATE_SAVE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/case/record/save`, JSON.stringify(param))
}

// 进入调解室
export const getMeetingUrl = (caseId: string | number): Promise<string> => {
  return axios.get(`${baseURL}/api/video/joinMediation/${caseId}`)
}
