/**
 * 案件管理/案件查询
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案件查询分页  /api/mis/mdt/case/pageList
export const pageList = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mediate/case/pageList`, JSON.stringify(param), {
    hideLoading: true
  })
}

// 查询调解组织下的调解团队
export const getDept = (orgIds: (string | number)[]): Promise<REQUEST_POST_COMMON_DEPT_DATA_TYPE[]> => {
  return axios.post(`${baseURL}/api/mis/common/biz/getDept`, JSON.stringify(orgIds), {
    hideLoading: true
  })
}



declare interface EmployeeBO {
	orgIds?: never[]
	deptIds?: (string | number)[]
	employeeStatus?: number
	employeeId?: string | number
	queryType?: number
}


// 查询调解团队下的所有调解员
export const getMdtMediator = (employeeBO:EmployeeBO): Promise<REQUEST_POST_COMMON_MEDIATOR_DATA_TYPE[]> => {
  return axios.post(`${baseURL}/api/mis/common/biz/getMdtMediator`, JSON.stringify(employeeBO), {
    hideLoading: true
  })
}

// 查询调解团队下的调解员
export const getMediator = (deptIds: (string | number)[]): Promise<REQUEST_POST_COMMON_MEDIATOR_DATA_TYPE[]> => {
	return axios.post(`${baseURL}/api/mis/common/biz/getMediator`, JSON.stringify(deptIds), {
		hideLoading: true
	})
}

// 查询可见的调解组织
export const getOrg = (): Promise<REQUEST_GET_COMMON_ORG_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/common/biz/getOrg`)
}
// 批量导入案件 caseImportRecord caseBatchImport
export const caseBatchImport = (param: OBJ_KEY_STR_ANY_TYPE): Promise<OBJ_KEY_STR_ANY_TYPE> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseImportRecord/import`, param, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}
// 导出案件
export const exportCase = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mediate/case/exportCase`, JSON.stringify(param), {
    responseType: 'blob'
  })
}
// 查询导出模板
export const getExportTmpl = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mediate/case/getExportTmpl`, param, {
    hideLoading: true
  })
}

// 获取所有案件id
export const getAllCaseId = (params: OBJ_KEY_STR_ANY_TYPE): Promise<(string | number)[]> => {
  return axios.post(`${baseURL}/api/mis/mediate/case/getAllListId`, params)
}

// 获取权限内的模板列表-主模板
export const getAllTemplateList = (): Promise<REQUEST_GET_TEMPLATE_LIST_TYPE[]> => {
	return axios.get(`${baseURL}/api/mis/mediate/case/tmplList`)
}
