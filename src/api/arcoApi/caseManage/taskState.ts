/**
 * 案件管理/任务完成情况登记功能
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 任务完成情况登记功能接口 Mdt Task Complete Register Controller

// DELETE /task/complete/register 通过主键删除数据
export const taskStateDelete = (taskCompleteId: string | number) => {
  return axios.delete(`${baseURL}/task/complete/register`, { params: { taskCompleteId } })
}

// POST /task/complete/register/add 新增数据
export const taskStateAdd = (param: REQUEST_CASEDETAILS_TASK_SAVE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/task/complete/register/add`, param)
}

// GET /task/complete/register/getByCaseId 根据案件id查询任务完成情况
export const taskStateGetByCaseId = (caseId: string | number): Promise<REQUEST_CASEDETAILS_TASK_INFO_TYPE> => {
  return axios.get(`${baseURL}/task/complete/register/getByCaseId`, { params: { caseId } })
}

// PUT /task/complete/register/update 更新数据
export const taskStateUpdate = (param: REQUEST_CASEDETAILS_TASK_SAVE_PARAM_TYPE) => {
  return axios.put(`${baseURL}/task/complete/register/update`, param)
}

// GET /task/complete/register/{taskCompleteId} 通过ID查询单条数据
export const taskStateGetById = (taskCompleteId: string | number) => {
  return axios.get(`${baseURL}/task/complete/register/${taskCompleteId}`)
}


// POST 批量任务分派
export const bathTaskAssign = (params: REQUEST_POST_BATCH_TASK_ASSIGN_PARAM_TYPE) => {
	return axios.post(`${baseURL}/task/complete/register/bathTaskAssign`, params, {
		hideLoading: true
	})
}
