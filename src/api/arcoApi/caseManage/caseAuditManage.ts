/**
 * 案件管理/案件审批查询
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案件审批查询分页
export const pageList = (
  param: REQUEST_POST_AUDIT_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_POST_AUDIT_LIST_DATA_TYPE[]; total: number }> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/pageList`, JSON.stringify(param), {
    hideLoading: true
  })
}

// POST /api/mis/mdt/caseApproval/getApprovalType获取审批类型
export const getAuditApprovalType = (approvalIdList: (string | number)[]): Promise<boolean> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/getApprovalType`, JSON.stringify(approvalIdList), {
    hideLoading: true
  })
}

// 批量审核呈批
export const batchAuditApproval = (param: REQUEST_POST_AUDIT_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseApproval/batchAuditApproval`, JSON.stringify(param), {
    hideLoading: true
  })
}
