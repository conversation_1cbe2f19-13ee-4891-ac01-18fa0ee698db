declare interface REQUEST_CASEDETAILS_MEDIATE_SAVE_PARAM_TYPE {
  caseId: string | number
  contactPhone: string
  contactPhoneList: string[]
  contactorUser: string
  identityType: string
  intentionAmount: number
  isAnswer: string
  mediateStatus: string
  mediateTime: string
  mediateType: string
  mediatorName: string
  needHelpContent: string
  nextDoTime: string
  otherDemand: string
  recordId: string | number
  litigantId?: string | number
	expectedSuccessDate: string
}

declare interface REQUEST_CASEDETAILS_MEDIATE_LIST_DATA_TYPE {
  account: string
  accountName: string
  callRecord: string
  caseId: string | number
  contactPhone: string
  contactPhoneList: string[]
  contactRela: number | null
  contactResult: number | null
  contactorUser: string
  createTm: string
  identityType: string
  intentionAmount: number | null
  isAnswer: string
  mdtResult: number | null
  mediateResult: string
  mediateStatus: string
  mediateTime: string
  mediateType: string
  mediatorName: string
  needHelpContent: string
  nextDoTime: string
  otherDemand: string
  phoneType: number | null
  recordId: string | null
  reducteAmount: number | null
  reducteType: number | null
  litigantId: string | number
}

declare interface REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE {
  isPrivateEnterprise: string
  litigantId: string | number
  registerAddress: string
  businessAddress: string
  caseId: string | number
  legalAgentName: string
  contactAddress: string
  litigantPhone: string
  litigantDesc: string
  litigantType: string
  litigantName: string
  identityType: string
  phoneStatus: string
	contactStatus:number
  companyName: string
  idAddress: string
  idType: string
  nation: string
  birth: string
  idNo: string
  post: string
  sex: string
}
declare interface LITIGANT_PHONE_TYPE{
  caseId: number
  checkTag: string
  litigantId: number
  phone: string
  preTag: number
  show: boolean
  checksloading: boolean
}
declare interface REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE {
  ifShowVirtualButton: boolean
  isPrivateEnterprise: string
  litigantId: string | number
  creatorId: string | number
  updaterId: string | number
  numberRepairResult: string
  matchRepairResult: string
  refreshloading?: boolean
  repairloading?: boolean
  checksloading?: boolean
  batchExpireDate: number
  businessAddress: string
  caseId: string | number
  registerAddress: string
  legalAgentName: string
  contactAddress: string
  litigantPhone: string
  litigantDesc: string
  litigantType: string
  litigantName: string
  identityType: string
  companyName: string
  phoneStatus: string
  updateTime: string
  createTime: string
  virtualNo: string
  idAddress: string
  nation: string
  idType: string
  birth: string
  post: string
  idNo: string
  sex: string
	mbti: string | null
	mbtiStatus:number
  phoneList:LITIGANT_PHONE_TYPE[]
}

declare interface REQUEST_CASEDETAILS_CUSTOM_LIST_VALUES_TYPE {
  1: { data: { key: string; value: string }[]; dataId: string }[]
  0: { key: string; value: string }[]
}

declare interface REQUEST_CASEDETAILS_CUSTOM_LIST_DATA_TYPE {
  valueList: REQUEST_CASEDETAILS_CUSTOM_LIST_VALUES_TYPE[0]
  values: REQUEST_CASEDETAILS_CUSTOM_LIST_VALUES_TYPE[1]
  fields: REQUEST_GET_FIELD_LIST_TYPE[]
  tmplModuleId: string | number
  allowMultipleRecord: 1 | 0
  data: OBJ_KEY_STR_STR_TYPE
  moduleTitle: string
  moduleType: string
  moduleSn: number
  dataId: string
}

declare interface REQUEST_CASEDETAILS_CUSTOM_SAVE_PARAM_TYPE {
  tmplModuleId: string | number
  data: OBJ_KEY_STR_STR_TYPE
  caseId: string | number
  dataId: string
}

declare interface REQUEST_CASEDETAILS_FILE_LIST_DATA_TYPE {
  caseId: string | number
  fileId: string | number
  uploadDate: string
  uploadUser: string
  fileFormat: string
  fileName: string
  filePath: string
  fileType: string
}

declare interface REQUEST_CASEDETAILS_FILE_SAVE_PARAM_TYPE {
  fileId: string | null
  file: FormData
}

declare interface REQUEST_CASEBASIS_DATA_TYPE {
  caseId: number
  caseNo: string
  entrustsName: string
  entrustsDeptId: number
  entrustsDeptName: string
  creatorName: string
  mdtCaseStatus: string
  mediateStatus: string
  mediateResult: string
  orgName: string
  deptName: string
  currentMediatorName: string
  remainingDays: number
  createTime: string
  LocalDateTime: string
  closeTime: string
  caseName: string
  mediateBeginTime: string
  mediateDoneTime: string
  mediateCloseTime: string
  caseApplyTime: string
  caseNatureContent: string
  expirationTime: string
  defendAntLitigantNames: string
  plainTiffLitigantNames: string
  otherDemand: string
  successReason: string
  closeReason: string,
	businessType?:number
	businessTypeObj?:SysBusinessType
	caseManageCloseTime?:string
	caseManageCloseReason?:string
  caseSubjectMatter?:number
	suspendReason: string
}
