declare interface REQUEST_CASEDETAILS_TASK_SAVE_PARAM_TYPE {
	deliveredTaskStatus?: string | number;
	deliveredTaskAccountId?: string | number;
	schedulingTaskStatus?: string | number;
	schedulingTaskAccountId?: string | number;
	evaluationTaskStatus?: string | number;
	evaluationTaskAccountId?: string | number;
	closedTaskStatus?: string | number;
	closedTaskAccountId?: string | number;
	reachTaskStatus?: string | number;
	reachTaskAccountId?: string | number;
	repairTaskStatus?: string | number;
	repairTaskAccountId?: string | number;
	mediateTaskStatus?: string | number;
	mediateTaskAccountId?: string | number;

  caseId: string | number
}

declare interface REQUEST_CASEDETAILS_TASK_INFO_TYPE {
	deliveredTaskStatus?: string | number;
	deliveredTaskAccountId?: string | number;
	schedulingTaskStatus?: string | number;
	schedulingTaskAccountId?: string | number;
	evaluationTaskStatus?: string | number;
	evaluationTaskAccountId?: string | number;
	closedTaskStatus?: string | number;
	closedTaskAccountId?: string | number;
	reachTaskStatus?: string | number;
	reachTaskAccountId?: string | number;
	repairTaskStatus?: string | number;
	repairTaskAccountId?: string | number;
	mediateTaskStatus?: string | number;
	mediateTaskAccountId?: string | number;

  taskCompleteId: string | number
  caseId: string | number
  updateTime?: string
  createTime?: string
}


declare interface REQUEST_POST_BATCH_TASK_ASSIGN_PARAM_TYPE{
	[key:string]:any
	mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
	caseIdList: (string | number)[]
	ifChooseAll?: boolean
}
