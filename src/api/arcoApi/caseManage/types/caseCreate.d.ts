declare interface REQUEST_CREATE_SAVE_BASEPARAM_TYPE {
  caseSubjectMatter: number | null
  entrustsDeptId: string | number
  deputyTmplId: string | number
  entrustsId: string | number
  caseNatureContent: string
  mediateBeginTime: string
  mediateEndTime: string
  deputyTmplName: string
  completeReview: number
  entrustsName: string
  allTime?: string[]
  tmplName: string
  caseName: string
  caseNo: string
	businessType:string
}

declare interface REQUEST_CREATE_SAVE_PARAM_TYPE extends REQUEST_CREATE_SAVE_BASEPARAM_TYPE {
  mdtCaseLitigantSaveBOList: REQUEST_CASEDETAILS_PARTY_SAVE_PARAM_TYPE[]
}
