declare interface REQUEST_CASEDETAILS_DOC_SAVE_PARAM_TYPE {
  instrumentsLitigantIdList: (string | number)[]
  instrumentsTmplId: string | number
  instrumentsIdentityType: string
  instrumentsFile: File | null
  documentFileName?: string
  caseId: string | number
  selectedRecords?: REQUEST_GET_CALL_LIST_DATA_TYPE[]
}

declare interface REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE {
  instrumentsLitigantIds: (string | number)[]
  instrumentsLitigantName: string | number
  instrumentsTmplId: string | number
  instrumentsIdentityType: string
  fileSignInfoId: string | number
  instrumentsId: string | number
  instrumentsSignStatus: number
  instrumentsTmplName: string
  instrumentsFilePath: string
  creatorId: string | number
  instrumentsName: string
  caseId: string | number
  creatorName: string
  updaterName: string
  createType: string
  updateTime: string
  createTime: string
  updaterId: string
	createStatus:number
}

declare interface REQUEST_CASEDETAILS_SGIN_MAN_DATA_TYPE {
  litigantId: string
  litigantName: string
  signStatus: number
}

declare interface REQUEST_CASEDETAILS_SGIN_QECODE_TYPE {
  litigantIdList: (string | number)[]
  instrumentsId: string | number
  identityTypeList?: string[]
  caseId: string | number
  filePath: string
}

declare interface REQUEST_CASEDETAILS_SGIN_DETAILS_TYPE {
  qrCodeFilePath: string
  signerSignStatuses: REQUEST_CASEDETAILS_SGIN_MAN_DATA_TYPE[]
  situation: string
  status: number
}

declare interface AI_DOCUMENT_GENERATE_BY_RECORD_PARAM_TYPE {
  caseId: string | number
  recordIdList: (string | number)[]
}
