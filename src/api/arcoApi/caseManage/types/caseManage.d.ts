declare interface CASE_SEARCH_FORM_TYPE {
  cascaderEntrustsIds?: any[]
  mdtCaseStatuses: never[]
  mediateStatuses: never[]
  entrustsNames: never[]
  createTime: never[]
  entrustsOrgBOList: any[]

  createStartTime: string
  createEndTime: string
  mediateResult: string
  caseNo: string
}

// 任务实例查询表单类型
declare interface TASK_INSTANCE_SEARCH_FORM_TYPE {
  assignAccountIdList: number[]
  taskTypes: number[]
  assignTimeStart?: string
  assignTimeEnd?: string
  deadlineStart?: string
  deadlineEnd?: string
  managerIdList: number[]
  taskStatuses: number[]
  finishAccountIdList: number[]
  finishTimeStart?: string
  finishTimeEnd?: string
  remark?: string
  assignTime?: string[]
  deadline?: string[]
  finishTime?: string[]
	isExist?:number
}

declare interface CASE_HIGH_SEARCH_FORM_TYPE {
  litigantIdentityTypes: never[]
  litigantPhoneStatuses: never[]

  caseNatureIds: number[]
  currentMediatorIds: never[]

  caseExpirationBegin: string
  caseExpirationEnd: string
  caseApplyBegin: string
  caseStartBegin: string
  caseStartEnd: string
  caseApplyEnd: string

  mediateCloseTimeStart: string
  mediateCloseTimeEnd: string
  mediateCloseTime: string[]

  expirationTimeStart: number | null
  expirationTimeEnd: number | null

  caseExpiraTime?: string[]
  caseStartTime?: string[]
  applyTime?: string[]

  closeStartTime: string
  closeEndTime: string
  closeTime: string[]

	caseManageCloseStartTime: string[]
	caseManageCloseEndTime: string
	caseManageTime: string[]


  successReasons: string[]
	suspendReasons: string[]
	caseManageCloseReasons:string[]
  litigantTypes: never[]
  closeReasons?: never[]
  litigantPhone: string
  closeReason: never[]
  litigantName: string
  deptIds: never[]
  orgIds: never[],
	businessTypes:string[]
	caseMediateTypes:string[]
  // 新的任务实例查询对象
  taskInstance: TASK_INSTANCE_SEARCH_FORM_TYPE
}

declare interface CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE {
  fieldValueList?: string[]
  componentRelation: string
  tmplModuleFieldId: number
  fieldValue?: string
  fieldValueSecond?: string
  fieldTitle: string
  originalName: string
  isAbsent?: boolean
  fieldValues?: any
}

//数据层次结构
declare interface CASE_CUSTOM_MODULE_SEARCH_FORM_MODULE_TYPE {
  fields: CASE_CUSTOM_MODULE_SEARCH_FORM_FIELD_TYPE[]
  tmplModuleId: number
}

declare class CASE_CUSTOM_MODULE_SEARCH_FORM_TYPE {
  customModules: CASE_CUSTOM_MODULE_SEARCH_FORM_MODULE_TYPE[]
  tmplCaseId?: number
}

declare interface CASE_IMPORT_FORM_TYPE {
  mediateBeginTime: string
  dateConfigType: string
  mediateEndTime: string
  completeReview: number
  entrustsDeptId: string
  isIgnoreError: boolean
  deputyTmplId: string
  entrustsId: string
  allTime?: string[]
  fileList: never[]
  impFlag: string
	caseMediateType:string
	businessType:number|null
	isDesensitize:boolean
}

declare interface CASE_ASSIGN_FORM_TYPE {
  currentMediatorId: string | number
  deptId: string | number
  orgId: string | number
  orgType: string
}

declare interface REQUEST_GET_CASE_LIST_TYPE {
  arbMdtAmount: number
  batchNo: string
  mediateStatus: string
  caseBatchId: number | string
  caseId: number | string
  caseNatureContent: string
  caseNo: string
  caseStatus: number | string
  entrustsDeptName: string
  creatorName: string
  claimantName: string
  closeTime: string
  createTime: string
  currentMediatorName: string
  entrustsId: number | string
  entrustsName: string
  filePath: string
  fileType: string
  loanTotal: number
  mediationAgreement: string
  mediationAgreementSignFlag: boolean
  mediationNotice: string
  mediationNoticeSignFlag: boolean
  mediationResult: string
  overdueInterest: number
  overduePrincipal: number
  penaltyInterest: number
  processFlag: number | string
  respondAddress: string
  respondIdCertNo: string
  respondMobile: string
  respondName: string
  signStatus: string
  defendAntLitigantNames: string
  plainTiffLitigantNames: string
  otherDemand: string
	businessType:string
	caseMediateType:string
}

declare interface REQUEST_GET_COMMON_ORG_DATA_TYPE {
  companyId: string | number
  socialCreditCode: string
  orgId: string | number
  buildOrgType: string
  contactWay: string
  createArea: string
  principal: string
  buildTime: string
  leadBody: string
  mdtScope: string
  orgName: string
  orgType: string
  address: string
  intro: string
}

declare interface REQUEST_POST_COMMON_MEDIATOR_DATA_TYPE {
  employeeStatus: string | number
  employeeId: string | number
  accountId: string | number
  loginName: string | number
  deptId: string | number
  employeeName: string
  roleNames: string
  deptName: string
}

declare interface REQUEST_POST_COMMON_DEPT_DATA_TYPE {
  deptId: string | number
  treeLevel: number
  deptName: string
  remark: string
  sort: number
}
