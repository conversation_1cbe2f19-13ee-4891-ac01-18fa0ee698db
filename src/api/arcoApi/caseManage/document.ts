/**
 * 案件管理/案件详情/文书-电子签名
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// POST 新增文书数据
export const addDocument = (param: FormData) => {
  return axios.post(`${baseURL}/mdtInstruments/add`, param, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// PUT 完成签名
export const completeSign = (params: { filePath: string }) => {
  return axios.put(`${baseURL}/mdtInstruments/completeSign`, JSON.stringify(params))
}

// DELETE 通过主键删除文书数据
export const byIdDeleteDocument = (instrumentsId: string | number) => {
  return axios.delete(`${baseURL}/mdtInstruments/delete/${instrumentsId}`)
}

// GET 下载文书文件
export const downloadDocumentFile = (param: { instrumentsId: string | number }) => {
  return axios.get(`${baseURL}/mdtInstruments/downloadFile`, {
    headers: { 'content-type': 'application/json; charset=utf-8' },
    responseType: 'blob',
    params: param
  })
}

// GET 根据案件id获取文书列表信息
export const byCaseIdGetDocumentList = (
  caseId: string | number
): Promise<REQUEST_CASEDETAILS_DOC_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/mdtInstruments/list/${caseId}`, { hideLoading: true })
}

// POST 生成文书签名二维码
export const newCreateSignQrCode = (param: REQUEST_CASEDETAILS_SGIN_QECODE_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/mdtInstruments/newCreateQrCode`, JSON.stringify(param), {
    hideLoading: true
  })
}

// GET 获取文书签名二维码
export const getSignQrCode = (param: { filePath: string }): Promise<{ base64QrCode: string; content: string }> => {
  return axios.get(`${baseURL}/mdtInstruments/newGetQrCode`, { params: param })
}

// POST 签名发送短信验证码
export const sendSignSmsCode = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/mdtInstruments/sendSignSmsCode`, JSON.stringify(param), {
    hideLoading: true
  })
}

// POST 更新文书数据
export const updateDocument = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/mdtInstruments/update`, JSON.stringify(param), {
    hideLoading: true
  })
}

// POST 上传文书文件
export const uploadDocumentFile = (param: FormData) => {
  return axios.post(`${baseURL}/mdtInstruments/uploadFile`, param, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// POST 上传签名文件并签名
export const uploadSignImageAndSignFile = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/mdtInstruments/uploadSignImageAndSignFile`, JSON.stringify(param), {
    hideLoading: true
  })
}

// GET 通过ID查询单条数据
export const byIdFindSingleDocument = (
  instrumentsId: string | number
): Promise<REQUEST_CASEDETAILS_PARTY_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/mdtInstruments/${instrumentsId}`)
}

// GET 刷新签名记录
export const flushSignRecord = (instrumentsId: string | number): Promise<REQUEST_CASEDETAILS_SGIN_DETAILS_TYPE> => {
  return axios.get(`${baseURL}/mdtInstruments/flushSignRecord/${instrumentsId}`)
}

// GET 终止签名
export const terminateSign = (instrumentsId: string | number): Promise<boolean> => {
  return axios.get(`${baseURL}/mdtInstruments/endSign/${instrumentsId}`)
}

// POST AI根据录音生成Word文书文件
export const generateDocumentByRecord = (param: AI_DOCUMENT_GENERATE_BY_RECORD_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/docGenerate/generateByRecord`, JSON.stringify(param), {
    hideLoading: true
  })
}


