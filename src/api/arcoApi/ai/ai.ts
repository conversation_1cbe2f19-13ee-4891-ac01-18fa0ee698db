/**
 * ai功能
 */

import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http'
import { AIContentDTO } from '@/api/arcoApi/ai/types/AIContentBo.ts' // 导入http中创建的axios实例



export enum AIContentType {
	MEDIATE_OUTLINE = 1,
	CASE_REVEIW = 2,
	MBTI_REPORT = 101,
	GUESS_REPLY = 102,
	VERBAL_TRICK = 103,

	CALL_RECORD_ASR = 201,
}

// GET
export const createAIContent = (type: AIContentType,id: number | string): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/mdt/ai/content`, JSON.stringify({ type,id}))
}

// GET
export const getAIContent = (type: AIContentType,id: number | string): Promise<AIContentDTO> => {
	return axios.get(`${baseURL}/api/mis/mdt/ai/content`, { params: { type,id} })
}




export default {
	createAIContent,
	getAIContent,
	AIContentType
}
