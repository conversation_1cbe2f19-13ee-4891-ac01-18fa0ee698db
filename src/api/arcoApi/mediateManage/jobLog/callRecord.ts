/**
 * 调解管理/作业日志/呼叫记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 呼叫记录管理接口

// POST 分页查询呼叫记录列表
export const getCallRecordList = (
  params: REQUEST_POST_CALL_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_CALL_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/pageList`, params)
}

// GET 案件详情-呼叫记录
export const byCaseIdGetCallRecord = (caseId: string): Promise<REQUEST_GET_CALL_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/list/${caseId}`, { hideLoading: true })
}

// GET 拉取外呼工作台外呼记录
export const getCallRecord = () => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/pullRecord`)
}

// GET 查询电话状态
export const getCallRecordStates = () => {
  return axios.get(`${baseURL}/api/mis/mdt/callRecord/getStates`)
}

// GET 导出Excel文件
export const exportCallRecordExcel = (params: REQUEST_POST_CALL_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/exportAsExcel`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}

// 导出-呼叫记录
export const exportCallRecord = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/exportCallRecord`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}

// GET 导出压缩包文件
export const exportCallRecordZip = (params: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/exportAsZip`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}

// POST 获取播放录音
export const getCallRecordStream = (path: string) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/getStream`, JSON.stringify(path), {
    responseType: 'blob'
  })
}

// POST 上传呼叫记录
export const uploadCallRecord = (formData: FormData) => {
  return axios.post(`${baseURL}/api/mis/mdt/callRecord/upload`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}
