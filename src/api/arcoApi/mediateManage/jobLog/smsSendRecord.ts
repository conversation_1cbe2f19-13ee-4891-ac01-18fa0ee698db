/**
 * 调解管理/作业日志/短信发送记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 短信发送记录管理接口

// GET 案件详情-短信发送记录
export const byCaseIdGetSmsSendRecord = (caseId: string | number): Promise<REQUEST_GET_SMS_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseSms/log/list/${caseId}`, {
    hideLoading: true
  })
}

// POST 查询短信发送记录
export const querySmsSendRecord = (
  params: REQUEST_POST_SMS_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_SMS_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/log/pageList`, JSON.stringify(params), {
    hideLoading: true
  })
}

// GET 获取模板列表
export const getSmsSendTemplate = (applyto: string): Promise<REQUEST_GET_SMS_BY_APPLYTO_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseSms/templateList/${applyto}`, { hideLoading: true })
}

// POST 批量发送业务短信
export const batchSendBusinessSms = (params: REQUEST_POST_SMS_BATCHSEND_PARAM_TYPE): Promise<boolean> => {
  return axios.post(`${baseURL}/api/mis/mdt/case/batchSendSms`, params)
}

// POST 发送业务短信
export const sendBusinessSms = (params: REQUEST_POST_SMS_SEND_PARAM_TYPE): Promise<boolean> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/sendSms`, params)
}

// POST 重新发送业务短信
export const repairSendBusinessSms = (params: REQUEST_POST_SMS_REPAIRSEND_PARAM_TYPE): Promise<boolean> => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/reSendSms`, params)
}

// GET 导出Excel文件
export const exportSmsLog = (params: REQUEST_POST_SMS_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseSms/exportSmsLog`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
