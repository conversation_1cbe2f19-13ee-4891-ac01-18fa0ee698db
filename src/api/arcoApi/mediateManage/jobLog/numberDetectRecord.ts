/**
 * 调解管理/作业日志/号码检测记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 号码检测记录管理接口

// PUT 更新数据

export const updateDetectRecord = (params: REQUEST_POST_NUMBERDETECT_SAVE_PARAM_TYPE) => {
  return axios.put(`${baseURL}/sysPhoneCheckRecord`, JSON.stringify(params), { hideLoading: true })
}

// DELETE 通过主键删除数据
export const byIdDeleteDetectRecord = (recordId: string | number) => {
  return axios.delete(`${baseURL}/sysPhoneCheckRecord/${recordId}`, { hideLoading: true })
}

// GET 案件详情-号码检测记录
export const byCaseIdGetNumberDetectRecord = (
  caseId: string | number
): Promise<REQUEST_GET_NUMBERDETECT_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/sysPhoneCheckRecord/getPhoneCheckLogByCaseId/${caseId}`, {
    hideLoading: true
  })
}

// POST 查询号码检测记录
export const queryNumberDetectRecord = (
  params: REQUEST_POST_NUMBERDETECT_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_NUMBERDETECT_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/sysPhoneCheckRecord/getPhoneCheckPageLog`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 批量号码检测
export const batchNumberCheck = (params: REQUEST_GET_NUMBERDETECT_BATCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/sysPhoneCheckRecord/phoneBatchCheck`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 单个号码检测
export const singleNumberDetect = (params: { caseId: string | number; litigantId: string | number ;litigantPhone: string}) => {
  return axios.post(`${baseURL}/sysPhoneCheckRecord/phoneCheck`, JSON.stringify(params), {
    hideLoading: true
  })
}

// GET 导出Excel文件
export const exportPhoneCheckPageLog = (params: REQUEST_POST_NUMBERDETECT_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/sysPhoneCheckRecord/exportPhoneCheckPageLog`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
