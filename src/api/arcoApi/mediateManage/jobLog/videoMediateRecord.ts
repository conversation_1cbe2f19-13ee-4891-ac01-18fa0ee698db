/**
 * 调解管理/作业日志/视频调解记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 视频调解记录管理接口

// POST 分页查询视频调解记录列表
export const getVideoMediateRecordList = (
  params: REQUEST_POST_VIDEOMEDIATE_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_VIDEOMEDIATE_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/video/pageList`, params)
}

// GET 案件详情-视频调解记录
export const byCaseIdGetVideoMediateRecord = (
  caseId: string | number
): Promise<REQUEST_GET_VIDEOMEDIATE_BY_CASEID_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/video/meetingList/${caseId}`, { hideLoading: true })
}

// POST /api/video/getMeetingRecordFilePath 获取视频文件下载地址
export const getMeetingVideoPath = (params: { filePath: string }): Promise<string> => {
  return axios.post(`${baseURL}/api/video/getMeetingRecordFilePath`, JSON.stringify(params))
}

// GET 导出Excel文件
export const reportMeetingRecord = (params: REQUEST_POST_VIDEOMEDIATE_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/video/reportMeetingRecord`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
