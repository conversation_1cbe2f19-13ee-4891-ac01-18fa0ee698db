/**
 * 调解管理/作业日志/案件接收记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案件接收记录管理接口

// GET 案件详情-接收记录
export const byCaseIdGetCaseReceptionRecord = (caseId: string): Promise<REQUEST_GET_CASERECEPTION_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseBatchImport/getCaseCollectionRecordReceiveLogListByCaseId/${caseId}`, { hideLoading: true })
}

// POST 分页查询案件接收记录列表
export const getCaseReceptionRecordList = (
  params: REQUEST_POST_CASERECEPTION_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_CASERECEPTION_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseBatchImport/getCaseCollectionRecordReceiveLog`, params)
}

// GET 导出Excel文件
export const exportCollectionReceiveLog = (params: REQUEST_POST_CASERECEPTION_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseBatchImport/exportCollectionReceiveLog`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}