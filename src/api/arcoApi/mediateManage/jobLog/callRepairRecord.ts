/**
 * 调解管理/作业日志/失联修复记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 失联修复记录管理接口

// GET 案件详情-失联修复记录
export const byCaseIdGetCallRepairRecord = (
  caseId: string | number
): Promise<REQUEST_GET_CALLREPAIR_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/contactRepair/repairLogList/${caseId}`, {
    hideLoading: true
  })
}

// GET 案件详情-失联修复记录(新)
export const byCaseIdGetCallRepairRecordNew = (
  caseId: string | number
): Promise<REQUEST_GET_CALLREPAIR_LIST_NEW_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/contactRepair/repairLogListNew/${caseId}`, {
    hideLoading: true
  })
}

// GET 案件详情-失联修复记录(新)
export const byRepairRecordIdGetPhone = (repairRecordId: string): Promise<string> => {
  return axios.get(`${baseURL}/contactRepair/getPhoneNumber/${repairRecordId}`, {
    hideLoading: true
  })
}

// POST 查询失联修复记录
export const queryCallRepairRecord = (
  params: REQUEST_POST_CALLREPAIR_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_CALLREPAIR_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/contactRepair/pageRepairLog`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 查询失联修复记录(新)
export const queryCallRepairRecordNew = (
  params: REQUEST_POST_CALLREPAIR_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_CALLREPAIR_LIST_NEW_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/contactRepair/pageRepairLogNew`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 失联修复-绑定虚拟号
export const bindVirtualNumber = (
  params: CallRepairBindVirtualNoParam
): Promise<{ callNo: string; virtualNo: string }> => {
  return axios.post(`${baseURL}/contactRepair/bindVirtualNumber`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 失联修复-解绑虚拟号
export const unBindVirtualNumber = (params: CallRepairBindVirtualNoParam): Promise<boolean> => {
  return axios.post(`${baseURL}/contactRepair/unbindVirtualNumber`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 批量失联修复
export const batchCallRepair = (params: REQUEST_GET_CALLREPAIR_BATCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/contactRepair/batchRepair`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 批量失联修复(新)
export const batchCallRepairNew = (params: REQUEST_GET_CALLREPAIR_BATCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/contactRepair/batchRepairNew`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 单个失联修复
export const singleCallRepair = (params: REQUEST_GET_CALLREPAIR_SINGLE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/contactRepair/singleRepair`, JSON.stringify(params), {
    hideLoading: true
  })
}

// POST 单个失联修复(新)
export const singleCallRepairNew = (params: REQUEST_GET_CALLREPAIR_SINGLE_PARAM_TYPE) => {
  return axios.post(`${baseURL}/contactRepair/singleRepairNew`, JSON.stringify(params), {
    hideLoading: true
  })
}

// GET 导出Excel文件
export const exportRepairLog = (params: REQUEST_POST_CALLREPAIR_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/contactRepair/exportRepairLog`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
