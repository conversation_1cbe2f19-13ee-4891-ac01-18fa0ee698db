declare interface REQUEST_POST_NUMBERDETECT_SAVE_PARAM_TYPE {
  operatorCompanyId: string | number
  phoneCheckStatus: string | number
  checkDateStart: string | number
  checkDateEnd: string | number
  phoneStatus: string | number
  operatorId: string | number
  httpStatus: string | number
  entrustsId: string | number
  mediatorId: string | number
  litigantId: string | number
  recordId: string | number
  phoneCheckResult: string
  caseId: string | number
  entrustsName: string
  httpResponse: string
  identityType: string
  litigantName: string
  litigantType: string
  operatorName: string
  checkDate: string
  allTime?: string[]
  caseNo: string
}

declare interface REQUEST_POST_NUMBERDETECT_LISTSEARCH_PARAM_TYPE extends REQUEST_POST_NUMBERDETECT_SAVE_PARAM_TYPE {
  litigantPhone: string
}

declare interface REQUEST_POST_NUMBERDETECT_LIST_PARAM_TYPE {
  param?: REQUEST_POST_NUMBERDETECT_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_NUMBERDETECT_LIST_DATA_TYPE extends REQUEST_POST_NUMBERDETECT_SAVE_PARAM_TYPE {
  litigantPhone: string
}

declare interface REQUEST_GET_NUMBERDETECT_BATCH_PARAM_TYPE {
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
  caseIdList: (string | number)[]
  identityTypeList: string[]
  litigantTypeList: string[]
  ifChooseAll?: boolean
}
