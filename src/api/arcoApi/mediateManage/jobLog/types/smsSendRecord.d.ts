declare interface REQUEST_POST_SMS_LISTSEARCH_PARAM_TYPE {
  caseNo: string
  companyId: string | number
  mediatorId: string | number
  contactName: string
  phoneNumber: string
  sendMessage: string
  sendTimeBegin: string
  sendTimeEnd: string
  allTime?: string[]
}

declare interface REQUEST_POST_SMS_LIST_PARAM_TYPE {
  param?: REQUEST_POST_SMS_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_SMS_LIST_DATA_TYPE {
  caseId: string | number
  caseNo: string
  companyId: string | number
  companyName: string
  contactName: string
  phoneNumber: string
  receiveStatus: string
  sendContext: string
  sendMessage: string
  sendStatus: string
  sendTime: string
  sendType: string
  senderId: string | number
  senderName: string
  smsId: string | number
  smsSendId: string | number
}

declare interface REQUEST_GET_SMS_TEMPLATE_PLARM_TYPE {
  allowEdit: string
  default: string
  param: string
  title: string
}

declare interface REQUEST_GET_SMS_BY_APPLYTO_DATA_TYPE {
  smsTemplateId: string | number
  templateContent: string
  templateName: string
  templateParams: REQUEST_GET_SMS_TEMPLATE_PLARM_TYPE
}

declare interface REQUEST_POST_SMS_BATCHSEND_PARAM_TYPE {
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
  caseIdList: (string | number)[]
  tmplId: string | number
  identityType: string
  litigantType: string
  ifChooseAll?: boolean
}

declare interface REQUEST_POST_SMS_SEND_PARAM_TYPE {
  litigantIds: (string | number)[]
  litigantIdentityType?: string[]
  templateId: string | number
  recordId: string | number
  caseId: string | number
}

declare interface REQUEST_POST_SMS_REPAIRSEND_PARAM_TYPE {
  smsSendId: string | number
}
