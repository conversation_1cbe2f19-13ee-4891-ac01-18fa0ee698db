declare interface REQUEST_POST_CASERECEPTION_LISTSEARCH_PARAM_TYPE {
  acceptTime: string
  caseNo: string
  createResult: string
  receiveType: string
  creatorName: string
  entrustsName: string
  entrustsId: string | number
  acceptTimeEnd: string
  acceptTimeStart: string
  allTime?: string[]
}

declare interface REQUEST_POST_CASERECEPTION_LIST_PARAM_TYPE {
  param?: REQUEST_POST_CASERECEPTION_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_CASERECEPTION_LIST_DATA_TYPE {
  acceptTime: string
  caseNo: string
  caseId: string
  collectionId: string | number
  collectionResult: string
  collectionType: string
  companyName: string
  creatorName: string
  entrustsName: string
  filePath: string
  tmplName: string
}
