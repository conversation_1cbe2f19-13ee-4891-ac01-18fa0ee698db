declare interface REQUEST_POST_CALL_LISTSEARCH_PARAM_TYPE {
  calleeName: string
  calleePhone: string
  callerPhone: string
  companyId: string | number
  companyIds: (string | number)[]
  entrustsId: string | number
  mediatorId: string | number
  mediatorIds: (string | number)[]
  mediatorName: string
  optDateEnd: string
  optDateStart: string
  usageMode: string
  allTime?: string[]
}

declare interface REQUEST_POST_CALL_LIST_PARAM_TYPE {
  param?: REQUEST_POST_CALL_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_CALL_LIST_DATA_TYPE {
  callResult: string
  calleeName: string
  calleePhone: string
  callerPhone: string
  caseId: string | number
  caseNo: string
  filePath: string
  mediatorName: string
  orgName: string
  recordId: string | number
  ringLength: string
  startTime: string
  timeLength: string
  usageMode: string
}

declare interface REQUEST_GET_CALL_PHONE_STATUS_TYPE {
  label: string
  value: string
}
