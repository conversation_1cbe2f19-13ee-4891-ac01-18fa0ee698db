declare interface REQUEST_POST_CALLREPAIR_LISTSEARCH_PARAM_TYPE {
  caseNo: string
  company: string
  litigantName: string
  litigantPhone: string
  matchRepairResult: string
  repairTimeEnd: string
  repairTimeStart: string
  allTime?: string[]
}

declare interface REQUEST_POST_CALLREPAIR_LIST_PARAM_TYPE {
  param?: REQUEST_POST_CALLREPAIR_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_CALLREPAIR_LIST_DATA_TYPE {
  batchNo: string
  batchStatus: string
  caseId: string | number
  companyName: string
  failMessage: string
  idCard: string
  phoneStatus: string
  recordId: string | number
  repairTime: string
  repairerName: string
  repairerNumber: string
  userName: string
}

declare interface REQUEST_GET_CALLREPAIR_LIST_NEW_DATA_TYPE {
  caseNo: string
  accountId: string
  accountName: string
  acquireResult: string
  acquireStatus: string
  caseId: string
  companyId: string
  companyName: string
  failMsg: string
  idCard: string
  litigantId: string
  litigantName: string
  expireTime: string
  mdtNetRepairRecordDTOList: REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE[]
  numberExpireTime: string
  repairLitigantId: string
  repairResult: string
  repairStatus: string
  repairSuccessNet: string
  repairSuccessTime: string
  virtualNo: string
}

declare interface REQUEST_GET_CALLREPAIR_LIST_NEW_CHILDDATA_TYPE {
  acquireResult: string
  acquireStatus: string
  caseId: string
  expireTime: string
  failMsg: string
  litigantId: string
  numberExpireTime: string
  phoneNumber: number
  repairCompleteTime: string
  repairFailCode: string
  repairFailMsg: string
  repairLitigantId: string
  repairNet: string
  repairRecordId: string
  repairResult: string
  repairStatus: string
  taskNo: string
  virtualNo: string
}

declare interface REQUEST_GET_CALLREPAIR_BATCH_PARAM_TYPE {
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
  caseId?: string | number
  caseIdList: (string | number)[]
  identityTypeList: string[]
  litigantId: string | number
  litigantPhoneStatusesList: (string | number)[]
  litigantTypeList: string[]
  ifChooseAll?: boolean
}

declare interface REQUEST_GET_CALLREPAIR_SINGLE_PARAM_TYPE {
  caseId: string | number
  caseIdList?: (string | number)[]
  identityType?: string
  litigantId: string | number
  litigantPhoneStatuses?: string | number
  litigantType?: string
}

declare interface CALLREPAIR_TASK_DATA_TYPE {
  label: string
  value: string
  type: string
  data: { repairerNumber: string; repairerName: string; batchNo: string }[]
}

declare interface CallRepairBindVirtualNoParam {
  litigantId?: string | number
  seatNumber?: string
  virtualNo?: string
  caseId?: string
  type?: number
}
