declare interface REQUEST_POST_VIDEOMEDIATE_LISTSEARCH_PARAM_TYPE {
  caseNo: string
  mediationEndTime: string
  mediationStartTime: string
  mediatorId: string
  mediatorName: string
  meetingStatus: string
  participant: string
  allTime?: string[]
}

declare interface REQUEST_POST_VIDEOMEDIATE_LIST_PARAM_TYPE {
  param?: REQUEST_POST_VIDEOMEDIATE_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_VIDEOMEDIATE_LIST_DATA_TYPE {
  caseId: string | number
  caseNo: string
  companyName: string
  entrustsName: string
  filePath: string
  mediationTime: string
  mediatorName: string
  meetingName: string
  meetingStatus: string
  participant: string
  participantType: string
  recordId: string
}

declare interface REQUEST_GET_VIDEOMEDIATE_BY_CASEID_SMS_DATA_TYPE {
  contactName: string
  msgContent: string
  phoneNumber: string
  receiveDescription: string
  smsSendId: string | number
}

declare interface REQUEST_GET_VIDEOMEDIATE_BY_CASEID_DATA_TYPE {
  caseId: string
  filePath: string
  mediationTime: string
  mediatorId: string
  mediatorName: string
  meetingName: string
  meetingStatus: string
  meetingUrl: string
  participant: string
  password: string
  recordId: string | number
  smsList: REQUEST_GET_VIDEOMEDIATE_BY_CASEID_SMS_DATA_TYPE[]
  textContent: string
}
