declare interface REQUEST_POST_CASEALLOCATION_LISTSEARCH_PARAM_TYPE {
  caseId: string | number
  caseNo: string
  distributeTargetName: string
  entrustsId: string | number
  entrustsName: string
  fromStatus: string
  operateTime: string
  operateTimeEnd: string
  operateTimeStart: string
  operatorName: string
  orgName: string
  recordId: string | number
  roleName: string
  toStatus: string
  allTime?: string[]
}

declare interface REQUEST_POST_CASEALLOCATION_LIST_PARAM_TYPE {
  param?: REQUEST_POST_CASEALLOCATION_LISTSEARCH_PARAM_TYPE
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_CASEALLOCATION_LIST_DATA_TYPE {
  caseId: string | number
  caseNo: string
  distributeTargetName: string
  entrustsId: string | number
  entrustsName: string
  fromStatus: string
  operateTime: string
  operateTimeEnd: string
  operateTimeStart: string
  operatorName: string
  orgName: string
  recordId: string | number
  roleName: string
  toStatus: string
}
