/**
 * 调解管理/作业日志/案件分派记录
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 案件分派记录管理接口

// POST 分派案件
export const caseDistribute = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/distribute`, JSON.stringify(param))
}

// POST 查询分派案件
export const findDistributeCase = (param: OBJ_KEY_STR_ANY_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/searchBeforeDistribute`, JSON.stringify(param))
}

// GET 案件详情-分派记录
export const byCaseIdGetCaseAllocationRecord = (
  caseId: string
): Promise<REQUEST_GET_CASEALLOCATION_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/mdt/caseDistribute/getDistributeRecordListByCaseId/${caseId}`, {
    hideLoading: true
  })
}

// POST 分页查询案件分派记录列表
export const getCaseAllocationRecordList = (
  params: REQUEST_POST_CASEALLOCATION_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_CASEALLOCATION_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/getPageDistributeRecordLog`, params)
}

// GET 查询所有调解员
export const findAllMediator = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseDistribute/mediatorList`)
}

// GET 导出Excel文件
export const exportDistributeLog = (params: REQUEST_POST_CASEALLOCATION_LISTSEARCH_PARAM_TYPE) => {
  return axios.post(`${baseURL}/api/mis/mdt/caseDistribute/exportDistributeLog`, JSON.stringify(params), {
    hideLoading: true,
    responseType: 'blob'
  })
}
