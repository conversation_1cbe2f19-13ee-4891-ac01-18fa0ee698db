declare interface REQUEST_POST_STATISTIC_PARAM_TYPE {
  caseCauseList?: string[]
  dateEnd?: string | Date | number
  dateStart?: string | Date | number
  groupCondition?: string | number
  statisticsRound?: STATISTICS_ROUND_TYPE[]
}

declare interface STATISTICS_ROUND_TYPE {
  key: string | number
  type: number
}

declare interface COMMON_FORM_TYPE {
  key?: string
  keyword?: string | number
  date?: [string | Date | number, string | Date | number]
  trees?: (string | number)[]
  treesNode?: STATISTICS_ROUND_TYPE[]
  natures?: (string | number)[]
  naturesTitle?: string[]
}

declare interface XY_DATA_TYPE {
  xAxis: string[]
  yAxis: YAXIS_TYPE[]
}

declare interface YAXIS_TYPE {
  date: string[]
  type: string
}
