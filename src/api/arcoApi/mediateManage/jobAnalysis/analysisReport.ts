/**
 * 调解管理/作业分析
 */
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例
import { removeNullParam } from '@/utils'

// GET 获取统计条件
export const getStatisticsCondition = () => {
  return axios.get(`${baseURL}/api/mis/mdt/caseStatistics/getStatisticsCondition`)
}

// 获取结案率
export const getCaseCloseRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getCaseCloseRate`, params)
}

// 获取案件调解进展
export const getCaseProgress = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getCaseProgress`, params)
}

// 获取案件触达率
export const getCaseReachRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getCaseReachRate`, params)
}

// 获取案件突增预警
export const getCaseSurgeWarning = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getCaseSurgeWarning`, params)
}

// 获取诉调分流率
export const getDiversionRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getDiversionRate`, params)
}

// 获取调解成功率
export const getMediationSuccessRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/caseStatistics/getMediationSuccessRate`, params)
}

// 获取有效通话平均时长
export const getAverageEffectiveCall = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getAverageEffectiveCall`, params)
}

// 获取人均拨打时长
export const getAvgCallTime = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getAvgCallTime`, params)
}

// 获取呼叫数据总览
export const getCallDataOverview = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getCallDataOverview`, params)
}

// 获取呼损率趋势分析
export const getCallFailureRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getCallFailureRate`, params)
}

// 获取人均通话数
export const getCallsPerPerson = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getCallsPerPerson`, params)
}

// 获取在线坐席统计
export const getOnlineAgentStatistics = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getOnlineAgentStatistics`, params)
}

// 获取有效接通率
export const getValidAnswerRate = (params: REQUEST_POST_STATISTIC_PARAM_TYPE) => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/mdt/callStatistic/getValidAnswerRate`, params)
}