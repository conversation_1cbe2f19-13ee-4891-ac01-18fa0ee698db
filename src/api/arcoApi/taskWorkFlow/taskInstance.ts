/**
 * 业务配置/任务实例管理
 * 案件任务实体接口
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base' // 导入接口域名列表
import axios from '@/utils/http' // 导入http中创建的axios实例

// 任务实例管理接口

/**
 * 任务登记/分派
 * @param params 任务创建参数
 * @returns Promise<number> 返回任务实例ID
 */
export const createTask = (params: REQUEST_POST_TASKINSTANCE_CREATE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/task/instance/create`, JSON.stringify(params))
}

/**
 * 任务办理
 * @param params 任务处理参数
 * @returns Promise<string> 返回处理结果
 */
export const processTask = (params: REQUEST_POST_TASKINSTANCE_PROCESS_PARAM_TYPE): Promise<string> => {
  return axios.put(`${baseURL}/api/mis/task/instance/process`, JSON.stringify(params))
}

/**
 * 任务批量登记
 * @param params 任务批量登记参数
 * @returns Promise<string> 返回处理结果
 */
export const batchCreateTask = (params: REQUEST_POST_TASKINSTANCE_BATCHCREATE_PARAM_TYPE): Promise<string> => {
  return axios.post(`${baseURL}/api/mis/task/instance/batchCreate`, JSON.stringify(params))
}

/**
 * 查询任务实例列表
 * @param params 查询参数
 * @returns Promise<REQUEST_GET_TASKINSTANCE_LIST_DATA_TYPE[]> 返回所有任务实例列表
 */
export const getTaskInstanceList = (
  params: REQUEST_POST_TASKINSTANCE_LISTSEARCH_PARAM_TYPE
): Promise<REQUEST_GET_TASKINSTANCE_LIST_DATA_TYPE[]> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/task/instance/list`, JSON.stringify(params),{hideLoading: true})
}

/**
 * 根据ID获取单个任务实例
 * @param instanceId 任务实例ID
 * @returns Promise<REQUEST_GET_TASKINSTANCE_DATA_TYPE> 返回单个任务实例详情
 */
export const getTaskInstanceById = (instanceId: number|string): Promise<REQUEST_GET_TASKINSTANCE_DATA_TYPE> => {
  return axios.get(`${baseURL}/api/mis/task/instance/${instanceId}`)
}

export default {
  createTask,
  processTask,
  getTaskInstanceList,
  getTaskInstanceById
}
