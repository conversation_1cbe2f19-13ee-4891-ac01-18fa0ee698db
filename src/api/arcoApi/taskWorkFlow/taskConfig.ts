/**
 * 业务配置/任务流程配置
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base.ts' // 导入接口域名列表
import axios from '@/utils/http.ts' // 导入http中创建的axios实例

// 任务流程配置接口

// POST 分页查询案源方列表
export const getConfigPageList = (
  params: {}
): Promise<{ list: []; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/task/config/pageList`, params)
}
export const getConfigList = (
	workflowId:number
): Promise<{ list: []; total: number }> => {
	return axios.get(`${baseURL}/api/mis/task/config/list/${workflowId}`)
}
// POST  添加|修改 任务流程
export const addConfig = (params: REQUEST_POST_TASKWORKFLOW_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/task/config/addOrUpdate`, JSON.stringify(params))
}
// GET 获取任务流程实体
export const getConfigDetails =  (id: number): Promise<string> => {
	return axios.get(`${baseURL}/api/mis/task/config/${id}`)
}

// 通过id删除任务流程
export const deleteConfig = (id: number): Promise<string> => {
	return axios.delete(`${baseURL}/api/mis/task/config/${id}`)
}

export default {
	getConfigList,
}
