/**
 * 业务配置/任务流程触发条件配置
 */
import { baseURL } from '@/api/base.ts' // 导入接口域名列表
import axios from '@/utils/http.ts' // 导入http中创建的axios实例

// 任务流程配置接口
// GET /api/mis/task/trigger/config/ 通过id查询任务触发条件
export const getTaskTriggerConditionGroup = (id: number): Promise<number> => {
	return axios.get(`${baseURL}/api/mis/task/trigger/config/${id}`)
}
// POST /api/mis/task/trigger/save 保存任务触发条件
export const saveTaskTriggerConditionGroup = (data: any): Promise<number> => {
	return axios.post(`${baseURL}/api/mis/task/trigger/save`, JSON.stringify(data))
}
export default {
	getTaskTriggerConditionGroup,
}
