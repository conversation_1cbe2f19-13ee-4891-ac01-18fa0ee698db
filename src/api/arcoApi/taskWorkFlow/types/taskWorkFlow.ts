declare interface REQUEST_POST_TASKWORKFLOW_SAVE_PARAM_TYPE {
	workflowName: string
	entrustsId: number | null
	templateId: number | null
	status:number|null
}

declare interface REQUEST_POST_TASKWORKFLOW_LISTSEARCH_PARAM_TYPE {
	workflowName: string
	entrustsId: number | null
	templateId: number | null
	status:number|null
}

declare interface REQUEST_POST_TASKWORKFLOW_LIST_PARAM_TYPE {
  param?: Partial<REQUEST_POST_TASKWORKFLOW_LISTSEARCH_PARAM_TYPE>
  defaultAscs?: string[]
  defaultDescs?: string[]
  pageInfo: {
    pageNumber: number
    orderParams?: []
    size: number
  }
}

declare interface REQUEST_GET_TASKWORKFLOW_DATA {
	workflowName: string
	entrustsId: number | null
	templateId: number | null
	status:number|null
	workflowId: number | null
}
