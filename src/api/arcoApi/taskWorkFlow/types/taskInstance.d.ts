/**
 * 任务实例相关类型定义
 */

// 任务实例创建请求参数类型
declare interface REQUEST_POST_TASKINSTANCE_CREATE_PARAM_TYPE {
  /** 案件ID */
  caseId: string
  /** 任务类型 - 必填项，单项下拉选择，从任务类型字典选择 */
  taskType: number | null
  /** 任务时限(小时) - 非必填项，限制正整数，单位：时 */
  timeLimit: number |null
  /** 任务负责人ID集合 - 必填项，多选，来源于案源方账号 */
  managerIdList: number[]
  assignAccountId:string,
  assignTime:string |null,
}

// 任务实例处理请求参数类型
declare interface REQUEST_POST_TASKINSTANCE_PROCESS_PARAM_TYPE {
  /** 实例ID */
  instanceId: string
  /** 任务状态 - 2:进行中，3:已完成，4:已完成（异常） */
  taskStatus: number
  /** 备注 - 当状态为'已完成（异常）'时必填，最大长度500字符 */
  remark?: string
}

// 任务实例批量登记请求参数类型
declare interface REQUEST_POST_TASKINSTANCE_BATCHCREATE_PARAM_TYPE {
  /** 案件id集合 */
  caseIdList: (string | number)[]
  /** 任务类型 - 必填项，单项下拉选择，从任务类型字典选择 */
  taskType: number
  /** 任务时限(小时) - 非必填项，限制正整数，单位：时 */
  timeLimit?: number
  /** 任务负责人ID集合 - 必填项，多选，来源于案源方账号 */
  managerIdList: number[]
  /** 备注 */
  remark?: string
  /** 任务发起人id */
  assignAccountId: number
  /** 任务发起时间 */
  assignTime: string
  /** 是否已全选 */
  ifChooseAll?: boolean
  /** 查询案件列表条件bo */
  mediateCasePageBO?: OBJ_KEY_STR_ANY_TYPE
}

// 任务实例分页查询搜索参数类型
declare interface REQUEST_POST_TASKINSTANCE_LISTSEARCH_PARAM_TYPE {
  /** 实例ID */
  instanceId?: string
  /** 案件ID */
  caseId?: string
  /** 流程ID */
  workflowId?: number
  /** 配置ID */
  configId?: number
  /** 任务类型 */
  taskType?: number
  /** 当前状态 - 1:未开始 2:进行中 3:已完成 4:异常 */
  taskStatus?: number
  /** 发起人id */
  assignAccountId?: number
  /** 完成人id */
  finishAccountId?: number
  /** 发起时间开始 */
  assignTimeStart?: string
  /** 发起时间结束 */
  assignTimeEnd?: string
  /** 完成时间开始 */
  finishTimeStart?: string
  /** 完成时间结束 */
  finishTimeEnd?: string
  /** 创建时间开始 */
  createTimeStart?: string
  /** 创建时间结束 */
  createTimeEnd?: string
}

// 任务实例详情数据类型
declare interface REQUEST_GET_TASKINSTANCE_DATA_TYPE {
  /** 实例ID */
  instanceId: string
  /** 案件ID */
  caseId: string
  /** 流程ID */
  workflowId: string
  /** 配置ID */
  configId: string
  /** 任务类型 */
  taskType: number
  /** 任务时限(小时) */
  timeLimit?: number
	//截至时间
	deadline:string
  /** 任务负责人account_id集合 */
  managerIdList: number[]
	managerNames:string
  /** 当前状态 - 1:未开始 2:进行中 3:已完成 4:异常 */
  taskStatus: number
  /** 备注 */
  remark?: string
  /** 发起人id */
  assignAccountId: string
  /** 发起人姓名 - 系统自动发起时显示'自动' */
  assignAccountName: string
  /** 发起时间 */
  assignTime: string
  /** 完成人id */
  finishAccountId?: string
  /** 完成人姓名 */
  finishAccountName?: string
  /** 完成时间 */
  finishTime?: string
  /** 创建人 */
  creatorId: number
  /** 创建人姓名 */
  creatorName: string
  /** 更新人 */
  updaterId: number
  /** 更新人姓名 */
  updaterName: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

// 任务实例列表响应数据类型
declare interface REQUEST_GET_TASKINSTANCE_LIST_DATA_TYPE extends REQUEST_GET_TASKINSTANCE_DATA_TYPE {}
