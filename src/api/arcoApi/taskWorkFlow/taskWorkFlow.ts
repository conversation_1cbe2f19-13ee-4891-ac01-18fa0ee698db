/**
 * 业务配置/任务流程管理
 */
import { removeNullParam } from '@/utils'
import { baseURL } from '@/api/base.ts' // 导入接口域名列表
import axios from '@/utils/http.ts' // 导入http中创建的axios实例

// 任务流程管理接口

// POST 分页查询案源方列表
export const getFlowList = (
  params: REQUEST_POST_TASKWORKFLOW_LIST_PARAM_TYPE
): Promise<{ list: REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE[]; total: number }> => {
  params = removeNullParam(params)
  return axios.post(`${baseURL}/api/mis/task/workflow/pageList`, params)
}

// POST  添加|修改 任务流程
export const addTaskWorkFlow = (params: REQUEST_POST_TASKWORKFLOW_SAVE_PARAM_TYPE): Promise<number> => {
  return axios.post(`${baseURL}/api/mis/task/workflow/add`, JSON.stringify(params))
}
// GET 获取任务流程实体
export const getWorkFlowDetails =  (id: number): Promise<string> => {
	return axios.get(`${baseURL}/api/mis/task/workflow/${id}`)
}

// DELETE删除任务流程
export const deleteWorkFlow = (id: number): Promise<string> => {
  return axios.delete(`${baseURL}/api/mis/task/workflow/${id}`)
}

// GET获取所有委托方
export const getAllEntrusts = (): Promise<REQUEST_GET_ENTRUSTS_LIST_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/getAll`)
}

// GET 通过id查询任务流程
export const getEntrustsDetails = (id: number): Promise<REQUEST_POST_ENTRUSTS_SAVE_PARAM_TYPE> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/${id}`)
}

// GET /api/mis/sys/entrusts/getDeptInfoById/{entrustsId} 通过任务流程id获取部门信息
export const getDeptInfoById = (entrustsId: string | number): Promise<REQUEST_GET_ENTRUSTS_BYIDDEPT_DATA_TYPE[]> => {
  return axios.get(`${baseURL}/api/mis/sys/entrusts/getDeptInfoById/${entrustsId}`, { hideLoading: true })
}

export default {
  getEntrustsDetails,
	addTaskWorkFlow,
  getFlowList,
  getDeptInfoById,
  getAllEntrusts,
	deleteWorkFlow
}
