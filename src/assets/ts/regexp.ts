// 手机或电话
const phoneAndTelReg = new RegExp('(^([1][0-9]{10})*$)|' + '(^\\d{8,22}$)|(^\\d{3,6}-[1-9]\\d{4,9}(-\\d{1,6})?$)')

// 密码
const pwdReg = new RegExp(
  '^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,}$'
)

// 邮箱
const emailReg = new RegExp('^[A-Za-z0-9_\\.\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$')

// 手机号码(可验证多个，号码之间英文逗号分隔)
const phoneReg = /^([1][0-9]{10})*$/

export { phoneReg, emailReg, phoneAndTelReg, pwdReg }
