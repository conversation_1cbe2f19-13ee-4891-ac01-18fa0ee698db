*,
*:before,
*:after {
  box-sizing: border-box;
}

html {
  width: 100%;
  height: 100%;
}

body {
  font-family: 'Helvetica Neue', Helvetica, Tahoma, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', sans-serif;
  font-size: $--font-size-base;
  line-height: 1.15;
  color: #303030;
  width: 100%;
  height: 100%;
  // overflow: hidden;
  overflow: auto;

  & .app-container {
    padding: 10px;
  }
}

#app {
  width: 100%;
  height: 100%;
}

a {
  text-decoration: none;

  &:focus,
  &:hover {
    text-decoration: underline;
  }
}

img {
  vertical-align: middle;
}

// 公共样式
.mdt-common {
  // 标签
  &-tag {
    background-color: transparent !important;
    color: rgb(var(--primary-6)) !important;
    cursor: pointer;
    &:active {
      cursor: pointer;
      color: rgb(var(--primary-6));
    }
  }
  // 文本
  &-text {
    color: rgb(var(--color-1));
    &-active {
      cursor: pointer;
      color: rgb(var(--primary-6));
    }
    &-visited {
      color: var(--color-neutral-6);
    }
  }
  // 滚动条
  &-scrollbar {
    &::-webkit-scrollbar {
      width: 8px;
      height: 10px;
      overflow: auto;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(144, 147, 153, 0.5);
    }
  }
}

.mdt {
  // 表单
  &-form {
    &-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    &-split {
      height: 20px;
      margin-right: 10px;
      width: 4px;
      border-radius: 1px;
      background-color: rgb(var(--primary-6));
    }

    &-title {
      text-align: left;
      font-size: 16px;
      font-weight: 600;
    }
    &-item-label {
      max-width: 120px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &-option-value {
      width: 100%;
      display: inherit;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  // 卡片
  &-card {
    // 文本-淡灰
    &-textgray {
      color: rgb(198, 192, 192);
    }
    &-common {
      border-radius: 4px;
      border: none;
      & > .arco-card-header {
        // height: auto !important;
        border: none;
        padding: 16px 20px 0px 20px !important;
        .arco-card-header-title {
          font-weight: bold;
        }
      }
      & > .arco-card-body {
        padding: 16px 20px 16px 20px !important;
      }
    }
  }
  // 对话框
  &-modal {
    // arco Modal 底部样式（移除边框）
    .arco-modal-footer {
      text-align: center;
      border-top: none !important;
    }

    // arco Modal 底部样式（移除边框）
    .arco-modal {
      &-header {
        border-bottom: 3px solid rgb(var(--primary-6)) !important;
      }
      &-footer {
        border-top: none !important;
      }
    }
  }
  // 气泡卡片
  &-popover {
    &-ul {
      margin: 4px 0 8px 0;
      list-style: none;
      padding: 0;
      width: 100px;
    }
    &-li {
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      & + li {
        border-top: 1px solid #dedede;
      }
      &:hover,
      &:focus,
      &:active {
        background-color: lighten($color: $color-primary, $amount: 60%);
        color: $color-primary;
        font-weight: bold;
      }
    }
  }
  // 栅格
  &-col {
    &-flexend {
      display: flex;
      justify-content: end;
      align-items: center;
    }
  }
}
