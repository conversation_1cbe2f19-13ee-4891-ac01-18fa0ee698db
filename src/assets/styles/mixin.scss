// common
@mixin overflow-ellipsis($width: 100%) {
  width: $width;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}

@mixin title2-font {
  font-size: $--font-size-large;
  font-weight: bold;
}

@mixin normalPadding {
  padding: 12px 0px;
}

@mixin linePadding {
  padding: 6px 12px;
}

@mixin h-v-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin v-c {
  display: flex;
  align-items: center;
}

@mixin h-c {
  display: flex;
  justify-content: center;
}

@mixin pseudo {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  content: '';
  width: 100%;
  height: 100%;
}

@mixin mc($width) {
  margin: 0 auto;
  width: $width;
}

// self
@mixin bold-text {
  font: $--font-size-base/22px;
}

@mixin dialog-shadow {
  box-shadow: 0 10px 49px 1px rgba($color: #000, $alpha: 0.2);
}

@mixin login-bg {
  background-image: url('@/assets/images/bg.min.png');
  background-position: 100% 100%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

@mixin tab-hover {
  background-color: $color-primary;
}

// padding and margin
@mixin inside-mini-padding {
  padding: 3px 5px;
}

@mixin inside-small-padding {
  padding: 4px 8px;
}
@mixin inside-medium-padding {
  padding: 5px 10px;
}
@mixin inside-large-padding {
  padding: 15px 20px;
}
