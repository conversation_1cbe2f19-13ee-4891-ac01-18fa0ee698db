.global-loading {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    PingFang SC,
    Helvetica Neue,
    Noto Sans,
    Noto Sans CJK SC,
    Microsoft Yahei,
    Arial,
    Hiragino Sans GB,
    sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5715;
  color: #333;
  text-align: left;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 100000;
}

.global-loading .loading-wrapper {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    PingFang SC,
    Helvetica Neue,
    Noto Sans,
    Noto Sans CJK SC,
    Microsoft Yahei,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON> Sans GB,
    sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5715;
  color: #333;
  text-align: left;
  box-sizing: border-box;
  position: absolute;
  border-radius: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.global-loading .loading-wrapper .loading {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    PingFang SC,
    Helvetica Neue,
    Noto Sans,
    Noto Sans CJK SC,
    Microsoft Yahei,
    Arial,
    Hiragino Sans GB,
    sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5715;
  color: #333;
  text-align: left;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  animation: loading 1.3s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
}

.global-loading .loading-wrapper .loading > div {
  position: relative;
  width: 80px;
  height: 22px;
}

.global-loading .loading-wrapper .loading > div > span:nth-child(1) {
  transform: rotate(90deg);
}

.global-loading .loading-wrapper .loading > div > span:nth-child(2) {
  transform: rotate(210deg);
}

.global-loading .loading-wrapper .loading > div > span:nth-child(3) {
  transform: rotate(330deg);
}

.global-loading .loading-wrapper .loading > div > span:nth-child(1):before {
  background: #968de4;
}

.global-loading .loading-wrapper .loading > div > span:nth-child(2):before {
  background: #e14f3b;
}

.global-loading .loading-wrapper .loading > div > span:nth-child(3):before {
  background: #3a9189;
}

.global-loading .loading-wrapper .loading > div > span:before {
  content: '';
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  top: 0;
  left: 0;
  animation: loading-box 1.3s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
}

.global-loading .loading-wrapper .loading > div > span {
  position: absolute;
  width: 100%;
  height: 22px;
  top: 0;
  left: 0;
}

@keyframes loading {
  0% {
    transform: rotate(0);
  }

  92%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-box {
  0% {
    transform: translate(0);
  }
  20.57%,
  70.57% {
    transform: translate(60%);
  }
  92%,
  100% {
    transform: translate(0);
  }
}
