// utils 工具

.primary {
  color: $color-primary !important;
}

.success {
  color: $color-success !important;
}

.warning {
  color: $color-warning !important;
}

.danger {
  color: $color-danger !important;
}

.info {
  color: $color-info !important;
}

.bg-primary {
  background-color: $color-primary !important;
}

.bg-success {
  background-color: $color-success !important;
}

.bg-warning {
  background-color: $color-warning !important;
}

.bg-danger {
  background-color: $color-danger !important;
}

.bg-info {
  background-color: $color-info !important;
}

.bg-white {
  background-color: $color-white !important;
}

.text-black {
  color: $color-black;
}

.text-dark {
  color: $color-dark;
}

.text-deep {
  color: $color-deep;
}

.text-weak {
  color: $color-weak;
}

.text-white {
  color: $color-white;
}
.text-regular {
  color: $color-text-regular;
}
// 颜色
@each $num in 0,2,3,5,6,8,9{
  .color#{$num} {
    color: #{'#'}#{$num}#{$num}#{$num}#{$num}#{$num}#{$num} !important;
  }
}
// 文字对齐
.tl {
  text-align: left;
}

.tc {
  text-align: center;
}

.tr {
  text-align: right;
}

// 浮动与清除浮动
.fl {
  float: left;
}

.fr {
  float: right;
}

.fix:after {
  display: table;
  content: '';
  clear: both;
}

.clear{
  clear: both;
}

// 显示
.dn {
  display: none;
}

.di {
  display: inline;
}

.db {
  display: block;
}

.dib {
  display: inline-block;
}

.dix {
  display: inline-flex;
}

.dt {
  display: table;
}

.vm {
  vertical-align: middle;
}

.vt {
  vertical-align: top;
}

.vib {
  display: inline-block;
  vertical-align: middle;
}

// 定位
.pr {
  position: relative;
}

.pa {
  position: absolute;
}

.pf {
  position: fixed;
}

// 按钮禁用
.disabled {
  outline: 0 none;
  cursor: default !important;
  opacity: .4;
  filter: alpha(opacity=40);
  -ms-pointer-events: none;
  pointer-events: none;
}

//百分比
@for $i from 0 through 30 {
  .percent-#{5*$i} {
    display: inline-block;
    width: 5% * $i !important;
  }
  .pl#{2 * $i} {
    padding-left: 2px * $i !important;
  }
  .pr#{2 * $i} {
    padding-right: 2px * $i !important;
  }
  .pt#{2 * $i} {
    padding-top: 2px * $i !important;
  }
  .pb#{2 * $i} {
    padding-bottom: 2px * $i !important;
  }
  .padding#{2 * $i} {
    padding: 2px * $i !important;
  }
  .ml#{2 * $i} {
    margin-left: 2px * $i !important;
  }
  .mr#{2 * $i} {
    margin-right: 2px * $i !important;
  }
  .mt#{2 * $i} {
    margin-top: 2px * $i !important;
  }
  .mb#{2 * $i} {
    margin-bottom: 2px * $i !important;
  }
  .margin#{2 * $i} {
    margin: 2px * $i !important;
  }
  .lineHeight#{10 + 2 * $i} {
    line-height: (10px + 2px * $i) !important;
  }
  .f#{12 + $i} {
    font-size: (12px + $i) !important;
  }
}

.fs0{
  font-size:0;
}

.title2 {
  margin: $margin-vertical-medium 0;
  @include title2-font;
}

.title3 {
  margin: $margin-vertical-small 0;
  font-size: $heading3;
  font-weight: bold;
}

.title4 {
  margin: $margin-vertical-mini 0;
  font-size: $heading4;
  font-weight: bold;
}

.bold {
  font-weight: bold !important;
}

.flex {
  display:flex;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.invisible {
  visibility: hidden;
}

// 弹性布局
.h-v-c {
  @include h-v-c;
}

.v-c {
  @include v-c;
}

.h-c {
  @include h-c;
}

.f-g{
  flex-grow:1;
}

.justify- {
  &start {
    justify-content: flex-start;
  }
  &end {
    justify-content: flex-end;
  }
  &center {
    justify-content: center;
  }
  &between {
    justify-content: space-between;
  }
  &around {
    justify-content: space-around;
  }
  &evenly {
    justify-content: space-evenly;
  }
}

.items- {
  &start {
    align-items: flex-start;
  }
  &end {
    align-items: flex-end;
  }
  &center {
    align-items: center;
  }
  &baseline {
    align-items: baseline;
  }
  &stretch {
    align-items: stretch;
  }
}


.elli {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.break-all {
  word-break: break-all;
}

.form-half-item {
  width: 49%;
  display: inline-block;
  vertical-align: bottom;
}

.full-absolution {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.h100 {
  height: 100%;
}

.odr-scrollbar {
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    overflow: auto;
  }

  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(144, 147, 153, 0.5);
  }
}

// router-link
.router-link-auto {
  display: inline-block;
  width: 100%;
  height: 100%;
  text-align: center;
  text-decoration: none;
  color: inherit;
  &:hover,
  &:active,
  &:focus,
  &:visited {
    text-decoration: none;
  }
}
