/*
* 换肤
*/
$theme-selector-1: "[data-theme='theme1'] &";
$theme-selector-2: "[data-theme='theme2'] &";
$theme-selector-3: "[data-theme='theme3'] &";

@mixin font_size($size) {
  /*通过该函数设置字体大小，后期方便统一管理；*/
  @include font-dpr($size);
}

@mixin font_color(
  $color: $color-primary,
  $color1: $font-color-theme1,
  $color2: $font-color-theme2,
  $color3: $font-color-theme3
) {
  /*通过该函数设置字体颜色，后期方便统一管理；*/
  color: $color;

  #{$theme-selector-1} {
    color: $color1;
  }

  #{$theme-selector-2} {
    color: $color2;
  }

  #{$theme-selector-3} {
    color: $color3;
  }
}

@mixin bg_color(
  $color: $color-primary,
  $color1: $background-color-theme1,
  $color2: $background-color-theme2,
  $color3: $background-color-theme3
) {
  /*通过该函数设置主题颜色，后期方便统一管理；*/
  background-color: $color;

  #{$theme-selector-1} {
    background-color: $color1;
  }

  #{$theme-selector-2} {
    background-color: $color2;
  }

  #{$theme-selector-3} {
    background-color: $color3;
  }
}

@mixin border_color(
  $color: $color-primary,
  $color1: $border-color-theme1,
  $color2: $border-color-theme2,
  $color3: $border-color-theme3
) {
  /*通过该函数设置主题颜色，后期方便统一管理；*/
  border-color: $color;

  #{$theme-selector-1} {
    border-color: $color1;
  }

  #{$theme-selector-2} {
    border-color: $color2;
  }

  #{$theme-selector-3} {
    border-color: $color3;
  }
}

@mixin bg_img_linear_part($color) {
  $end-color: mix($color, #fff, 66%);
  background-image: linear-gradient(-30deg, #{$color} 0%, #{$end-color} 100%);
}
@mixin bg_img_linear(
  $color: $color-primary,
  $color1: $border-color-theme1,
  $color2: $border-color-theme2,
  $color3: $border-color-theme3
) {
  /*通过该函数设置主题颜色，后期方便统一管理；*/
  @include bg_img_linear_part($color);
  #{$theme-selector-1} {
    @include bg_img_linear_part($color1);
  }

  #{$theme-selector-2} {
    @include bg_img_linear_part($color2);
  }

  #{$theme-selector-3} {
    @include bg_img_linear_part($color3);
  }
}
