/*Brand Color*/
// $color-primary: #397ee6 !default;
$color-primary: #0f6b9c !default;
// $color-primary: #008c8c !default;
$color-success: #67c23a !default;
$color-warning: #e6a23c !default;
$color-danger: #f56c6c !default;
$color-info: #336699 !default;

// 文字色
$color-black: #000 !default;
$color-dark: #222 !default;
$color-text-regular: #404044 !default;
$color-deep: #555 !default;
$color-pl: $color-info !default;
$color-weak: #888 !default;
$color-white: #fff !default;

//Border Color
$border-color-base: #d9d9d9 !default;

//Background Color

//Background Color
$color-primary-blue: #5aabff;
$color-primary-blue-light: #4eb3f8;
$color-primary-light: lighten(
  $color: $color-primary,
  $amount: 15%
);
$color-primary-lighter: lighten(
  $color: $color-primary,
  $amount: 35%
);
$color-primary-lightest: #bcd1f1;
$background-color-base: #f5f7fa;
// $color-primary-dark: #174299;
// $color-primary-dark: #008c8c;
$color-primary-dark: #0f6b9c;

//Font Size
$heading1:20px;//主标题文字大小
$heading2:18px;//标题文字大小
$heading3:16px;//小标题文字大小
$heading4:14px;//正文文字大小
$heading5:13px;//正文（小）文字大小
$heading6:12px;//辅助文字大小

//Font Size
/// fontSize|1|Font Size|0
$--font-size-extra-large: 20px !default;
/// fontSize|1|Font Size|0
$--font-size-large: 18px !default;
/// fontSize|1|Font Size|0
$--font-size-medium: 16px !default;
/// fontSize|1|Font Size|0
$--font-size-base: 14px !default;
/// fontSize|1|Font Size|0
$--font-size-small: 13px !default;
/// fontSize|1|Font Size|0
$--font-size-extra-small: 12px !default;

//Border
$--radius-large: 6px !default; //大圆角
$--radius-medium: 4px !default; //中圆角
$--radius-small: 3px !default; //小圆角
$--radius-mini: 3px !default; //最小圆角

/// fontWeight|1|Font Weight|1
$--font-weight-primary: 500 !default;
/// fontWeight|1|Font Weight|1
$--font-weight-secondary: 100 !default;
/// fontLineHeight|1|Line Height|2
$--font-line-height-primary: 22px !default;
/// fontLineHeight|1|Line Height|2
$--line-height: 18px !default;
/// fontLineHeight|1|Line Height|2
$--font-line-height-secondary: 16px !default;

//Padding内边距
//padding-vertical
$padding-vertical-large: 12px; // 大
$padding-vertical-medium: 10px; // 中
$padding-vertical-small: 9px; // 小
$padding-vertical-mini: 7px; //最小
//padding-horizontal
$padding-horizontal-large: 20px; // 大
$padding-horizontal-medium: 20px; // 中
$padding-horizontal-small: 15px; // 小
$padding-horizontal-mini: 15px; //最小

//Margin外边距
//Margin-vertical
$margin-vertical-large: 20px; // 大
$margin-vertical-medium: 15px; // 中
$margin-vertical-small: 10px; // 小
$margin-vertical-mini: 5px; //最小
//Margin-horizontal
$margin-horizontal-large: 20px; // 大
$margin-horizontal-medium: 15px; // 中
$margin-horizontal-small: 10px; // 小
$margin-horizontal-mini: 5px; //最小

//normal pages
$--normal-footer-height: 60px !default;

$--page-width: 1600px !default;
$--nav-text-color: $color-white !default;
$--header-height: 54px !default;
$--page-header-padding: 0 34px !default;
$--nav-bar-height: 32px !default;
$--nav-tab-width: 160px !default;
$--footer-height: 48px !default;

//预定义样式
$--shadow-base: 0 2px 5px 0 rgba(8, 15, 38, 0.1) !default;
$--border-base: 1px solid $border-color-base !default;
$--background-color-base: rgba($color-primary, 0.1) !default;

:export {
  pageWidth: $--page-width;
  navTabWidth: $--nav-tab-width;
  headerHeight: $--header-height;
  navBarHeight: $--nav-bar-height;
  footerHeight: $--footer-height;
  navTextColor: $--nav-text-color;
  colorWhite: $color-white;
  colorBlack: $color-black;
  colorDark: $color-dark;
  colorWeak: $color-weak;
}
