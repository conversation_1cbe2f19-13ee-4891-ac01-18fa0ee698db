// Interface data format used to return a unified format
export const baseURL = '/mockBase'

export function resultSuccess(result: OBJ_KEY_STR_ANY_TYPE | undefined | string, { message = 'ok' } = {}) {
  return { code: 0, result, message }
}

export function resultPageSuccess(
  pageNumber: number,
  size: number,
  list: OBJ_KEY_STR_ANY_TYPE[],
  { message = 'ok' } = {}
) {
  const pageData = pagination(pageNumber, size, list)

  return {
    ...resultSuccess({ list: pageData, total: list.length, pageNumber, size }),
    message
  }
}

export function resultError(message = 'Request failed', { code = -1, result = null } = {}) {
  return { code, result, message }
}

export function pagination(pageNo: number, size: number, array: OBJ_KEY_STR_ANY_TYPE[]) {
  const offset = (pageNo - 1) * Number(size)
  const ret =
    offset + Number(size) >= array.length
      ? array.slice(offset, array.length)
      : array.slice(offset, offset + Number(size))
  return ret
}

/**
 * @description 本函数用于从request数据中获取token，请根据项目的实际情况修改
 *
 */
export function getRequestToken({ headers }: OBJ_KEY_STR_ANY_TYPE) {
  return headers?.authorization
}
