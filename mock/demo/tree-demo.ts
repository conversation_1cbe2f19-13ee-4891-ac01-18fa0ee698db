import { resultSuccess } from '../_util'

const demoTreeList = (keyword: string) => {
  type LIST_CHILDREN_TYPE = { title: String; value: string; key: string; children?: LIST_CHILDREN_TYPE[] }
  const result: { list: LIST_CHILDREN_TYPE[] } = { list: [] }
  for (let index = 0; index < 5; index++) {
    const children = []
    for (let j = 0; j < 3; j++) {
      children.push({
        title: `${keyword ?? ''}选项${index}-${j}`,
        value: `${index}-${j}`,
        key: `${index}-${j}`
      })
    }
    result.list.push({ title: `${keyword ?? ''}选项${index}`, value: `${index}`, key: `${index}`, children })
  }
  return result
}

export default [
  {
    url: '/api/tree/getDemoOptions',
    timeout: 1000,
    method: 'get',
    response: (res: { query: { keyword: string } }) => {
      const { keyword } = res.query
      return resultSuccess(demoTreeList(keyword))
    }
  }
]
