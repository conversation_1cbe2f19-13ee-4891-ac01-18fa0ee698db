import { resultPageSuccess } from '../_util'
import { Random } from 'mockjs'

function getRandomPics(count = 10) {
  const arr = []
  for (let i = 0; i < count; i++) {
    arr.push(Random.image('800x600', Random.color(), Random.color(), Random.title()))
  }
  return arr
}

const list = (() => {
  const result = []
  for (let index = 0; index < 200; index++) {
    result.push({
      id: `${index}`,
      beginTime: '@datetime',
      endTime: '@datetime',
      address: '@city()',
      name: '@cname()',
      name1: '@cname()',
      name2: '@cname()',
      name3: '@cname()',
      name4: '@cname()',
      name5: '@cname()',
      name6: '@cname()',
      name7: '@cname()',
      name8: '@cname()',
      avatar: Random.image('400x400', Random.color(), Random.color(), Random.first()),
      imgArr: getRandomPics(Math.ceil(Math.random() * 3) + 1),
      imgs: getRandomPics(Math.ceil(Math.random() * 3) + 1),
      date: `@date('yyyy-MM-dd')`,
      time: `@time('HH:mm')`,
      'no|100000-10000000': 100000,
      'status|1': ['normal', 'enable', 'disable']
    })
  }
  return result
})()

// hasNext: true
// hasPrevious: false
// lastPageNum: 105
// list: [{arbMdtAmount: null, batchNo: "798999", caseBatchId: "975888845407129600",…},…]
// pageNumber: 1
// size: 10
// total: 1046
export default [
  {
    url: '/api/table/getDemoList',
    timeout: 100,
    method: 'post',
    response: (res: { body: { pageInfo: { pageNumber: number; size: number } } }) => {
      const { pageNumber = 1, size = 20 } = res.body.pageInfo
      return resultPageSuccess(pageNumber, size, list)
    }
  }
]
