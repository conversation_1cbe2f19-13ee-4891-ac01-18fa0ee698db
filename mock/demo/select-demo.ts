import { resultSuccess } from '../_util'

const demoList = (keyword: string, count = 20) => {
  const result: { list: { name: string; id: string }[] } = { list: [] }
  for (let index = 0; index < count; index++) {
    result.list.push({ name: `${keyword ?? ''}选项${index}`, id: `${index}` })
  }
  return result
}

export default [
  {
    url: '/api/select/getDemoOptions',
    timeout: 1000,
    method: 'get',
    response: (res: { query: { keyword: string; count: number } }) => {
      const { keyword, count } = res.query
      return resultSuccess(demoList(keyword, count))
    }
  }
]
