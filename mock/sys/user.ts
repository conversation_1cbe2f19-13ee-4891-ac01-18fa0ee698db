import { resultError, resultSuccess, getRequestToken } from '../_util'

export function createFakeUserList() {
  return [
    {
      userId: '1',
      username: 'vben',
      realName: 'Vben Admin',
      avatar: 'https://q1.qlogo.cn/g?b=qq&nk=190848757&s=640',
      desc: 'manager',
      password: '123456',
      token: 'fakeToken1',
      homePath: '/dashboard/analysis',
      roles: [{ roleName: 'Super Admin', value: 'super' }]
    },
    {
      userId: '2',
      username: 'test',
      password: '123456',
      realName: 'test user',
      avatar: 'https://q1.qlogo.cn/g?b=qq&nk=339449197&s=640',
      desc: 'tester',
      token: 'fakeToken2',
      homePath: '/dashboard/workbench',
      roles: [{ roleName: 'Tester', value: 'test' }]
    }
  ]
}

const fakeCodeList: { [x: string]: ARR_STRING_TYPE } = {
  1: ['1000', '3000', '5000'],
  2: ['2000', '4000', '6000']
}
export default [
  // mock user login
  {
    url: '/api/login',
    timeout: 200,
    method: 'post',
    response: ({ body }: OBJ_KEY_STR_ANY_TYPE) => {
      const { username, password } = body
      const checkUser = createFakeUserList().find((item) => {
        return item.username === username && password === item.password
      })
      if (!checkUser) {
        return resultError('Incorrect account or password！')
      }
      const { userId, username: _username, token, realName, desc, roles } = checkUser
      return resultSuccess({
        roles,
        userId,
        username: _username,
        token,
        realName,
        desc
      })
    }
  },
  {
    url: '/api/getUserInfo',
    method: 'get',
    response: (request: OBJ_KEY_STR_ANY_TYPE) => {
      const token = getRequestToken(request)
      if (!token) {
        return resultError('Invalid token')
      }
      const checkUser = createFakeUserList().find((item) => {
        return item.token === token
      })
      if (!checkUser) {
        return resultError('The corresponding user information was not obtained!')
      }
      return resultSuccess(checkUser)
    }
  },
  {
    url: '/api/getPermCode',
    timeout: 200,
    method: 'get',
    response: (request: OBJ_KEY_STR_ANY_TYPE) => {
      const token = getRequestToken(request)
      if (!token) {
        return resultError('Invalid token')
      }
      const checkUser = createFakeUserList().find((item) => item.token === token)
      if (!checkUser) {
        return resultError('Invalid token!')
      }
      const codeList = fakeCodeList[checkUser.userId]

      return resultSuccess(codeList)
    }
  },
  {
    url: '/api/logout',
    timeout: 200,
    method: 'get',
    response: (request: OBJ_KEY_STR_ANY_TYPE) => {
      const token = getRequestToken(request)
      if (!token) {
        return resultError('Invalid token')
      }
      const checkUser = createFakeUserList().find((item) => {
        return item.token === token
      })
      if (!checkUser) {
        return resultError('Invalid token!')
      }
      return resultSuccess(undefined, { message: 'Token has been unmounted' })
    }
  },
  {
    url: '/api/testRetry',
    statusCode: 405,
    method: 'get',
    response: () => resultError('Error!')
  }
]
