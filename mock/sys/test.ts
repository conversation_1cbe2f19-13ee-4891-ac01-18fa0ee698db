import { Random } from 'mockjs'
import { resultSuccess, resultPageSuccess } from '../_util'

const userInfo = {
  name: '<PERSON><PERSON>',
  userid: '00000001',
  email: '<EMAIL>',
  signature: '海纳百川，有容乃大',
  introduction: '微笑着，努力着，欣赏着',
  title: '交互专家',
  group: '某某某事业群－某某平台部－某某技术部－UED',
  tags: [
    { key: '0', label: '很有想法的' },
    { key: '1', label: '专注设计' },
    { key: '2', label: '辣~' },
    { key: '3', label: '大长腿' },
    { key: '4', label: '川妹子' },
    { key: '5', label: '海纳百川' }
  ],
  notifyCount: 12,
  unreadCount: 11,
  country: 'China',
  address: 'Xiamen City 77',
  phone: '0592-268888888'
}

function getRandomPics(count = 10) {
  const arr = []
  for (let i = 0; i < count; i++) {
    arr.push(Random.image('800x600', Random.color(), Random.color(), Random.title()))
  }
  return arr
}

const total = 200
const list = (() => {
  const result = []
  for (let index = 0; index <= total; index++) {
    result.push({
      id: `${index}`,
      beginTime: '@datetime',
      endTime: '@datetime',
      address: '@city()',
      name: '@cname()',
      name1: '@cname()',
      name2: '@cname()',
      name3: '@cname()',
      name4: '@cname()',
      name5: '@cname()',
      name6: '@cname()',
      name7: '@cname()',
      name8: '@cname()',
      avatar: Random.image('400x400', Random.color(), Random.color(), Random.first()),
      imgArr: getRandomPics(Math.ceil(Math.random() * 3) + 1),
      imgs: getRandomPics(Math.ceil(Math.random() * 3) + 1),
      date: `@date('yyyy-MM-dd')`,
      time: `@time('HH:mm')`,
      'no|100000-10000000': 100000,
      'status|1': ['normal', 'enable', 'disable']
    })
  }
  return result
})()

export default [
  {
    url: '/api/mis/test1',
    timeout: 1000,
    method: 'get',
    response: () => resultSuccess('123456')
  },
  {
    url: '/api/mis/test2',
    timeout: 1000,
    method: 'post',
    response: () => resultSuccess(userInfo)
  },
  {
    url: '/api/mis/test3',
    timeout: 100,
    method: 'post',
    response: ({ body: { pageInfo } }: OBJ_KEY_STR_ANY_TYPE) => {
      const { pageNumber = 1, size = 20 } = pageInfo
      return resultPageSuccess(pageNumber, size, list)
    }
  }
]
