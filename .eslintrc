{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "globals": {
    "postMessage": true
  },
  "extends": [
    "plugin:vue/vue3-strongly-recommended",
    "plugin:vue/vue3-recommended",
    "plugin:vue/vue3-essential",
    "eslint:recommended"
  ],
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true,
      "tsx": true
    }
  },
  "plugins": ["vue"],
  /*
   * "off" 或 0    ==>  关闭规则
   * "warn" 或 1   ==>  打开的规则作为警告（不影响代码执行）
   * "error" 或 2  ==>  规则作为一个错误（代码不能执行，界面报错）
   */
  "rules": {
    "comma-dangle": ["error", "never"], //是否允许对象中出现结尾逗号
    "no-cond-assign": 2, //条件语句的条件中不允许出现赋值运算符
    "no-console": 0, //不允许出现console语句
    "no-constant-condition": 2, //条件语句的条件中不允许出现恒定不变的量
    "no-control-regex": 2, //正则表达式中不允许出现控制字符
    "no-debugger": 2, //不允许出现debugger语句
    "no-dupe-args": 2, //函数定义的时候不允许出现重复的参数
    "no-dupe-keys": 2, //对象中不允许出现重复的键
    "no-duplicate-case": 2, //switch语句中不允许出现重复的case标签
    "no-empty": 0, //不允许出现空的代码块
    "no-empty-character-class": 2, //正则表达式中不允许出现空的字符组
    "no-ex-assign": 2, //在try catch语句中不允许重新分配异常变量
    "no-extra-boolean-cast": 2, //不允许出现不必要的布尔值转换
    "no-extra-parens": 0, //不允许出现不必要的圆括号
    "no-extra-semi": 2, //不允许出现不必要的分号
    "no-func-assign": 2, //不允许重新分配函数声明
    "no-inner-declarations": ["error", "functions"], //不允许在嵌套代码块里声明函数
    "no-invalid-regexp": 2, //不允许在RegExp构造函数里出现无效的正则表达式
    "no-irregular-whitespace": 2, //不允许出现不规则的空格
    "no-negated-in-lhs": 2, //不允许在in表达式语句中对最左边的运算数使用取反操作
    "no-obj-calls": 2, //不允许把全局对象属性当做函数来调用
    "no-regex-spaces": 2, //正则表达式中不允许出现多个连续空格
    "quote-props": 0, //对象中的属性名是否需要用引号引起来
    "no-sparse-arrays": 2, //数组中不允许出现空位置
    "no-unreachable": 2, //在return，throw，continue，break语句后不允许出现不可能到达的语句
    "curly": 0, //强制使用花括号的风格
    "no-useless-escape": 0, // 禁止不必要的转义字符
    // "curly": ["error", "all"], //强制使用花括号的风格
    "default-case": 0, //在switch语句中需要有default语句
    "no-alert": 0, //不允许使用alert，confirm，prompt语句
    "no-caller": 2, //不允许使用arguments.callee和arguments.caller属性
    "guard-for-in": 0, //监视for in循环，防止出现不可预料的情况
    "no-div-regex": 2, //不能使用看起来像除法的正则表达式
    "no-else-return": 0, //如果if语句有return，else里的return不用放在else里
    "no-labels": [
      "error",
      {
        "allowLoop": false,
        "allowSwitch": false
      }
    ], //不允许标签语句
    "no-eq-null": 0, //不允许对null用==或者!=
    "no-eval": 2, //不允许使用eval()
    "no-extend-native": 2, //不允许扩展原生对象
    "no-extra-bind": 2, //不允许不必要的函数绑定
    "no-fallthrough": 2, //不允许switch按顺序全部执行所有case
    "no-floating-decimal": 2, //不允许浮点数缺失数字
    "no-implied-eval": 2, //不允许使用隐式eval()
    "no-iterator": 2, //不允许使用__iterator__属性
    "no-lone-blocks": 2, //不允许不必要的嵌套代码块
    "no-loop-func": 2, //不允许在循环语句中进行函数声明
    "no-multi-spaces": 2, //不允许出现多余的空格
    "no-multi-str": 2, //不允许用\来让字符串换行
    "no-global-assign": 2, //不允许重新分配原生对象
    "no-new": 2, //不允许new一个实例后不赋值或者不比较
    "no-new-func": 2, //不允许使用new Function
    "no-new-wrappers": 2, //不允许使用new String，Number和Boolean对象
    "no-octal": 2, //不允许使用八进制字面值
    "no-octal-escape": 2, //不允许使用八进制转义序列
    "no-redeclare": 2, //不允许变量重复声明
    "no-return-assign": 2, //不允许在return语句中使用分配语句
    "no-script-url": 2, //不允许使用javascript:void(0)
    "no-self-compare": 0, //不允许自己和自己比较
    "no-sequences": 2, //不允许使用逗号表达式
    "no-throw-literal": 2, //不允许抛出字面量错误 throw "error"
    "no-unused-expressions": 0, //不允许无用的表达式
    "no-void": 0, //不允许void操作符
    "no-warning-comments": [
      1,
      {
        "terms": ["todo", "fixme", "any other term"]
      }
    ], //不允许警告备注
    "no-with": 2, //不允许使用with语句
    "radix": 1, //使用parseInt时强制使用基数来指定是十进制还是其他进制
    "vars-on-top": 0, //var必须放在作用域顶部
    "wrap-iife": [2, "any"], //立即执行表达式的括号风格
    "no-catch-shadow": 2, //不允许try catch语句接受的err变量与外部变量重名
    "no-delete-var": 2, //不允许使用delete操作符
    "no-label-var": 2, //不允许标签和变量同名
    "no-shadow": 0, //外部作用域中的变量不能与它所包含的作用域中的变量或参数同名
    "no-shadow-restricted-names": 2, //js关键字和保留字不能作为函数名或者变量名
    "no-undef": 0, //不允许未声明的变量
    "no-unused-vars": 0,
    "no-unused-locals": 0,
    "no-undef-init": 0, //不允许初始化变量时给变量赋值undefined
    "no-undefined": 0, //不允许把undefined当做标识符使用
    //  每行的最大属性数
    "vue/max-attributes-per-line": [
      "error",
      {
        "singleline": {
          "max": 10
        },
        "multiline": {
          "max": 10
        }
      }
    ],
    "vue/html-self-closing": 0,
    "vue/singleline-html-element-content-newline": [0, "always"],
    "no-plusplus": 0, // 不允许使用++ --运算符
    "vue/require-default-prop": 0, // props必须有默认参数
    "vue/multi-word-component-names": 0 // 组件名必须多字
  },
  "overrides": [
    {
      "files": ["src/assets/iconfont/iconfont.js"],
      "rules": { "no-catch-shadow": "off", "no-sequences": "off", "no-self-assign": "off" }
    }
  ]
}
