{"compilerOptions": {"baseUrl": ".", "target": "esnext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["./src/*"], "@mock/*": ["./mock/*"], "@scss/*": ["./src/assets/styles/*"], "@arco/*": ["./src/layouts/appArco/*"]}, "types": ["vite/client", "element-plus/global"]}, "include": ["src", "src/**/*.vue", "viteconfig", "mock", "types", "vite.config.mts"], "exclude": ["nodeServer"]}