import { vitePluginForArco } from '@arco-plugins/vite-vue'
// import topLevelAwait from 'vite-plugin-top-level-await'
import viteCompression from 'vite-plugin-compression'
import { createHtmlPlugin } from 'vite-plugin-html'
import configVisualizerPlugin from './visualizer'
import { viteMockServe } from 'vite-plugin-mock'
import px2rem from 'postcss-pxtorem'
// import legacyPlugin from '@vitejs/plugin-legacy'
// import { VitePWA } from 'vite-plugin-pwa'
import vue from '@vitejs/plugin-vue'

import type { PluginOption } from 'vite'

const plugins: PluginOption[] = [
  vue(),
  // VitePWA(),
  createHtmlPlugin({ minify: false, inject: { data: { title: '', injectScript: '' } } }),
  // 打包出兼容传统浏览器的代码
  // legacyPlugin({
  //   targets: ['> 1%, last 2 version, ie >= 11'],
  //   additionalLegacyPolyfills: ['regenerator-runtime/runtime']
  // }),
  // topLevelAwait({
  //   promiseImportName: (i) => `__yla_${i}`,
  //   promiseExportName: '__yla'
  // }),
  viteCompression({
    deleteOriginFile: false,
    threshold: 10240,
    algorithm: 'gzip',
    disable: false,
    verbose: true
  }),
  // mock配置
  viteMockServe({ mockPath: 'mock', watchFiles: true }),
  // 样式按需引入
  vitePluginForArco({}),
  // propList：需要进行转换的css属性的值，*意思是将全部属性单位都进行转换
  px2rem({
    rootValue: 10, // 默认100，根据实际情况来
    unitPrecision: 5, // 精度控制
    mediaQuery: false, // 媒体查询中是否转换
    minPixelValue: 2, // 被转换的最小值，默认0
    propWhiteList: [], // 白名单
    propBlackList: [], // 黑名单
    selectorBlackList: [],
    // exclude: /node_modules/i, // 可以是正则，默认false
    propList: ['*']
  })
]

// 配置可视化分析
let VisualizerPlugin = configVisualizerPlugin()

VisualizerPlugin && plugins.push(VisualizerPlugin)

export default plugins
