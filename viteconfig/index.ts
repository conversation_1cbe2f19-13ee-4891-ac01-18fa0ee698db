import type { UserConfig } from 'vite'

import { loadEnv } from 'vite'
import * as path from 'path'

import plugins from './plugins'
import server from './server'
import build from './build'

// 相对路径viteconfig ../
const pathResolve = (dir: string) => path.resolve(__dirname, `../${dir}`)
// const pathJoin = (dir: string) => path.join(__dirname, `../${dir}`)

const viteConfig: (mode: string) => UserConfig = (mode) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    root: process.cwd(),
    base: env.NODE_ENV === 'development' ? '' : '/', // (history 用 /) (hash 用./)
    define: { 'process.env': env },
    resolve: {
      // 导入文件时，可以省略文件的后缀，默认支持js后缀，不支持vue后缀。下面配置导入文件时可省略的后缀，此配置是覆盖，非合并
      extensions: ['.vue', '.js', '.jsx', '.ts', '.tsx', '.json', '.mjs'], // 需要忽略的文件后缀
      // 配置路径别名
      alias: [
        { find: '@', replacement: pathResolve('src') },
        { find: '@mock', replacement: pathResolve('mock') },
        { find: '@scss', replacement: pathResolve('src/assets/styles') },
        { find: '@arco', replacement: pathResolve('src/layouts/appArco') },

        // appAcro
        { find: 'vue', replacement: 'vue/dist/vue.esm-bundler.js' },
        { find: 'vue-i18n', replacement: 'vue-i18n/dist/vue-i18n.cjs.js' }
      ]
    },
    css: {
      // 定义scss全局文件
      preprocessorOptions: {
        scss: { additionalData: `@import "@scss/preset.scss";`, charset: false },
        less: {
          modifyVars: { 'arcoblue-6': '#3a9189' },
          javascriptEnabled: true
        }
      },
      postcss: {
        plugins: [
          {
            // element-plus的index.css文件包含 @chartset:UFT-8;配置postcss删除库里的@chartset:UFT-8;
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    },
    plugins,
    build,
    server
  }
}

export default viteConfig
