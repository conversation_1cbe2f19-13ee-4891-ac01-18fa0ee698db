/**
 * Generation packaging analysis
 * 生成打包分析
 */
import { visualizer } from 'rollup-plugin-visualizer'
import { Plugin } from 'vite'

export default function configVisualizerPlugin() {
  if (process.env.REPORT === 'true') {
    return visualizer({
      filename: './node_modules/.cache/visualizer/stats.html',
      brotliSize: true,
      gzipSize: true,
      open: true
    }) as Plugin
  }
  return null
}
