import type { BuildOptions } from 'vite'

const minify = 'terser'

const assetsDir = 'assets'

const timestamp = new Date().getTime()

const outDir = getNpmConfigArgv() || 'dist'

const rollupOptions: BuildOptions['rollupOptions'] = {
  output: {
    chunkFileNames: `js/[name]${timestamp}-[hash].js`,
    entryFileNames: `js/[name]${timestamp}-[hash].js`,
    assetFileNames: `[ext]/[name]-[hash].[ext]`,
    manualChunks: {
      vue: ['vue'],
      echarts: ['echarts'],
      'element-plus': ['element-plus'],
      'arco-design': ['@arco-design/web-vue']
    }
  }
}

const terserOptions: BuildOptions['terserOptions'] = {
  compress: {
    keep_infinity: true,
    drop_debugger: true,
    drop_console: true
  }
}

function getNpmConfigArgv() {
  if (process.env && process.env.npm_config_argv) {
    let configArgv = process.env.npm_config_argv
    if (configArgv) {
      let original = JSON.parse(configArgv).original
      if (original && original.length) {
        return `dist-${original[0].split(':')[1]}`
      }
    }
  }
  return ''
}

export default <BuildOptions>{
  target: 'esnext',
  minify,
  outDir,
  assetsDir,
  rollupOptions,
  terserOptions
}
