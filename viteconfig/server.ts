import type { ServerOptions } from 'vite'

export default <ServerOptions>{
  host: '0.0.0.0',
  port: 3091,
  open: false,
  proxy: {
    '/local': {
      target: 'http://127.0.0.1:8086/',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/local/, '')
    },

    '/mediate/dev': {
      // 内网-云桌面
      target: 'http://localhost:8096/mdt/', // 柏舒
      // target: 'http://**************:8096/mdt/', // 小马哥
      // 内网-本地机器
      // target: 'http://*************:8096/mdt/', // 柏舒
      // target: 'http://*************:8096/mdt/', // 小马哥
      // target: 'http://*************:8096/mdt/', // 林杰

      // 内网代理
      // target: 'http://*************:3090/mdtybs/', // 柏舒
      // target: 'http://*************:8096/mdt/', // 小马哥
      // 本地代理
      // target: 'http://*************:3090/mdt/',  // 测试环境-nginx代理
      // target: 'http://*************:3090/mdtybs/',  // 柏舒-nginx代理
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/dev/, '')
    },
    '/mediate/baishu': {
      // 内网-本地机器
      target: 'http://*************:8096/mdt/', // 柏舒
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/baishu/, '')
    },
    '/mediate/linjie': {
      // 内网-本地机器
      target: 'http://*************:8096/mdt/', // 林杰
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/linjie/, '')
    },
    '/mediate/maheng': {
      // 内网-本地机器
      target: 'http://*************:8096/mdt/', // 马恒
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/maheng/, '')
    },
    '/mediate/liangzhu': {
      // 内网-本地机器
      target: 'http://192.168.10.92:8099/mdt/', // 良柱
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/liangzhu/, '')
    },
    '/mediate/mingming': {
      // 内网-本地机器
      target: 'http://192.168.10.93:8096/mdt/', // 明明
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/mingming/, '')
    },
    '/mediate/cloudlinjie': {
      // 内网-云桌面
      target: 'http://**************:8096/mdt/', // 林杰
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/cloudlinjie/, '')
    },
    '/mediate/cloudmingming': {
      // 内网-云桌面
      target: 'http://**************:8096/mdt/', // 明明
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/cloudmingming/, '')
    },
    '/mediate/yiliang': {
      // 内网-云桌面
      target: 'http://localhost:8096/mdt/', // 怡良
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/mediate\/yiliang/, '')
    }
  }
}
