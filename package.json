{"name": "newmediateui", "private": true, "version": "0.0.0", "type": "module", "description": "一站式智慧调解管理平台", "scripts": {"dev": "vite", "build": "vite build", "prepare": "husky install", "lint-staged": "npx lint-staged", "prettier": "npx prettier --write .", "report": "cross-env REPORT=true pnpm run build", "preview": "pnpm run build && vite preview --host", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "serve:baishu": "cross-env prefix=baishu vite --port 4089", "serve:linjie": "cross-env prefix=linjie vite --port 4090", "serve:maheng": "cross-env prefix=maheng vite --port 4091", "serve:liangzhu": "cross-env prefix=liangzhu vite --port 4092", "serve:mingming": "cross-env prefix=mingming vite --port 4093", "serve:cloudlinjie": "cross-env prefix=cloudlinjie vite --port 4094", "serve:cloudmingming": "cross-env prefix=cloudmingming vite --port 4095", "serve:yiliang": "cross-env prefix=yiliang vite --port 4095"}, "engines": {"node": ">=16.0.0", "yarn": ">=1.22.0", "pnpm": ">=7.10.0"}, "pre-commit": ["prettier"], "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css,scss}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@arco-design/web-vue": "^2.56.0", "@element-plus/icons-vue": "^2.3.1", "@vue/runtime-core": "^3.4.35", "@vueuse/core": "^10.11.0", "@xylink/xy-rtc-sdk": "^4.0.0", "axios": "^1.7.3", "core-js": "^3.37.1", "dayjs": "^1.11.12", "echarts": "^5.5.1", "element-plus": "^2.7.8", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-toc-done-right": "^4.2.0", "merge-images": "^2.0.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nanoid": "^5.1.3", "nprogress": "^0.2.0", "pinia": "^2.2.0", "pinia-plugin-persistedstate": "^3.2.1", "postcss-pxtorem": "^6.1.0", "qs": "^6.13.0", "signature_pad": "^5.0.2", "sortablejs": "^1.15.2", "uuid": "^10.0.0", "vite-plugin-pwa": "^0.20.1", "vue": "^3.4.35", "vue-echarts": "7.0.0-beta.0", "vue-i18n": "^9.13.1", "vue-router": "^4.4.2", "vuex": "^4.1.0", "vxe-pc-ui": "4.5.22", "vxe-table": "4.13.2"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.1.2", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-vue": "^9.26.0", "husky": "^9.1.4", "less": "^4.2.0", "lint-staged": "^15.2.7", "pre-commit": "^1.2.2", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "sass-loader": "^16.0.0", "typescript": "^5.5.4", "vite": "^5.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vue-tsc": "^2.0.29"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "packageManager": "pnpm@10.3.0+sha512.ee592eda8815a8a293c206bb0917c4bb0ff274c50def7cbc17be05ec641fc2d1b02490ce660061356bd0d126a4d7eb2ec8830e6959fb8a447571c631d5a2442d"}